import json
import jsonref
from typing import Dict, Any, List
from pathlib import Path

def load_openapi_json(file_path: str) -> Dict[str, Any]:
    with open(file_path, 'r', encoding='utf-8') as f:
        return jsonref.loads(f.read())  # 自动解析 $ref

def get_constraints(prop: Dict[str, Any]) -> str:
    constraints = []

    def collect(p):
        if "format" in p:
            constraints.append(f"格式: {p['format']}")
        if "maxLength" in p:
            constraints.append(f"最大长度: {p['maxLength']}")
        if "minLength" in p:
            constraints.append(f"最小长度: {p['minLength']}")
        if "pattern" in p:
            constraints.append(f"正则: {p['pattern']}")
        if p.get("nullable") is True:
            constraints.append("可为 null")

    if "anyOf" in prop:
        for item in prop["anyOf"]:
            collect(item)
    else:
        collect(prop)

    return ", ".join(constraints)


def extract_field_table(schema: Dict[str, Any]) -> List[Dict[str, str]]:
    result = []
    properties = schema.get("properties", {})
    required = schema.get("required", [])

    for name, prop in properties.items():
        # ---------- 类型解析 ----------
        field_type = ""
        if "$ref" in prop:
            field_type = f"引用: {prop['$ref']}"
        elif "anyOf" in prop:
            types = []
            for item in prop["anyOf"]:
                if "$ref" in item:
                    types.append(f"引用: {item['$ref']}")
                else:
                    types.append(item.get("type", "object"))
            field_type = " | ".join(types)
        else:
            field_type = prop.get("type", "object")

        # ---------- 限制条件 ----------
        constraints = get_constraints(prop)

        # ---------- 中文释义：只取最外层 ----------
        description = prop.get("description", "")

        result.append({
            "字段名": name,
            "类型": field_type,
            "是否必填": "是" if name in required else "否",
            # "限制": constraints,
            "中文释义": description
        })

    return result
def extract_api_docs(openapi: Dict[str, Any]) -> str:
    output = []
    for path, methods in openapi.get("paths", {}).items():
        for method, meta in methods.items():
            method_upper = method.upper()
            summary = meta.get("summary", "")
            description = meta.get("description", "")
            auth_required = "是" if "security" in meta and meta["security"] else "否"

            output.append(f"## {summary or method_upper} {path}")
            output.append(f"- **接口地址**：`{path}`")
            output.append(f"- **请求方式**：{method_upper}")
            output.append(f"- **功能描述**：{description}")
            output.append(f"- **是否认证**：{auth_required}\n")

            # 请求体
            req_body = meta.get("requestBody", {})
            if req_body:
                output.append(f"### 🔹请求体（`application/json`）")
                schema = list(req_body['content'].values())[0]['schema']
                resolved_schema = jsonref.JsonRef.replace_refs(schema)
                table = extract_field_table(resolved_schema)
                output.append("| 字段名 | 类型 | 是否必填 | 中文释义 |")
                output.append("|--------|------|----------|------|")
                for row in table:
                    output.append(f"| {row['字段名']} | {row['类型']} | {row['是否必填']} | {row['中文释义']} |")
                output.append("")

            # 响应体（200）
            resp = meta.get("responses", {}).get("200", {})
            if "content" in resp:
                output.append("### 🔹响应体（200 OK）")
                resp_schema = list(resp["content"].values())[0]["schema"]
                resolved_resp_schema = jsonref.JsonRef.replace_refs(resp_schema)
                if resolved_resp_schema.get("type") == "object":
                    props = resolved_resp_schema.get("properties", {})
                    output.append("| 字段名 | 类型 | 中文释义 |")
                    output.append("|--------|------|----------|")
                    for key, val in props.items():
                        output.append(f"| {key} | {val.get('type', 'object')} | {val.get('description', '')} |")
                output.append("")

    return "\n".join(output)

# if __name__ == "__main__":
#     # 从网络获取OpenAPI文档
#     import requests
    
#     openapi_url = "http://**********:7999/openapi.json"
#     try:
#         response = requests.get(openapi_url)
#         response.raise_for_status()  # 如果请求失败则抛出异常
#         openapi_data = response.json()  # 直接解析JSON响应
#         print(f"✅ 成功从 {openapi_url} 获取OpenAPI文档")
#     except Exception as e:
#         print(f"❌ 从网络获取OpenAPI文档失败: {str(e)}")
#         exit(1)
#     output_path = "接口文档汇总.md"

#     openapi_data = load_openapi_json(openapi_path)
#     markdown = extract_api_docs(openapi_data)

#     with open(output_path, "w", encoding="utf-8") as f:
#         f.write(markdown)

#     print(f"✅ 文档已生成：{output_path}")
if __name__ == "__main__":
    import requests

    openapi_url = "http://localhost:7001/openapi.json"
    try:
        response = requests.get(openapi_url)
        response.raise_for_status()
        openapi_text = response.text  # 保留原始 JSON 字符串
        print(f"✅ 成功从 {openapi_url} 获取OpenAPI文档")
    except Exception as e:
        print(f"❌ 从网络获取OpenAPI文档失败: {str(e)}")
        exit(1)

    # 解析 $ref
    openapi_data = jsonref.loads(openapi_text, jsonschema=True)

    # 生成 Markdown 文档
    markdown = extract_api_docs(openapi_data)

    # 写入文件
    output_path = "接口文档汇总.md"
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(markdown)
    print(f"✅ 文档已生成：{output_path}")