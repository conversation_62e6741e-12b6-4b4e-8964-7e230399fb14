# React 组件开发规范

## 1. 组件定义规范

### 1.1 函数组件定义

```typescript
// ✅ 正确的组件定义
interface UserCardProps {
  user: UserInfo;
  onEdit?: (user: UserInfo) => void;
  onDelete?: (id: string) => void;
  className?: string;
}

const UserCard: React.FC<UserCardProps> = ({ 
  user, 
  onEdit, 
  onDelete, 
  className 
}) => {
  // 组件逻辑
  return (
    <div className={className}>
      {/* 组件内容 */}
    </div>
  );
};

export default UserCard;

// ❌ 错误的组件定义
const userCard = ({ user }) => { }  // 组件名应使用PascalCase
const UserCard = (props: any) => { } // 避免使用any类型
function UserCard() { }              // 推荐使用箭头函数
```

### 1.2 组件导入导出规范

```typescript
// ✅ 正确的导入导出
import React from 'react';
import { Button, Card, message } from 'antd';
import { UserInfo } from '@/types/UserInfo';
import { userApi } from '@/utils/api';
import './UserCard.css';

// 默认导出组件
export default UserCard;

// 同时导出类型定义（可选）
export type { UserCardProps };

// ❌ 错误的导入导出
import * as React from 'react';     // 不推荐使用namespace导入
export { UserCard };                // 组件应使用默认导出
```

## 2. Props 接口规范

### 2.1 基础Props接口

```typescript
// 基础组件Props
interface BaseComponentProps {
  className?: string;                // 自定义样式类名
  style?: React.CSSProperties;       // 内联样式
  children?: React.ReactNode;        // 子元素
  id?: string;                       // DOM元素ID
  'data-testid'?: string;            // 测试ID
}

// 扩展基础Props
interface UserCardProps extends BaseComponentProps {
  // 必填属性
  user: UserInfo;                    // 用户信息
  
  // 可选属性
  showActions?: boolean;             // 是否显示操作按钮
  variant?: 'default' | 'compact';   // 展示变体
  
  // 回调函数
  onEdit?: (user: UserInfo) => void;       // 编辑回调
  onDelete?: (id: string) => void;         // 删除回调
  onStatusChange?: (id: string, status: string) => void; // 状态变更回调
}
```

### 2.2 事件处理Props

```typescript
// 表单相关Props
interface UserFormProps {
  initialValues?: Partial<UserInfo>;  // 初始值
  onSubmit: (values: CreateUserParams) => Promise<void>; // 提交回调
  onCancel?: () => void;              // 取消回调
  onFieldChange?: (field: string, value: any) => void; // 字段变更回调
  
  // 状态控制
  loading?: boolean;                  // 加载状态
  disabled?: boolean;                 // 禁用状态
  readOnly?: boolean;                 // 只读状态
}

// 抽屉/模态框Props
interface DrawerProps {
  visible: boolean;                   // 显示状态（必填）
  onClose: () => void;                // 关闭回调（必填）
  title?: string;                     // 标题
  width?: number | string;            // 宽度
  placement?: 'left' | 'right' | 'top' | 'bottom'; // 位置
  
  // 内容相关
  children?: React.ReactNode;         // 子元素
  footer?: React.ReactNode;           // 自定义底部
  destroyOnClose?: boolean;           // 关闭时销毁内容
}
```

## 3. 状态管理规范

### 3.1 useState使用规范

```typescript
const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
  // ✅ 正确的状态定义
  const [loading, setLoading] = useState<boolean>(false);
  const [editing, setEditing] = useState<boolean>(false);
  const [formData, setFormData] = useState<Partial<UserInfo>>({});
  
  // ✅ 状态初始化（基于props）
  const [userInfo, setUserInfo] = useState<UserInfo>(user);
  
  // ✅ 复杂状态使用useReducer
  const [state, dispatch] = useReducer(userReducer, {
    loading: false,
    error: null,
    data: null,
  });
  
  // ❌ 错误的状态定义
  const [loading, setLoading] = useState();        // 缺少类型注解
  const [user, setUser] = useState(null);          // 应避免null初始值
  const [data, setData] = useState({} as any);     // 避免使用any
};
```

### 3.2 useEffect使用规范

```typescript
const UserCard: React.FC<UserCardProps> = ({ userId }) => {
  const [user, setUser] = useState<UserInfo | null>(null);
  
  // ✅ 正确的副作用处理
  useEffect(() => {
    if (!userId) return;
    
    let cancelled = false;
    
    const fetchUser = async () => {
      try {
        setLoading(true);
        const userData = await userApi.getUserById(userId);
        if (!cancelled) {
          setUser(userData);
        }
      } catch (error) {
        if (!cancelled) {
          console.error('获取用户信息失败:', error);
          message.error('获取用户信息失败');
        }
      } finally {
        if (!cancelled) {
          setLoading(false);
        }
      }
    };
    
    fetchUser();
    
    // 清理函数
    return () => {
      cancelled = true;
    };
  }, [userId]); // 明确依赖项
  
  // ✅ 组件销毁时的清理
  useEffect(() => {
    return () => {
      // 清理定时器、取消请求等
      console.log('UserCard组件即将销毁');
    };
  }, []);
};
```

### 3.3 自定义Hook规范

```typescript
// ✅ 自定义Hook定义
interface UseUserReturn {
  user: UserInfo | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  updateUser: (data: Partial<UserInfo>) => Promise<void>;
}

interface UseUserParams {
  userId: string;
  autoFetch?: boolean;
}

export const useUser = ({ userId, autoFetch = true }: UseUserParams): UseUserReturn => {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const fetchUser = useCallback(async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      setError(null);
      const userData = await userApi.getUserById(userId);
      setUser(userData);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取用户信息失败';
      setError(errorMsg);
      console.error('获取用户信息失败:', err);
    } finally {
      setLoading(false);
    }
  }, [userId]);
  
  const updateUser = useCallback(async (data: Partial<UserInfo>) => {
    if (!userId) return;
    
    try {
      setLoading(true);
      const updatedUser = await userApi.updateUser({ ...data, id: userId });
      setUser(updatedUser);
      message.success('用户信息更新成功');
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '更新用户信息失败';
      setError(errorMsg);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [userId]);
  
  useEffect(() => {
    if (autoFetch) {
      fetchUser();
    }
  }, [fetchUser, autoFetch]);
  
  return {
    user,
    loading,
    error,
    refresh: fetchUser,
    updateUser,
  };
};
```

## 4. 事件处理规范

### 4.1 事件处理函数命名

```typescript
const UserCard: React.FC<UserCardProps> = ({ user, onEdit, onDelete }) => {
  // ✅ 正确的事件处理函数命名
  const handleEditClick = useCallback(() => {
    onEdit?.(user);
  }, [onEdit, user]);
  
  const handleDeleteClick = useCallback(() => {
    onDelete?.(user.id);
  }, [onDelete, user.id]);
  
  const handleFormSubmit = useCallback(async (values: CreateUserParams) => {
    try {
      await userApi.createUser(values);
      message.success('用户创建成功');
    } catch (error) {
      console.error('创建用户失败:', error);
    }
  }, []);
  
  // ❌ 错误的命名
  const edit = () => { };           // 应添加handle前缀
  const onClick = () => { };        // 命名不够具体
  const btnClick = () => { };       // 使用缩写
};
```

### 4.2 表单处理规范

```typescript
const UserForm: React.FC<UserFormProps> = ({ initialValues, onSubmit, onCancel }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  
  // 表单提交处理
  const handleSubmit = useCallback(async (values: CreateUserParams) => {
    try {
      setLoading(true);
      await onSubmit(values);
      form.resetFields();
    } catch (error) {
      console.error('表单提交失败:', error);
    } finally {
      setLoading(false);
    }
  }, [onSubmit, form]);
  
  // 表单重置处理
  const handleReset = useCallback(() => {
    form.resetFields();
    onCancel?.();
  }, [form, onCancel]);
  
  // 字段值变更处理
  const handleFieldChange = useCallback((changedFields: any, allFields: any) => {
    console.log('表单字段变更:', changedFields);
  }, []);
  
  return (
    <Form
      form={form}
      initialValues={initialValues}
      onFinish={handleSubmit}
      onFieldsChange={handleFieldChange}
      layout="vertical"
    >
      {/* 表单项 */}
    </Form>
  );
};
```

## 5. 样式处理规范

### 5.1 CSS Modules使用

```typescript
// UserCard.module.css
.card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  margin: 8px;
}

.card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

// UserCard.tsx
import styles from './UserCard.module.css';

const UserCard: React.FC<UserCardProps> = ({ user, className }) => {
  return (
    <div className={`${styles.card} ${className || ''}`}>
      <div>{user.username}</div>
      <div className={styles.actions}>
        <Button>编辑</Button>
        <Button danger>删除</Button>
      </div>
    </div>
  );
};
```

### 5.2 内联样式规范

```typescript
const UserCard: React.FC<UserCardProps> = ({ user }) => {
  // ✅ 动态样式计算
  const cardStyle: React.CSSProperties = useMemo(() => ({
    backgroundColor: user.status === 'active' ? '#f6ffed' : '#fff2e8',
    borderColor: user.status === 'active' ? '#b7eb8f' : '#ffbb96',
    borderWidth: 1,
    borderStyle: 'solid',
    borderRadius: 8,
    padding: 16,
  }), [user.status]);
  
  return (
    <div style={cardStyle}>
      {/* 组件内容 */}
    </div>
  );
};

// ❌ 避免直接在JSX中使用复杂内联样式
<div style={{
  backgroundColor: user.status === 'active' ? '#f6ffed' : '#fff2e8',
  borderColor: user.status === 'active' ? '#b7eb8f' : '#ffbb96',
  // ...更多样式
}}>
```

## 6. 条件渲染和列表渲染

### 6.1 条件渲染规范

```typescript
const UserCard: React.FC<UserCardProps> = ({ user, showActions }) => {
  return (
    <div>
      <h3>{user.username}</h3>
      
      {/* ✅ 简单条件渲染 */}
      {user.email && <p>邮箱: {user.email}</p>}
      
      {/* ✅ 复杂条件渲染 */}
      {user.status === 'active' ? (
        <Badge status="success" text="激活" />
      ) : user.status === 'inactive' ? (
        <Badge status="default" text="未激活" />
      ) : (
        <Badge status="processing" text="审核中" />
      )}
      
      {/* ✅ 条件渲染组件 */}
      {showActions && (
        <div className="actions">
          <Button onClick={handleEdit}>编辑</Button>
          <Button danger onClick={handleDelete}>删除</Button>
        </div>
      )}
    </div>
  );
};
```

### 6.2 列表渲染规范

```typescript
const UserList: React.FC<UserListProps> = ({ users }) => {
  return (
    <div>
      {/* ✅ 正确的列表渲染 */}
      {users.length > 0 ? (
        users.map(user => (
          <UserCard
            key={user.id}  // 使用稳定的唯一key
            user={user}
            onEdit={handleEditUser}
            onDelete={handleDeleteUser}
          />
        ))
      ) : (
        <Empty description="暂无用户数据" />
      )}
      
      {/* ❌ 错误的key使用 */}
      {users.map((user, index) => (
        <UserCard key={index} user={user} />  // 不要使用index作为key
      ))}
    </div>
  );
};
```

## 7. 错误处理和加载状态

### 7.1 错误边界处理

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('组件错误:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: 20, textAlign: 'center' }}>
          <h3>出现了错误</h3>
          <p>页面加载失败，请刷新页面重试</p>
          <Button onClick={() => window.location.reload()}>
            刷新页面
          </Button>
        </div>
      );
    }
    
    return this.props.children;
  }
}
```

### 7.2 加载状态处理

```typescript
const UserList: React.FC<UserListProps> = () => {
  const { users, loading, error, refresh } = useUsers();
  
  // 加载状态
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: 40 }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>正在加载用户列表...</p>
      </div>
    );
  }
  
  // 错误状态
  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: 40 }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={refresh}>
              重新加载
            </Button>
          }
        />
      </div>
    );
  }
  
  // 正常状态
  return (
    <div>
      {users.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  );
};
```

## 8. 性能优化规范

### 8.1 React.memo使用

```typescript
// ✅ 使用React.memo优化纯组件
const UserCard = React.memo<UserCardProps>(({ user, onEdit, onDelete }) => {
  return (
    <Card>
      <h3>{user.username}</h3>
      <p>{user.email}</p>
      <Button onClick={() => onEdit?.(user)}>编辑</Button>
      <Button onClick={() => onDelete?.(user.id)}>删除</Button>
    </Card>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数（可选）
  return (
    prevProps.user.id === nextProps.user.id &&
    prevProps.user.username === nextProps.user.username &&
    prevProps.user.email === nextProps.user.email
  );
});
```

### 8.2 useCallback和useMemo使用

```typescript
const UserList: React.FC<UserListProps> = ({ users, searchKeyword }) => {
  // ✅ 缓存计算结果
  const filteredUsers = useMemo(() => {
    if (!searchKeyword) return users;
    return users.filter(user => 
      user.username.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      user.email.toLowerCase().includes(searchKeyword.toLowerCase())
    );
  }, [users, searchKeyword]);
  
  // ✅ 缓存回调函数
  const handleEditUser = useCallback((user: UserInfo) => {
    console.log('编辑用户:', user);
    // 编辑逻辑
  }, []);
  
  const handleDeleteUser = useCallback((userId: string) => {
    console.log('删除用户:', userId);
    // 删除逻辑
  }, []);
  
  return (
    <div>
      {filteredUsers.map(user => (
        <UserCard
          key={user.id}
          user={user}
          onEdit={handleEditUser}
          onDelete={handleDeleteUser}
        />
      ))}
    </div>
  );
};
```

## 9. 质量保障要求

### 9.1 组件开发检查清单

- [ ] 组件使用TypeScript并有完整的类型定义
- [ ] Props接口继承BaseComponentProps
- [ ] 所有状态都有明确的类型注解
- [ ] 事件处理函数使用useCallback优化
- [ ] 副作用使用useEffect正确处理
- [ ] 组件有适当的错误处理机制
- [ ] 加载状态有友好的用户反馈
- [ ] 样式使用CSS Modules或统一的样式方案

### 9.2 性能优化检查

- [ ] 重组件使用React.memo优化
- [ ] 复杂计算使用useMemo缓存
- [ ] 回调函数使用useCallback缓存
- [ ] 列表渲染使用稳定的key值
- [ ] 避免在render中创建对象和函数

### 9.3 代码质量检查

- [ ] 组件职责单一，功能明确
- [ ] 函数和变量命名语义明确
- [ ] 组件有详细的中文注释
- [ ] 复杂逻辑抽取为自定义Hook
- [ ] 组件可复用性和可扩展性良好
description:
globs:
alwaysApply: false
---
