# 通用接口规范标准

## 1. TypeScript 接口定义规范

### 1.1 接口命名规范

```typescript
// ✅ 正确的接口命名
interface UserInfo {          // 数据模型接口
  id: string;
  username: string;
  email: string;
}

interface UserInfoProps {     // 组件Props接口，添加Props后缀
  userInfo: UserInfo;
  onEdit: (user: UserInfo) => void;
}

interface ApiResponse<T> {    // 泛型接口，支持多种数据类型
  code: number;
  data: T;
  error: string;
  success: boolean;
}

// ❌ 错误的接口命名
interface userinfo { }       // 应使用PascalCase
interface IUserInfo { }      // 不使用I前缀
interface User_Info { }      // 不使用下划线
```

### 1.2 接口属性定义规范

```typescript
interface UserInfo {
  // 必填属性
  id: string;                 // 用户ID
  username: string;           // 用户名
  
  // 可选属性
  realname?: string;          // 真实姓名（可选）
  mobile?: string;            // 手机号（可选）
  
  // 只读属性
  readonly created_at: string; // 创建时间（只读）
  readonly updated_at: string; // 更新时间（只读）
  
  // 联合类型
  status: 'active' | 'inactive' | 'pending'; // 用户状态
  
  // 嵌套对象
  organization?: {            // 所属组织（可选）
    id: string;
    name: string;
  };
  
  // 数组类型
  roles: Role[];              // 用户角色列表
  
  // 函数类型
  onStatusChange?: (status: string) => void; // 状态变更回调（可选）
}
```

### 1.3 接口继承和扩展

```typescript
// 基础接口
interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

// 扩展接口
interface UserInfo extends BaseEntity {
  username: string;
  email: string;
  // 自动继承 id, created_at, updated_at, is_deleted
}

// 接口合并
interface UserInfo {
  // 基础属性
  username: string;
  email: string;
}

interface UserInfo {
  // 扩展属性（与上面的接口合并）
  profile?: UserProfile;
  settings?: UserSettings;
}
```

## 2. API 接口规范

### 2.1 API 响应格式标准

```typescript
// 统一API响应格式
interface ApiResponse<T = any> {
  code: number;               // 状态码：0-成功，其他-错误
  data: T;                    // 业务数据
  error: string;              // 错误信息
  success: boolean;           // 是否成功
  timestamp?: number;         // 响应时间戳（可选）
  requestId?: string;         // 请求ID（可选）
}

// 分页响应格式
interface PaginatedResponse<T> {
  items: T[];                 // 数据列表
  total: number;              // 总数量
  page: number;               // 当前页码
  pageSize: number;           // 每页大小
  hasNext: boolean;           // 是否有下一页
}

// API响应示例
interface GetUsersResponse {
  users: PaginatedResponse<UserInfo>;
}
```

### 2.2 API 请求参数规范

```typescript
// 查询参数接口
interface GetUsersParams {
  page?: number;              // 页码（可选，默认1）
  pageSize?: number;          // 每页大小（可选，默认20）
  keyword?: string;           // 搜索关键词（可选）
  status?: 'active' | 'inactive'; // 用户状态（可选）
  organizationId?: string;    // 组织ID（可选）
}

// 创建用户参数接口
interface CreateUserParams {
  username: string;           // 用户名（必填）
  email: string;              // 邮箱（必填）
  password: string;           // 密码（必填）
  realname?: string;          // 真实姓名（可选）
  mobile?: string;            // 手机号（可选）
  roleIds: string[];          // 角色ID列表（必填）
}

// 更新用户参数接口
interface UpdateUserParams {
  id: string;                 // 用户ID（必填）
  username?: string;          // 用户名（可选）
  email?: string;             // 邮箱（可选）
  realname?: string;          // 真实姓名（可选）
  mobile?: string;            // 手机号（可选）
  status?: 'active' | 'inactive'; // 用户状态（可选）
}
```

### 2.3 API 方法定义规范

```typescript
// API方法接口定义
interface UserApi {
  // 获取用户列表
  getUsers: (params?: GetUsersParams) => Promise<GetUsersResponse>;
  
  // 获取单个用户
  getUserById: (id: string) => Promise<UserInfo>;
  
  // 创建用户
  createUser: (params: CreateUserParams) => Promise<UserInfo>;
  
  // 更新用户
  updateUser: (params: UpdateUserParams) => Promise<UserInfo>;
  
  // 删除用户
  deleteUser: (id: string) => Promise<void>;
  
  // 批量删除用户
  batchDeleteUsers: (ids: string[]) => Promise<void>;
}

// API实现示例
export const userApi: UserApi = {
  getUsers: (params) => api.get('/users', { params }),
  getUserById: (id) => api.get(`/users/${id}`),
  createUser: (params) => api.post('/users', params),
  updateUser: (params) => api.put(`/users/${params.id}`, params),
  deleteUser: (id) => api.delete(`/users/${id}`),
  batchDeleteUsers: (ids) => api.post('/users/batch-delete', { ids }),
};
```

## 3. React 组件接口规范

### 3.1 组件 Props 接口

```typescript
// 基础组件Props
interface BaseComponentProps {
  className?: string;         // 自定义样式类名
  style?: React.CSSProperties; // 内联样式
  children?: React.ReactNode; // 子元素
}

// 具体组件Props
interface UserCardProps extends BaseComponentProps {
  user: UserInfo;             // 用户信息（必填）
  showActions?: boolean;      // 是否显示操作按钮（可选）
  onEdit?: (user: UserInfo) => void; // 编辑回调（可选）
  onDelete?: (id: string) => void;   // 删除回调（可选）
}

// 表单组件Props
interface UserFormProps {
  initialValues?: Partial<UserInfo>; // 初始值（可选）
  onSubmit: (values: CreateUserParams) => Promise<void>; // 提交回调（必填）
  onCancel?: () => void;      // 取消回调（可选）
  loading?: boolean;          // 加载状态（可选）
}

// 抽屉组件Props
interface UserDrawerProps {
  visible: boolean;           // 是否显示（必填）
  onClose: () => void;        // 关闭回调（必填）
  userId?: string;            // 用户ID（可选，编辑时传入）
  mode?: 'create' | 'edit' | 'view'; // 模式（可选）
}
```

### 3.2 Hook 接口规范

```typescript
// Hook返回值接口
interface UseUsersReturn {
  users: UserInfo[];          // 用户列表
  loading: boolean;           // 加载状态
  error: string | null;       // 错误信息
  total: number;              // 总数量
  refresh: () => Promise<void>; // 刷新方法
  loadMore: () => Promise<void>; // 加载更多方法
}

// Hook参数接口
interface UseUsersParams {
  autoFetch?: boolean;        // 是否自动获取（可选，默认true）
  initialParams?: GetUsersParams; // 初始查询参数（可选）
}

// Hook定义
export function useUsers(params?: UseUsersParams): UseUsersReturn;
```

### 3.3 Context 接口规范

```typescript
// Context状态接口
interface AuthContextState {
  isAuthenticated: boolean;   // 是否已认证
  userInfo: UserInfo | null;  // 用户信息
  token: string | null;       // 认证令牌
  permissions: string[];      // 权限列表
}

// Context方法接口
interface AuthContextActions {
  login: (credentials: LoginParams) => Promise<void>; // 登录方法
  logout: () => Promise<void>; // 登出方法
  updateUserInfo: () => Promise<void>; // 更新用户信息
  checkPermission: (permission: string) => boolean; // 检查权限
}

// Context完整接口
interface AuthContextValue extends AuthContextState, AuthContextActions {}
```

## 4. 错误处理接口规范

### 4.1 错误类型定义

```typescript
// 基础错误接口
interface BaseError {
  code: string;               // 错误代码
  message: string;            // 错误信息
  timestamp: number;          // 错误时间戳
}

// API错误接口
interface ApiError extends BaseError {
  statusCode: number;         // HTTP状态码
  path: string;               // 请求路径
  method: string;             // 请求方法
  requestId?: string;         // 请求ID
}

// 验证错误接口
interface ValidationError extends BaseError {
  field: string;              // 错误字段
  value: any;                 // 错误值
  constraint: string;         // 约束条件
}

// 业务错误接口
interface BusinessError extends BaseError {
  errorType: 'validation' | 'authorization' | 'not_found' | 'conflict'; // 错误类型
  details?: Record<string, any>; // 错误详情
}
```

## 5. 实用工具类型

### 5.1 常用工具类型

```typescript
// 使ID字段可选的工具类型
type CreateType<T> = Omit<T, 'id' | 'created_at' | 'updated_at'>;

// 使所有字段可选的更新类型
type UpdateType<T> = Partial<T> & { id: string };

// 只选择特定字段的类型
type UserSummary = Pick<UserInfo, 'id' | 'username' | 'email'>;

// 排除特定字段的类型
type UserPublic = Omit<UserInfo, 'password' | 'token'>;

// 响应状态枚举
enum ResponseStatus {
  SUCCESS = 0,
  VALIDATION_ERROR = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_ERROR = 500,
}
```

## 6. 接口文档注释规范

```typescript
/**
 * 用户信息接口
 * @description 定义用户的基本信息和扩展属性
 */
interface UserInfo {
  /** 用户唯一标识 */
  id: string;
  
  /** 用户名，用于登录和显示 */
  username: string;
  
  /** 
   * 用户邮箱
   * @example "<EMAIL>"
   */
  email: string;
  
  /**
   * 用户状态
   * @default "active"
   */
  status: 'active' | 'inactive' | 'pending';
  
  /**
   * 用户角色列表
   * @description 用户所拥有的所有角色
   */
  roles: Role[];
}

/**
 * 创建用户的API方法
 * @param params 创建用户的参数
 * @returns Promise<UserInfo> 创建成功的用户信息
 * @throws {ValidationError} 当参数验证失败时
 * @throws {ApiError} 当API请求失败时
 * @example
 * ```typescript
 * const user = await userApi.createUser({
 *   username: 'john_doe',
 *   email: '<EMAIL>',
 *   password: 'secure_password',
 *   roleIds: ['role1', 'role2']
 * });
 * ```
 */
createUser: (params: CreateUserParams) => Promise<UserInfo>;
```

## 7. 质量保障要求

### 7.1 接口定义检查清单

- [ ] 所有属性都有明确的类型注解
- [ ] 可选属性使用`?:`标记
- [ ] 只读属性使用`readonly`修饰
- [ ] 联合类型使用字面量类型而非string
- [ ] 复杂对象使用嵌套接口而非内联定义
- [ ] 所有接口都有JSDoc注释
- [ ] 泛型接口有合理的默认类型参数

### 7.2 API接口规范检查

- [ ] 统一使用ApiResponse格式
- [ ] 分页数据使用PaginatedResponse格式
- [ ] 所有API方法都有完整的类型定义
- [ ] 错误处理有明确的错误类型
- [ ] 参数验证有相应的接口约束

### 7.3 组件接口规范检查

- [ ] Props接口继承BaseComponentProps
- [ ] 回调函数有明确的参数和返回类型
- [ ] 可选props有合理的默认值
- [ ] 复杂组件支持受控和非受控模式
- [ ] Hook返回值有完整的类型定义
description:
globs:
alwaysApply: false
---
