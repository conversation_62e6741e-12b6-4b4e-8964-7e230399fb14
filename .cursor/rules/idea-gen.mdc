---
description: 
globs: 
alwaysApply: true
---
description: 前端项目规则 - React + Vite 架构，确保生成内容符合项目结构、UI风格、组件职责与API交互规范
files: ["src/**/*.tsx", "src/**/*.ts", "vite.config.ts", "README.md", "directories.md"]

rules:
  - 所有生成的重要组件、Hook、模块文件，必须写清晰的 **中文注释**，解释其功能、输入输出、关键逻辑。
  - 所有生成的组件、页面、Hook 文件，必须放入对应目录结构中，并同步更新 `directories.md` 文档。
  - 若添加新的页面、模块或 API 接口，请在 `README.md` 中说明新增功能、入口路径、用途说明。
  - 所有路由应统一在 `src/routes.tsx` 中管理，并遵循已有的嵌套路由风格。
  - 页面组件必须存放于 `src/pages/`，抽象组件放于 `src/components/`，保持文件职责清晰。

  # React + TypeScript 组件规范
  - 所有组件使用 **函数组件**（Function Component）方式，必须写明 `Props` 类型。
  - 每个组件文件应导出一个默认组件，并命名与文件名保持一致（如 `AppHeader.tsx` 中导出 `AppHeader`）。
  - 所有组件必须支持 props 的可扩展性，禁止硬编码常量。
  - 样式采用 `src/styles/global.css` 中全局样式，或 Ant Design 样式系统。

  # API 与业务逻辑规范
  - 所有接口请求必须通过 `src/utils/api.ts` 的封装模块，禁止直接使用 `axios`。
  - API 分组组织应使用对象命名方式，如 `reportApi.getReportList()`，并按业务分文件或分组注释。
  - 接口调用逻辑统一放入 `src/hooks` 或组件 `useEffect/useCallback` 中，禁止在组件顶层直接调用异步函数。

  # 环境变量与配置
  - 所有环境变量应使用 `VITE_` 前缀，并在 `vite.config.ts` 中正确引用。

  # 风格与 UI 规范
  - 所有页面必须具备响应式布局，支持主流移动端与桌面设备显示。
  - 使用 Ant Design 时，应保持风格统一（按钮、输入框、卡片等组件不混用其他 UI 框架）。
  - 所有组件应避免出现视觉跳变、加载抖动，推荐使用 `LoadingDots.tsx` 处理 loading 状态。

  # 质量保障
  - 所有新增组件、模块尽量经过 TypeScript 类型检查，避免 `any` 或未声明返回类型。
  - 函数命名需语义明确，禁止缩写、无含义命名（如 `handleBtnClickX`）。
  - 若新增功能逻辑较复杂，应附带组件调用图或注释说明调用关系。
  - 日志需要尽可能的多打印

  # 组件封装规则
  - 业务级别的页面写到./src/pages目录，用大驼峰文件名
  - 业务级别的组件写到./src/pages/xx页面/components/，里面可以写一些业务逻辑和api请求
  - 通用级别的纯组件写到./src/components/里面，可以复用到其他页面，里面一般不要写复杂业务逻辑和接口请求
  - 单独的css文件配到组件或者页面的同级目录，名字跟组件一致，并且要保证引用正确

  
