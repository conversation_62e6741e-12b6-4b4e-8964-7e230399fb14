# API拦截器规则与标准

## 1. API响应格式规范

所有后端API响应必须遵循统一的格式规范，便于前端统一处理：

```typescript
interface ApiResponse<T = any> {
    code: number;       // 状态码，0表示成功，其他表示错误
    data: T;            // 业务数据，成功时返回
    error: string;      // 错误信息，失败时返回
    success: boolean;   // 是否成功，true/false
}
```

## 2. axios实例配置规范

```typescript
// 创建axios实例
const api = axios.create({
  baseURL: window.AppConfig.baseURL,  // 从全局配置获取基础URL
  timeout: 6000000,                   // 100分钟超时（适应大模型场景）
});
```

## 3. 请求拦截器规则

所有通过axios发出的请求必须经过以下拦截处理：

- **授权令牌注入**：所有需要认证的请求自动注入Bearer Token
- **请求头标准化**：根据不同请求类型设置合适的Content-Type
- **错误日志**：请求错误时进行详细日志记录

```typescript
// 请求拦截器实现
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 从AuthManager获取并注入Token
    const token = AuthManager.getToken();
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error: any) => {
    // 统一处理请求错误
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);
```

## 4. 响应拦截器规则

所有从后端接收的响应必须经过以下处理：

- **数据标准化**：将所有响应转换为标准ApiResponse格式
- **错误码统一处理**：根据不同错误码执行相应操作
- **认证失效处理**：401状态码处理，自动跳转登录页
- **业务错误处理**：显示友好错误信息，并拒绝Promise
- **网络错误处理**：连接超时、网络异常等情况处理

```typescript
// 响应拦截器实现
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 统一处理响应格式
    const res = response.data as ApiResponse;
    
    // 1. 处理认证失效 (401)
    if (res.code === 401) {
      message.open({
        content: res.error || '登录无效或已过期，请重新登录',
        type: 'error'
      });
      // 触发自定义认证过期事件
      window.dispatchEvent(new CustomEvent('auth-expired'));
      // 清除登录信息
      AuthManager.clear();
      // 跳转到登录页面
      window.location.href = `${getBasePath()}/login`;
      return Promise.reject(new Error(res.error || '登录无效或已过期'));
    }
    
    // 2. 标准格式转换（处理非标准响应）
    if (res.code === undefined) {
      return {
        code: response.status,
        data: response.data,
        success: response.status >= 200 && response.status < 300,
      };
    }

    // 3. 处理业务错误
    if (!res.success || res.code !== 0) {
      console.log('业务错误信息:', res.error); 
      message.open({
        content: res.error || '操作失败',
        type: 'error'
      });
      return Promise.reject(new Error(res.error || '操作失败'));
    }

    // 4. 返回成功数据
    return res.data;
  },
  (error: any) => {
    // 处理HTTP错误
    const status = error.response?.status;
    let errorMsg = '未知错误，请稍后重试';
    
    // 根据HTTP状态码提示错误信息
    switch (status) {
      case 401:
        errorMsg = '登录无效或已过期，请重新登录';
        // 触发认证过期事件
        window.dispatchEvent(new CustomEvent('auth-expired'));
        AuthManager.clear();
        window.location.href = `${getBasePath()}/login`;
        break;
      case 400:
        errorMsg = '请求参数错误，请检查输入';
        break;
      case 403:
        errorMsg = '没有权限执行此操作';
        break;
      case 404:
        errorMsg = '请求的资源不存在';
        break;
      case 500:
        errorMsg = '服务器内部错误，请联系管理员';
        break;
      case 502:
        errorMsg = '网关错误，请稍后重试';
        break;
      case 503:
        errorMsg = '服务暂时不可用，请稍后重试';
        break;
      case 504:
        errorMsg = '网关超时，请稍后重试';
        break;
    }
    
    // 尝试从响应中获取更详细的错误信息
    const serverErrorMsg = error.response?.data?.error;
    if (serverErrorMsg) {
      errorMsg = serverErrorMsg;
    }
    
    // 构造标准错误响应格式
    const errorResponse: ApiResponse = {
      code: status || 500,
      data: null,
      error: errorMsg,
      success: false
    };
    
    // 记录详细错误日志
    console.error('API请求错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status,
      errorMsg,
      error
    });
    
    // 显示错误消息
    message.open({
      content: errorMsg,
      type: 'error'
    });
    
    return Promise.reject(new Error(errorMsg));
  }
);
```

## 5. API请求分组管理

所有API请求应按业务域进行分组管理，每个分组独立维护在单独的文件中：

- **api.ts**: 基础API配置和通用接口
- **api_chat.ts**: 聊天相关API - 消息发送、会话管理
- **api_homework.ts**: 作业相关API - 作业创建、查询、提交
- **api_report.ts**: 报告相关API - 创建、查询、删除报告
- **api_report_config.ts**: 报告配置相关API - 配置管理
- **dictionary_api.ts**: 字典数据API - 下拉选项、枚举值

每个API定义必须包含：
- 清晰的方法命名
- 完整的TypeScript类型注解（请求参数和响应类型）
- 中文注释说明用途

示例：
```typescript
// 用户相关API
export const userApi = {
  // 获取用户信息
  getUserInfo: (): Promise<UserInfo> =>
    api.get('/user/info'),

  // 更新用户信息
  updateUserInfo: (data: Partial<UserInfo>): Promise<UserInfo> =>
    api.put('/user/info', data),
};

// 组织相关API
export const organizationApi = {
  // 获取组织列表
  getOrganizations: (params: BasePageModel): Promise<{ organizations: Organization[], total: number }> =>
    api.get('/organizations', { params }),

  // 创建组织
  createOrganization: (data: CreateOrganizationForm): Promise<Organization> =>
    api.post('/organizations', data),
};
```

## 6. 文件下载处理规范

对于文件下载场景，需要特殊处理响应类型和错误处理：

```typescript
/**
 * 通用文件下载辅助函数
 * @param url 下载请求的URL
 * @param defaultFilename 默认文件名
 * @param acceptHeader 请求头中的Accept类型
 * @param blobType Blob对象的类型
 */
export const downloadFile = async (
  url: string, 
  defaultFilename: string, 
  acceptHeader: string, 
  blobType: string
) => {
  try {
    const response = await axios.get(url, {
      responseType: 'blob',
      headers: {
        Authorization: `Bearer ${AuthManager.getToken()}`,
        'Accept': acceptHeader,
      }
    });

    if (response.status !== 200) {
      throw new Error(`下载失败: ${response.status}`);
    }

    const blob = new Blob([response.data], {
      type: response.headers['content-type'] || blobType
    });

    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', defaultFilename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('下载失败:', error);
    throw error;
  }
};
```

## 7. 环境配置规范

### 7.1 运行时配置

```typescript
// 公共基础路径获取
export const getBasePath = () => {
  return import.meta.env.BASE_URL;
};

// 全局配置对象（通过window.AppConfig获取）
interface AppConfig {
  baseURL: string;          // API基础URL
  version: string;          // 应用版本
  environment: string;      // 运行环境
}

// 配置文件位置：public/config.js
window.AppConfig = {
  baseURL: 'http://localhost:8000',
  version: '1.0.0',
  environment: 'development'
};
```

### 7.2 环境变量配置

```typescript
// vite环境变量（.env文件）
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=College Agent Frontend
VITE_APP_VERSION=1.0.0

// 在代码中使用
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const appTitle = import.meta.env.VITE_APP_TITLE;
```

## 8. 错误处理与日志规范

### 8.1 日志记录标准

- **请求日志**：记录所有API请求的URL、方法、参数
- **响应日志**：记录响应状态、数据、耗时
- **错误日志**：记录详细的错误信息，包括堆栈跟踪
- **业务日志**：记录关键业务操作和状态变更

### 8.2 错误处理策略

- **网络错误**：显示通用网络错误提示，支持重试
- **认证错误**：自动清除认证信息，跳转登录页
- **权限错误**：显示权限不足提示，阻止操作
- **业务错误**：显示具体业务错误信息，指导用户操作
- **系统错误**：显示系统维护提示，记录错误日志

### 8.3 自定义事件规范

```typescript
// 认证过期事件
window.dispatchEvent(new CustomEvent('auth-expired'));

// 网络状态变更事件
window.dispatchEvent(new CustomEvent('network-status-change', {
  detail: { online: navigator.onLine }
}));

// 事件监听示例
window.addEventListener('auth-expired', () => {
  // 处理认证过期逻辑
  console.log('用户认证已过期，即将跳转登录页');
});
```

## 9. 质量保障要求

### 9.1 API设计检查清单

- [ ] 所有API响应遵循统一格式规范
- [ ] 请求和响应都有完整的TypeScript类型定义
- [ ] 错误处理覆盖所有可能的HTTP状态码
- [ ] 认证失效场景有完整的处理流程
- [ ] 文件下载有专门的处理逻辑
- [ ] 所有API方法都有中文注释说明

### 9.2 拦截器功能检查

- [ ] 请求拦截器正确注入认证Token
- [ ] 响应拦截器正确处理各种错误状态
- [ ] 错误消息对用户友好且具有指导性
- [ ] 日志记录详细且便于调试
- [ ] 超时设置合理且适应业务场景

### 9.3 配置管理检查

- [ ] 环境变量使用VITE_前缀
- [ ] 运行时配置通过window.AppConfig获取
- [ ] 基础URL配置灵活且可切换
 - [ ] 配置文件结构清晰且易于维护