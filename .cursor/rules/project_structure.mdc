# College Agent Frontend 项目结构规范

## 1. 项目目录结构

```
college-agent-frontend/
├── README.md                  # 项目说明文档
├── directories.md             # 目录结构文档
├── index.html                 # HTML入口文件
├── package-lock.json          # 包依赖锁定文件
├── package.json               # 项目配置和依赖管理
├── .env                       # 环境变量配置
├── vite.config.ts             # Vite构建配置
├── tsconfig.json              # TypeScript配置
├── tsconfig.node.json         # Node环境TypeScript配置
├── Dockerfile                 # Docker构建文件
├── nginx.conf                 # Nginx配置文件
├── build.sh                   # 构建脚本
├── wrap.sh                    # 包装脚本
├── public/                    # 静态资源目录
│   ├── favicon.ico            # 网站图标
│   ├── config.js              # 前端运行时配置
│   └── *.png                  # 图标资源
├── src/                       # 源代码目录
│   ├── App.tsx                # 应用主组件
│   ├── main.tsx               # 应用入口文件
│   ├── routes.tsx             # 路由配置
│   ├── vite-env.d.ts          # Vite环境类型声明
│   ├── assets/                # 静态资源文件
│   │   ├── *.svg              # SVG图标
│   │   ├── *.png              # 图片资源
│   │   ├── *.md               # 说明文档
│   │   └── InsightPlus/       # 特定功能资源
│   ├── components/            # 通用组件目录
│   │   ├── ErrorPage.tsx      # 错误页面组件
│   │   ├── LoadingDots.tsx    # 加载动画组件
│   │   ├── MarkdownViewer.tsx # Markdown查看器组件
│   │   ├── Navigator.tsx      # 导航组件
│   │   ├── ProtectedRoute.tsx # 路由保护组件
│   │   ├── UserInfoDrawer.tsx # 用户信息抽屉组件
│   │   ├── ModelConfigDrawer.tsx # 模型配置抽屉组件
│   │   ├── FileUploadDragger/ # 文件上传拖拽组件
│   │   ├── FormField/         # 表单字段组件
│   │   ├── LimitedModal/      # 限制型模态框组件
│   │   └── MarkdownEditor/    # Markdown编辑器组件
│   ├── config/                # 配置文件目录
│   │   ├── menu.tsx           # 菜单配置
│   │   └── theme.ts           # 主题配置
│   ├── contexts/              # React上下文目录
│   │   ├── AuthContext.tsx    # 认证上下文
│   │   └── PermissionContext.tsx # 权限上下文
│   ├── hooks/                 # 自定义钩子目录
│   ├── pages/                 # 页面组件目录
│   │   ├── Home.tsx           # 首页
│   │   ├── Login.tsx          # 登录页面
│   │   ├── NotFound.tsx       # 404页面
│   │   ├── ComingSoon.tsx     # 敬请期待页面
│   │   ├── AuthRedirect.tsx   # 认证重定向页面
│   │   ├── AiTraces/          # AI追踪页面
│   │   ├── Background/        # 后台管理页面
│   │   │   ├── Layout.tsx     # 后台布局
│   │   │   ├── Menu/          # 菜单管理
│   │   │   ├── Model/         # 模型管理
│   │   │   ├── Organization/  # 组织管理
│   │   │   ├── Role/          # 角色管理
│   │   │   └── Users/         # 用户管理
│   │   ├── Chat/              # 聊天页面
│   │   ├── Homework/          # 作业页面
│   │   └── Paper/             # 论文页面
│   │       ├── Config.tsx     # 配置页面
│   │       ├── History.tsx    # 历史记录页面
│   │       ├── Paper.tsx      # 论文主页面
│   │       └── Workflow.tsx   # 工作流页面
│   ├── styles/                # 样式文件目录
│   │   ├── global.css         # 全局样式
│   │   ├── Home.css           # 首页样式
│   │   ├── Navigator.css      # 导航样式
│   │   └── Reports.css        # 报告样式
│   ├── types/                 # 类型定义目录
│   │   ├── index.ts           # 类型导出入口
│   │   ├── ApiRes.ts          # API响应类型
│   │   ├── UserInfo.ts        # 用户信息类型
│   │   ├── ModelConfig.ts     # 模型配置类型
│   │   ├── Organization.ts    # 组织类型
│   │   ├── Role.ts            # 角色类型
│   │   ├── AiTrace.ts         # AI追踪类型
│   │   ├── Dictionary.ts      # 字典类型
│   │   ├── Menu.ts            # 菜单类型
│   │   ├── Page.ts            # 页面类型
│   │   ├── PaperConfig.ts     # 论文配置类型
│   │   └── ReportConfig.ts    # 报告配置类型
│   └── utils/                 # 工具函数目录
│       ├── index.ts           # 工具函数导出入口
│       ├── api.ts             # API请求封装
│       ├── auth.ts            # 认证工具
│       ├── crypto.ts          # 加密工具
│       ├── api_chat.ts        # 聊天API
│       ├── api_homework.ts    # 作业API
│       ├── api_report.ts      # 报告API
│       ├── api_report_config.ts # 报告配置API
│       ├── dictionary_api.ts  # 字典API
│       ├── fetch_sse.ts       # SSE请求工具
│       └── send_dify_message.ts # Dify消息发送工具
├── scripts/                   # 脚本目录
│   └── parse_api_to_llm_friendly.py # API解析脚本
├── test-results/              # 测试结果目录
└── tests/                     # 测试文件目录
```

## 2. 文件组织规范

### 2.1 组件分类与职责

**通用组件 (src/components/)**
- 可在多个页面复用的纯UI组件
- 不包含复杂业务逻辑和API请求
- 每个组件都有对应的样式文件
- 必须包含完整的TypeScript类型定义

**页面组件 (src/pages/)**
- 业务级别的页面组件，使用大驼峰命名
- 可以包含业务逻辑和API请求
- 每个页面可以有自己的components子目录存放私有组件

**页面私有组件 (src/pages/*/components/)**
- 只在特定页面使用的组件
- 可以包含业务逻辑和API请求
- 与页面强耦合的功能组件

### 2.2 文件命名规范

- **组件文件**: 使用PascalCase，如 `UserInfoDrawer.tsx`
- **样式文件**: 与组件同名，如 `UserInfoDrawer.css` 或 `index.module.css`
- **类型文件**: 使用PascalCase，如 `UserInfo.ts`
- **工具文件**: 使用snake_case，如 `api_report.ts`
- **页面文件**: 使用PascalCase，如 `Home.tsx`

### 2.3 导入导出规范

- 每个组件文件导出一个默认组件，命名与文件名一致
- 类型定义统一从 `src/types/index.ts` 导出
- 工具函数统一从 `src/utils/index.ts` 导出
- 使用相对路径导入同级或子级文件，使用 `@/` 别名导入其他文件

## 3. 模块职责划分

### 3.1 核心模块

**认证模块 (contexts/AuthContext.tsx)**
- 用户登录状态管理
- Token管理和刷新
- 用户信息维护

**路由模块 (routes.tsx)**
- 所有路由定义和配置
- 路由守卫和权限控制
- 嵌套路由结构

**API模块 (utils/api*.ts)**
- 统一的API请求封装
- 请求响应拦截器
- 错误处理和日志记录

**类型模块 (types/)**
- 全局类型定义
- API响应类型规范
- 业务数据模型

### 3.2 页面模块功能

- **Home**: 首页展示和导航
- **Login**: 用户登录和认证
- **Chat**: AI聊天交互功能
- **Paper**: 论文生成和管理
- **Homework**: 作业管理功能
- **Background**: 后台管理系统
- **AiTraces**: AI操作追踪和日志

## 4. 开发规范要求

### 4.1 文档同步
- 新增组件或页面必须更新 `directories.md`
- 重要功能变更需要更新 `README.md`
- 所有组件必须包含中文注释说明

### 4.2 代码质量
- 使用TypeScript严格模式
- 避免使用 `any` 类型
- 所有函数必须有明确的返回类型
- 重要逻辑必须添加详细日志

### 4.3 样式规范
- 优先使用Ant Design组件
- 自定义样式使用CSS Modules或全局样式
- 确保响应式设计支持
- 避免内联样式和硬编码值
4. API请求拦截器系统
  - 请求拦截器: 添加授权token
  - 响应拦截器: 统一错误处理和响应格式
  - API响应格式: {code, data, error, success}
  - 认证失效处理: 401状态码处理和登录重定向