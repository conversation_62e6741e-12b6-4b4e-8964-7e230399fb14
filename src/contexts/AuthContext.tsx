import { AuthState, UserInfo } from "@/types/UserInfo";
import { authApi } from "@/utils/api";
import { AuthManager } from "@/utils/auth";
import React, { createContext, ReactNode, useContext, useEffect, useState } from "react";

interface AuthContextProps {
  auth: AuthState | null;
  isAuthenticated: boolean;
  isSuperAdmin: boolean;
  isOrgAdmin: boolean;
  isTrial: boolean; // 是否是体验账号
  userInfo: UserInfo | null;
  enableGenerate: boolean;
  loading: boolean; // 加载状态
  login: (token: string) => void;
  logout: () => void;
  updateUserInfo: () => Promise<UserInfo | null>;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // 在初始化state时就从localStorage读取token
  const initializeAuth = (): AuthState | null => {
    const token = localStorage.getItem("token_");
    if (token) {
      return { token, enableGenerate: true };
    }

    // 初始化未找到token
    return null;
  };

  // 在useState初始化时就加载token
  const [auth, setAuth] = useState<AuthState | null>(initializeAuth());
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [isTrial, setIsTrial] = useState<boolean>(false); // 是否是体验账号
  const [loading, setLoading] = useState<boolean>(!!auth?.token); // 有token时初始为加载中状态

  // 基于auth状态判断认证
  const isAuthenticated = !!auth?.token;

  // 根据用户信息判断是否是超级管理员
  const isSuperAdmin = userInfo?.role?.identifier === "super_admin";
  // 判断是否是机构管理员
  const isOrgAdmin = userInfo?.role?.identifier === "admin";
  // 是否可以生成
  const enableGenerate = !!auth?.enableGenerate;

  // 获取用户信息
  const fetchUserInfo = async (): Promise<UserInfo | null> => {
    if (!isAuthenticated) {
      setLoading(false);
      return null;
    }
    
    setLoading(true);
    try {
      const data = await authApi.getCurrentUser();
      console.log('获取到用户信息:', data);
      setUserInfo(data);
      
      // 设置是否是体验账号状态
      // setIsTrial(data.is_trial === true);
      console.log('是否是体验账号:', data.is_trial);
      
      // 更新 auth 状态，包含用户信息
      setAuth(prev => ({
        ...prev,
        userInfo: data,
        enableGenerate: data.is_could_create_report,  // 是否可以生成，用户用量状态
      } as AuthState));
      
      return data;
    } catch (error: any) {
      console.error('获取用户信息失败:', error);
      logout();
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 在有token的情况下，组件挂载时获取用户信息
  useEffect(() => {
    if (auth?.token) {
      fetchUserInfo();
    } else {
      setLoading(false);
    }
  }, []);

  // 登录
  const login = async (token: string) => {
    setLoading(true);
    try {
      // 保存token
      localStorage.setItem('token_', token);
      
      // 获取用户信息
      const userInfoRes = await authApi.getCurrentUser();
      console.log("登录后获取用户信息成功:", userInfoRes);
      
      // 更新用户信息状态
      setUserInfo(userInfoRes);

      // 设置是否是体验账号状态
      // setIsTrial(userInfoRes.is_trial === true);
      console.log('是否是体验账号2:', userInfoRes.is_trial);
      
      // 同时更新auth状态，包含token和enableGenerate
      setAuth(prev => ({
        ...prev,
        token,
        userInfo: userInfoRes,
        enableGenerate: userInfoRes.is_could_create_report  // 同时更新enableGenerate
      }));
      
      return true;
    } catch (error) {
      console.error('登录后获取用户信息失败:', error);
      logout(); // 出错时登出
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 登出
  const logout = () => {
    AuthManager.clear();
    setAuth(null);
    setUserInfo(null);
    setIsTrial(false); // 重置体验账号状态
    console.log("登出");
  };

  // 更新用户信息方法
  const updateUserInfo = async () => {
    return await fetchUserInfo();
  };

  // 添加调试信息
  useEffect(() => {
    console.log("全局状态更新:", {
      isAuthenticated,
      loading,
      userInfo: userInfo ? {
        username: userInfo.username,
        used_count: userInfo.used_count,
        max_allowed_count: userInfo.max_allowed_count
      } : null,
      isTrial
    });
  }, [auth, userInfo, loading]);

  return (
    <AuthContext.Provider
      value={{
        auth,
        isAuthenticated,
        isSuperAdmin,
        isOrgAdmin,
        isTrial,
        userInfo,
        enableGenerate,
        loading,
        login,
        logout,
        updateUserInfo,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// 自定义钩子，方便组件获取认证上下文
export const useAuth = (): AuthContextProps => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
