import React, { createContext, useContext, useState, useEffect } from 'react';
import { menuApi } from '@/utils/api';
import { Menu } from '@/types/Menu';
import { useAuth } from './AuthContext';
import { CustomMenuItem, getMenuConfig } from '@/config/menu';
import LoadingDots from '@/components/LoadingDots';

interface PermissionContextType {
  authorizedMenus: CustomMenuItem[];
  loading: boolean;
  hasMenuPermission: (menuPath: string) => boolean;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

// Menu 类型定义，包含 order排序
interface MenuWithOrder {
  order?: number;
}

export const PermissionProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [authorizedMenus, setAuthorizedMenus] = useState<CustomMenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const { isSuperAdmin, isOrgAdmin, isAuthenticated, userInfo } = useAuth();
  
  useEffect(() => {
    // 只有当认证状态确定且用户信息加载完成后才加载菜单权限
    if (!isAuthenticated) {
      setAuthorizedMenus([]);
      setLoading(false);
      return;
    }
    
    // 确保用户信息已加载
    if (!userInfo) {
      console.log('等待用户信息加载...');
      return; // 用户信息未加载完成，不继续执行
    }
    
    const loadMenuPermissions = async () => {
      try {
        if (isSuperAdmin) {
          // 超级管理员直接使用完整菜单配置
          const adminMenus = getMenuConfig(isSuperAdmin, isOrgAdmin);
          setAuthorizedMenus(adminMenus);
        } else if (isOrgAdmin) {
          // 机构管理员特殊处理
          const roleMenus = await menuApi.getRoleMenuTree(true);
          // 设置菜单缓存
          menuApi.setCachedRoleMenus(roleMenus);
          
          // 获取包含机构管理员可见后台菜单的配置
          const allMenus = getMenuConfig(isSuperAdmin, isOrgAdmin);
          
          // 获取所有菜单并标记权限
          const menus = markMenusWithPermission(allMenus, roleMenus);
          setAuthorizedMenus(menus);
          console.log('机构管理员菜单(包含所有菜单):', menus);
        } else {
          // 普通用户：显示所有菜单，但在hasMenuPermission中判断权限
          const roleMenus = await menuApi.getRoleMenuTree(true);
          // 设置菜单缓存
          menuApi.setCachedRoleMenus(roleMenus);
          
          // 获取基础菜单配置
          const allMenus = getMenuConfig(false, false);
          
          // 获取所有菜单并标记权限
          const menus = markMenusWithPermission(allMenus, roleMenus);
          setAuthorizedMenus(menus);
          console.log('普通用户菜单(包含所有菜单):', menus);
        }
      } catch (error) {
        console.error('加载菜单权限失败:', error);
        // 出错时使用空菜单
        setAuthorizedMenus([]);
      } finally {
        setLoading(false);
      }
    };
    
    loadMenuPermissions();
  }, [isAuthenticated, isSuperAdmin, isOrgAdmin, userInfo]);
  
  // 标记菜单权限状态的函数
  const markMenusWithPermission = (
    configMenus: CustomMenuItem[],
    roleMenus: Menu[]
  ): CustomMenuItem[] => {
    // 将角色菜单项转换为Map，保存标识符和排序信息
    const roleMenuMap = new Map<string, Menu & MenuWithOrder>();
    roleMenus.forEach(menu => {
      roleMenuMap.set(menu.identifier, menu as Menu & MenuWithOrder);
    });
    
    // 递归标记菜单权限
    const markMenus = (menus: CustomMenuItem[]): CustomMenuItem[] => {
      const result: CustomMenuItem[] = [];
      
      for (const menu of menus) {
        // 检查当前菜单是否有权限
        const menuKey = menu.key as string;
        const hasPermission = roleMenuMap.has(menuKey);
        
        // 创建新菜单对象，避免修改原始配置
        const newMenu = { ...menu };
        
        // 处理子菜单
        if (menu.children && menu.children.length > 0) {
          newMenu.children = markMenus([...menu.children]);
        }
        
        result.push(newMenu);
      }
      
      // 对结果进行排序 - 数值大的在前面
      // return result.sort((a, b) => {
      //   const orderA = roleMenuMap.get(a.key as string)?.order || 0;
      //   const orderB = roleMenuMap.get(b.key as string)?.order || 0;
      //   return orderB - orderA; // 降序排列（大的在前）
      // });
      // 原始排序
      return result;
    };
    
    return markMenus([...configMenus]);
  };
  
  // 检查用户是否有特定菜单路径的权限
  const hasMenuPermission = (menuPath: string): boolean => {
    // 超级管理员拥有所有权限
    if (isSuperAdmin) {
      return true;
    }
    
    // 对于机构管理员，需要特殊处理后台管理模块
    if (isOrgAdmin && menuPath.startsWith('/background')) {
      // 机构管理员可以访问用户管理和角色管理
      const adminPaths = ['/background/user', '/background/role'];
      return adminPaths.includes(menuPath);
    }
    
    // 获取菜单标识符
    const getMenuIdentifierFromPath = (path: string): string | null => {
      // 处理特殊路径
      if (path.startsWith('/college/')) {
        return 'college';
      }
      
      // 提取第一级路径作为菜单标识符
      const segments = path.split('/').filter(Boolean);
      if (segments.length > 0) {
        return segments[0];
      }
      return null;
    };
    
    // 获取菜单标识符
    const menuIdentifier = getMenuIdentifierFromPath(menuPath);
    if (!menuIdentifier) {
      return false;
    }
    
    // 判断当前用户是否拥有该菜单权限
    const roleMenusData = menuApi.getCachedRoleMenus();
    if (!roleMenusData) {
      return false;
    }
    
    return roleMenusData.some(menu => menu.identifier === menuIdentifier);
  };
  
  if (loading) {
    return <LoadingDots />;
  }
  
  return (
    <PermissionContext.Provider value={{ 
      authorizedMenus, 
      loading,
      hasMenuPermission
    }}>
      {children}
    </PermissionContext.Provider>
  );
};

// 自定义Hook便于使用权限
export const usePermission = () => {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermission必须在PermissionProvider内部使用');
  }
  return context;
};