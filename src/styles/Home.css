

.college-agent-home {
  width: 100%;
  height: 100%;
  background-color: #EEF3FF;
  padding: 24px;
}

.welcome-card {
  border-radius: 12px;
  margin-bottom: 24px;
}

.welcome-title {
  margin-bottom: 12px !important;
}

.welcome-slogan {
  font-size: 16px;
  margin-bottom: 16px !important;
  color: rgba(0, 0, 0, 0.65);
}

.brain-icon {
  font-size: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.date-info, .streak-info {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
}

.info-icon {
  margin-right: 8px;
}

.stat-card {
  border-radius: 12px;
  height: 100%;
}

.stat-card-inner {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.icon-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin-bottom: 10px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
}

.stat-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

/* 快速开始区域样式 */
.quick-start-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.quick-start-items-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 16px;
}

.quick-start-item {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 20px;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  border: 1px solid #f0f0f0;
}

.quick-start-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
}

.quick-start-icon {
  width: 50px;
  height: 50px;
  font-size: 28px;
  color: #fff;
  display: block;
  align-items: center;
  text-align: center;
  border-radius: 16px;
  margin-bottom: 12px;
}

.quick-start-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.quick-start-subtitle {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.5);
}

.quick-start-arrow {
  position: absolute;
  bottom: 12px;
  right: 16px;
  font-size: 24px;
  opacity: 0.4;
  transition: all 0.3s;
}

.quick-start-item:hover .quick-start-arrow {
  opacity: 0.8;
  transform: translateX(3px);
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .welcome-title {
    font-size: 1.5rem !important;
    margin-top: 16px;
  }
  
  .brain-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }
  
  .stat-value {
    font-size: 20px;
  }

  .quick-start-items-container {
    flex-direction: column;
  }
  
  .quick-start-item {
    margin-bottom: 12px;
    height: auto;
    min-height: 100px;
  }
}
