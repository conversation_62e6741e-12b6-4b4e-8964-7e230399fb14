.layout-container {
  min-width: 1400px;
  min-height: 700px;
  height: 100vh;

  .header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    padding: 0 20px;
    position: sticky;

    .app-logo {
      width: 160px;
      font-size: 18px;
      font-weight: bold;
      /* color: #4285F4; */
      margin-right: 20px;
      white-space: nowrap;
    }

    /* 模块名称、面包屑、搜索 */
    .header-middle {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;

      /* 模块名称/面包屑导航 */
      .module-navigation {
        display: flex;
        align-items: self-end;
        
        h4 {
          margin-bottom: 0 !important;
          margin-right: 16px !important;
          color: #333;
        }
        
        .ant-breadcrumb {
          font-size: 14px;
          color: #888;
        }
      }

      /* 搜索框 */
      .global-search {
        flex: 1;
        text-align: center;
    
        .ant-input-affix-wrapper {
          border-radius: 16px;
          border-color: #e8e8e8;
          
          &:hover, &:focus {
            border-color: #4285F4;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.5);
          }
          
          .ant-input {
            font-size: 13px;
          }
        }
      }
    }

    /* 中间导航菜单 */
    .nav-menu {
      flex: 1;
      justify-content: center;
      border: none;

      .ant-menu-item {
        display: flex;
        align-items: center;
        height: 64px;
        
        span {
          line-height: 28px;
          margin-left: 8px !important;
        }
      }
    }


    /* 使用次数 */
    .user-area {
      display: flex;
      align-items: center;
      gap: 12px;

      .usage-counter {
        font-size: 12px;
        line-height: 1.6;
        text-align: center;

        .usage-count-normal {
          color: #7b61ff;
        }

        .usage-count-exceeded {
          color: #ff4d4f;
        }
      }
    }
  }

  .header-container::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 100%;
    height: 2px;
    border-bottom: 2px solid transparent;
    border-image: linear-gradient(to right, #4285F4, #EF81BB) 1;
  }


  /* 内容区 */
  .main-content {
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .main-content-login {
    padding: 0;
  }

  /* 页脚样式 */
  .app-footer {
    text-align: center;
    background: #fff;
    padding: 5px 0;
    flex-shrink: 0;
  }

}

/* 侧边栏导航 */
.side-container {
  background: #fff;
  height: 100%;
  border: none;

  .title {
    width: 100%;
    height: 50px;
    max-height: 50px;
    border-bottom: 1px solid #e0e0e0;
    padding-left: 20px;
    padding-right: 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .logo {
      width: auto;
      max-width: 90px;
      transition: all 0.3s ease-out;
    }

    .icon {
      width: 24px;
      min-width: 24px;
      margin-right: 0px;
      margin-left: auto;
      cursor: pointer;
    }
  }

  .side-menu {
    border-inline-end: none !important;
    padding: 16px 0;
    
    .ant-menu-item {
      margin: 4px;
    }
    
    .ant-menu-item-selected {
      background-color: #f0f5ff !important;
      color: #4285F4;
      font-weight: 500;
      padding-left: 32px !important;
    }
    
    .ant-menu-item-selected::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 4px;
      height: 100%;
      background-color: #4285F4;
    }
    
    .ant-menu-item .anticon {
      /* margin-right: 16px; */
      font-size: 18px;
    }
  }
}
