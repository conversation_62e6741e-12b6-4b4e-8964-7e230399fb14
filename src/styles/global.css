:root {
  --primary-color: #2A5CA<PERSON>;
  --text-color: #333;
  --text-secondary: #666;
  --background-color: #f9faff;
  --border-radius: 12px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --font-title: 'PingFang SC', '微软雅黑', 'Microsoft YaHei', '苹方', '黑体', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-body: 'PingFang SC', '微软雅黑', 'Microsoft YaHei', '宋体', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-body);
  background: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-title);
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 1px;
}

::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 1px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-content {
  padding: 40px 0;
}

/* Markdown样式 */
.markdown-body {
  font-family: var(--font-body);
  line-height: 1.7;
  color: var(--text-color);
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  font-family: var(--font-title);
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  font-weight: 600;
}

.markdown-body h1 {
  font-size: 2em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-body h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-body p {
  margin: 0.75em 0;
}

.markdown-body ul, 
.markdown-body ol {
  padding-left: 2em;
  margin: 0.75em 0;
}

.markdown-body code {
  background-color: rgba(123, 97, 255, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
}

.markdown-body pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin: 1em 0;
}

.markdown-body pre code {
  background: none;
  padding: 0;
}

.markdown-body blockquote {
  border-left: 4px solid var(--primary-color);
  padding: 0 1em;
  color: #6a737d;
  margin: 1em 0;
}

.markdown-body table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.markdown-body table th,
.markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-body table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.markdown-body table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.model-config-item:hover {
  background-color: #fafafa !important;
}

.model-config-item:last-child {
  border-bottom: none !important;
} 