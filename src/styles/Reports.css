/* 页面容器样式 */
.reports-container {
  width: 100%;
  height: 100%;
  padding: 16px !important;
}

/* 列表和预览区容器 */
.reports-row {
  height: calc(100%);
}

/* 左侧历史报告列表 */
.reports-list-col {
  height: 100%;
  
  .reports-list-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    height: 100%;
    padding: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .ant-card-body {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    .reports-header {
      margin-bottom: 24px;
      display: flex;
      justify-content: left;
      align-items: center;
      flex-shrink: 0;

      .toHome{
        font-size: 20px;
        color: #6c6c6c;
        margin-left: 6px;
      }
    }

    .reports-intro {
      margin-bottom: 16px;
      flex-shrink: 0;
    }

  }
}


/* 报告卡片样式 */
.report-card {
  margin-bottom: 12px;
  cursor: pointer;
  padding: 12px 16px;

  .ant-card-body {
    padding: 0;

    .report-card-header {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      
      .report-title {
        flex: 1;
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 6px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .report-meta {
      font-size: 13px;
      color: #888;
      margin-bottom: 6px;
    }

    .report-footer {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      font-size: 13px;
      color: #888;
      margin-bottom: 6px;
    }
    
  }
}

.report-card.selected {
  background: #f5f0ff;
}


/* 右侧报告预览 */
.preview-col {
  height: 100%;
}

.preview-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  height: 100%;
  padding: 8px;

  .ant-card-body {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .preview-content {
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .preview-header {
        /* display: flex;
        justify-content: space-between;
        align-items: center; */
        margin-bottom: 8px;
        width: 100%;

        .preview-title {
          margin: 0;
        }

      }

      .header-buttons {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .ant-btn {
          padding: 0 8px;
        }
      }


    }
  }
}

.preview-title-center {
  margin: 0;
  width: 100%;
  text-align: center;
}

.preview-loading {
  text-align: center;
  margin: 20px 0;

  .loading-text {
    margin-top: 12px;
    max-width: 100%;
  }
}

.preview-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .preview-empty-text {
    text-align: center;
  }
}


