import { Organization } from "./Organization";

export enum UserRole {
  ADMIN = "admin",
  CLIENT = "client",
  TRIAL = "trial",
}

export enum UserRoleName {
  admin = "管理员",
  client = "普通用户",
  trial = "体验用户",
}

export interface LoginInfo {
  access_token: string;
  token_type: string;
  is_admin: boolean;
  role_name: string;
  is_create_reports: boolean; // 是否可以生成
}

export interface AuthState {
  token: string;
  enableGenerate?: boolean; // 是否可以生成
  userInfo?: UserInfo; // 用户信息
}

/** 用户基本信息 */
export interface UserInfo {
  username: string; // 用户名
  id: string; // 用户ID
  created_at: string; // 创建时间
  position: string; // 职称
  company: string; // 单位
  achievement: string; // 成就
  mobile: string; // 手机号，加密
  realname: string; // 真实姓名
  updated_at: string; // 更新时间
  is_deleted: boolean;
  role: {
    name: string; // 用户角色名称
    id: string; // 用户角色ID
    identifier: string; // 用户角色标识，super_admin, admin, user分别是 超级管理员，管理员，普通用户
  };
  is_trial?: boolean; // 是否是体验账号
  is_could_create_report?: boolean; // 是否可以生成，用户用量状态
  permissions?: string[]; // 用户权限列表
  used_count?: number; // 使用次数
  max_allowed_count?: number; // 最大允许使用次数
  organization_id?: string; // 所属机构ID
  organization?: Organization; // 所属机构
}

// 创建用户表单数据
export interface CreateUserForm {
  username: string; // 用户名
  password: string; // 密码
  confirmPassword: string; // 确认密码
  realname: string; // 姓名
  company: string; // 单位
  position: string; // 职称
  achievement: string; // 成就
  role_name: string; // 用户角色名称
  role_id: string; // 用户角色ID
  maxAllowedCount?: number; // 最大允许使用次数
  organization_id?: string; // 所属机构ID
}
