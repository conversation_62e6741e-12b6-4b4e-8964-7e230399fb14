/**
 * Toast UI Editor 上标下标插件的 TypeScript 声明文件
 * 用于支持在 React + TypeScript 项目中使用 tui.editor.supsup_plugin
 */

declare module 'tui.editor.supsup_plugin' {
  // 事件发射器接口
  interface EventEmitter {
    listen(event: string, callback: (...args: any[]) => void): void;
  }

  // ProseMirror 状态接口
  interface PMState {
    TextSelection: any;
  }

  // 插件上下文接口
  interface PluginContext {
    eventEmitter: EventEmitter;
    pmState: PMState;
  }

  // 命令执行器类型
  type CommandExecutor = (payload: any, state: any, dispatch: any) => boolean;

  // 工具栏按钮配置
  interface ToolbarButton {
    name: string;
    tooltip: string;
    command: string;
    className: string;
  }

  // 工具栏项配置
  interface ToolbarItem {
    groupIndex: number;
    itemIndex: number;
    item: ToolbarButton;
  }

  // HTML 渲染器节点
  interface HTMLRendererNode {
    type: 'openTag' | 'closeTag';
    tagName: string;
  }

  // HTML 渲染器函数类型
  type HTMLRenderer = (node: any, context: { entering: boolean }) => HTMLRendererNode;

  // 插件返回对象接口
  interface SupSubPluginReturn {
    markdownCommands: {
      subscript: CommandExecutor;
      superscript: CommandExecutor;
    };
    wysiwygCommands: {
      subscript: CommandExecutor;
      superscript: CommandExecutor;
    };
    toolbarItems: ToolbarItem[];
    toHTMLRenderers: {
      htmlInline: {
        sub: HTMLRenderer;
        sup: HTMLRenderer;
      };
    };
  }

  // 插件主函数
  function supsubPlugin(context: PluginContext): SupSubPluginReturn;

  // 默认导出
  export default supsubPlugin;
} 