// AI去痕结果接口类型定义
export interface TraceResult {
  original: string;
  modified: string;
  seriesNum: number;
}

// AI去痕响应接口类型定义
export interface AiTraceResponse {
  trace_id: string;
  message: string;
  file_name: string;
  file_size: number;
  char_count: number;
  processed_sections: number;
  trace_result: TraceResult[];
}

// 已上传的AI去痕文件接口类型定义
export interface UploadedTraceFile {
  trace_id: string;
  file_name: string;
  file_size: number;
  char_count: number;
  processed_sections: number;
  trace_result: TraceResult[];
  upload_time: string;
}

