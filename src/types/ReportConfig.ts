import { AreaItem } from "@/types/Dictionary";
import { UserInfo } from '@/types/UserInfo';
import { ModelConfig } from './ModelConfig';


/** 项目主体 */
export interface ProjectLeader {
  id: string; // 项目主体唯一标识符
  name: string; // 项目主体名称（如公司、机构名）
  credit_code: string; // 统一社会信用代码
  institution_type: string; // 机构性质，01:国有企业 02:民营企业 03:外资企业 04:合资企业 05:事业单位
  founded_date: string; // 成立日期，格式 YYYY-MM-DD
  // patent_count: number; // 专利数量
  related_projects: string; // 相关项目描述
  created_at: string; // 记录创建时间
  updated_at: string; // 记录更新时间
  is_deleted: number; // 删除标志，0=未删除，1=已删除
  deleted_at: string | null; // 删除时间，未删除时为null
  province_id?: number; // 省份ID（可选）
  city_id?: number; // 城市ID（可选）
  district_id?: number; // 区县ID（可选
  province?: AreaItem;
  city?: AreaItem;
  district?: AreaItem;
  address?: string; // 详细地址（可选）
  website?: string; // 网站（可选）
}

/** 项目成员 */
export interface ProjectMember {
  id: string; // 项目成员唯一标识符
  name: string; // 项目成员姓名
  title: string; // 项目成员职称
  education: string; // 学历
  introduction: string; // 简介
  organization: string | null; // 所属组织机构
  representative_works: string; // 代表性作品
  created_at: string; // 记录创建时间
  updated_at: string; // 记录更新时间
  is_deleted: number; // 删除标志，0=未删除，1=已删除
  deleted_at: string | null; // 删除时间，未删除时为null
}

/** 项目成员关联 */
export interface ProjectMemberJoin {
  join_id: string; // 关联ID，用于将多个成员关联到同一个项目/实体
  member: ProjectMember; // 关联的成员信息
  created_at: string; // 记录创建时间
  is_deleted: number; // 删除标志，0=未删除，1=已删除
  deleted_at: string | null; // 删除时间，未删除时为null
}

/** 项目配置 */
export interface ProjectConfig {
  id: string; // 项目配置唯一标识符
  name: string; // 项目名称
  application_category: string; // 申报口径
  leader: ProjectLeader; // 申报主体
  user: UserInfo; // 创建人
  team_members: ProjectMemberJoin[]; // 团队成员列表
  word_count_requirement: number; // 字数要求
  literature_library: string; // 文献库
  url_ids: string[]; // 参考文献网址ID
  requirements_attachments: string[]; // 需求附件
  language_style: string; // 语言风格
  ai_generated_outline: string; // AI生成的大纲
  manual_modified_outline: string; // 手动修改后的大纲
  ai_generated_report: string; // AI生成的报告
  report_generation_time: string; // 报告生成时间
  outline_generated_time: string; // 大纲生成时间
  status: ProjectStatus; // 项目状态
  created_at: string; // 记录创建时间
  updated_at: string; // 记录更新时间
  is_deleted: number; // 删除标志，0=未删除，1=已删除
  deleted_at: string | null; // 删除时间，未删除时为null
  outline_tokens_consumed: number; // 大纲消耗
  report_tokens_consumed: number; // 报告消耗
  team_introduction: string; // 团队介绍
  ai_leader_introduction: string; // 材料主体AI介绍
  model: ModelConfig; // 模型
  user_add_prompt: string; // 额外信息补充
  user_add_demo_id: string; // 自定义模版ID
  estimated_time: string; // 预计生成结束时间
}

/** 项目状态 */
export enum ProjectStatus {
  CONFIGURING = "CONFIGURING", // 配置中
  OUTLINE_GENERATING = "OUTLINE_GENERATING", // 大纲生成中
  OUTLINE_GENERATED = "OUTLINE_GENERATED", // 大纲生成完成
  OUTLINE_FAILED = "OUTLINE_FAILED", // 大纲生成失败
  OUTLINE_CANCELED = "OUTLINE_CANCELED", // 大纲生成取消
  REPORT_GENERATING = "REPORT_GENERATING", // 报告生成中
  REPORT_GENERATED = "REPORT_GENERATED", // 报告生成完成
  REPORT_FAILED = "REPORT_FAILED", // 报告生成失败
  REPORT_CANCELED = "REPORT_CANCELED", // 报告生成取消
  REMOVE_HALLUCINATING = "REMOVE_HALLUCINATING", // 幻觉审查中
  REMOVE_HALLUCINATED = "REMOVE_HALLUCINATED", // 幻觉审查完成
  REMOVE_AI_TRACING = "REMOVE_AI_TRACING", // AI去痕中
  REMOVE_AI_TRACED = "REMOVE_AI_TRACED", // AI去痕完成
}

/** 创建项目配置参数 */
export interface ProjectConfigCreate {
  name: string; // 项目名称
  application_category: string; // 申报口径
  leader: string; // 申报主体ID
  ai_leader_introduction: string; // 材料主体AI介绍
  team_members: string; // 团队成员关联ID
  team_introduction: string; // 团队介绍 
  word_count_requirement: number; // 字数要求
  literature_library: string; // 文献库
  url_ids: string[]; // 参考文献网址ID - 数组或字符串
  requirements_attachments_id: string[]; // 需求附件ID
  model: string; // 模型配置ID
  language_style: string; // 语言风格
  user_add_prompt: string; // 额外信息补充
  user_add_demo_id: string; // 自定义模版ID
  status: string; // 项目状态
}

/** 参考文献网址 */
export interface ReferenceUrl {
  id: string;   // 网址ID
  url: string;  // 网址
  is_valid: boolean | null; // 是否有效
  created_at?: string; // 创建时间
  updated_at?: string; // 更新时间
  project_id?: string; // 项目ID
}