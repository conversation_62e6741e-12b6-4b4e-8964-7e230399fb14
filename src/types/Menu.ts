import { MenuItemType } from "antd/es/menu/interface";
import { ReactNode } from "react";

// 菜单类型定义
export interface Menu {
  id: string;
  name: string;        // 菜单名称，如"HI-InsightPlus"
  identifier: string;  // 菜单标识符，如"insight_plus"
  parent_id: string | null; // 父菜单ID，顶级菜单为null
  order: number;       // 排序序号
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  menu_id?: string;
}

// 创建菜单表单数据
export interface CreateMenuForm {
  parent_id?: string;  // 父菜单ID
  name: string;        // 菜单名称
  identifier: string;  // 菜单标识符
  order: number;       // 排序序号
}

// 菜单树结构，用于展示
export interface MenuTreeNode extends Menu {
  children?: MenuTreeNode[];
  key?: string;        // 用于Tree组件
  title?: ReactNode;   // 用于Tree组件
  label?: string;      // 用于Select组件
  value?: string;      // 用于Select组件
} 