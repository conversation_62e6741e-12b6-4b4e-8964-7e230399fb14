/**
 * 分页数据格式
 */
export interface BasePageModel<T> {
    items: T[];
    page: number;
    size: number;
    total: number;
}

/**
 * 分页查询参数接口
 */
export interface PaginationParams {
    page: number;
    size: number;
    keyword?: string;
}

/**
 * 分页状态接口
 */
export interface PaginationState {
    current: number;
    pageSize: number;
    total: number;
}

/**
 * 处理分页数据响应
 * 统一处理不同格式的分页数据响应
 */
export const processPaginatedResponse = <T>(result: any): { data: T[], total: number } => {
    let items: T[] = [];
    let total = 0;

    if (Array.isArray(result)) {
        // 直接返回数组的情况
        items = result;
        total = result.length;
    } else if (result && typeof result === 'object') {
        // 标准分页数据结构
        if ('items' in result && Array.isArray(result.items)) {
        items = result.items;
        total = result.total || 0;
        }
    }

    return { data: items, total };
}; 