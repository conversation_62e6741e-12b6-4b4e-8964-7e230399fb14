// 幻觉审查文件上传
export interface HallucinationUploadResponse {
  id: string;
  file_path: string;
  file_name: string;
  created_at: string;
}

// 幻觉审查状态枚举
export type HallucinationStatus = 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'NO-START' | 'ONGOING';

// 幻觉审查文件信息
export interface HallucinationFile {
  id: string;
  file_path: string;
  file_name: string;
  created_at: string;
}

// 幻觉审查验证
export interface HallucinationVerifyResponse {
  id: string;
  ai_report: string;
  status: HallucinationStatus;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  deleted_at: string;
  file: HallucinationFile;
}

// 已上传的文件
export interface UploadedHallucinationFile {
  verify_id: string;
  file_id: string;
  file_name: string;
  status: HallucinationStatus;
  ai_report: string;
  upload_time: string;
}

// 创建请求参数
export interface CreateVerifyRequest {
  file_id: string;
}

// 历史记录
export interface HistoryItem {
  id: string;
  ai_report: string;
  status: HallucinationStatus;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  deleted_at: string;
  file: HallucinationFile;
}

// 历史分页响应
export interface HistoryResponse {
  items: HistoryItem[];
  total: number;
  page: number;
  size: number;
  has_more: boolean;
}

