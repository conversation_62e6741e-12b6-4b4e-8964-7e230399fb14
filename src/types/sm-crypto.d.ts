declare module 'sm-crypto' {
  export interface SM4Options {
    mode?: 'cbc' | 'ecb';
    iv?: string;
    output?: 'string' | 'array';
    padding?: 'pkcs#5' | 'pkcs#7';
  }

  export interface SM3Interface {
    (data: string): string;
  }

  export interface SM4Interface {
    encrypt(data: string, key: string, options?: SM4Options): any;
    decrypt(data: string | Uint8Array, key: string, options?: SM4Options): string;
  }

  const sm3: SM3Interface;
  const sm4: SM4Interface;
  
  export { sm3, sm4 };
} 