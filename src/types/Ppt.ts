/**
 * PPT相关类型定义
 */

// PPT生成状态枚举
export enum PptGenerateStatus {
  PENDING = 'pending',
  PROCESSING = 'processing', 
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// PPT模板类型枚举
export enum PptTemplateType {
  GRADUATION = 'graduation',
  CREATIVE = 'creative',
  SIMPLE = 'simple'
}

// PPT模板接口
export interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: PptTemplateType;
  created_at?: string;
  updated_at?: string;
}

// 上传文件信息接口
export interface UploadedFile {
  file: File;
  name: string;
  size: number;
  type: string;
}

// PPT生成请求参数接口
export interface PptGenerateRequest {
  file: File;
  ppt_type: PptTemplateType | string;
}

// PPT生成结果接口
export interface PptGenerateResult {
  id: string;            // 任务唯一标识符
  file_path: string;     // 服务器文件路径
  filename: string;      // 文件名
  size: number;          // 文件大小（字节）
  download_url: string;  // 下载链接
  preview_url: string;   // 预览链接
  document_content: string; // 文档内容预览（前500字符）
  generation_time: number;  // 生成耗时（秒）
}

// PPT生成响应接口
export interface PptGenerateResponse {
  success: boolean;
  code: number;
  data: PptGenerateResult;
  error?: string;
  timestamp?: number;
}

// PPT生成任务状态接口
export interface PptGenerateTaskStatus {
  task_id: string;
  status: PptGenerateStatus;
  progress: number;       // 进度百分比 0-100
  message: string;        // 状态描述
  result?: PptGenerateResult; // 生成完成时的结果
  error?: string;         // 错误信息
  created_at: string;     // 任务创建时间
  updated_at: string;     // 任务更新时间
}

// PPT下载参数接口
export interface PptDownloadParams {
  file_path: string;
  filename: string;
}

// PPT预览信息接口
export interface PptPreviewInfo {
  filename: string;
  size: number;
  page_count: number;     // 页面数量
  thumbnail?: string;     // 预览缩略图
  created_at: string;
}

// PPT编辑参数接口（预留，后续功能）
export interface PptEditParams {
  file_path: string;
  operations: PptEditOperation[];
}

// PPT编辑操作接口（预留，后续功能）
export interface PptEditOperation {
  type: 'add_slide' | 'delete_slide' | 'edit_text' | 'edit_image';
  slide_index?: number;
  content?: any;
}

// PPT生成历史记录接口
export interface PptGenerateHistory {
  id: string;
  original_filename: string;
  ppt_filename: string;
  template_type: PptTemplateType;
  file_size: number;
  status: PptGenerateStatus;
  created_at: string;
  completed_at?: string;
}

// API错误响应接口
export interface PptApiError {
  code: number;
  message: string;
  details?: string;
  timestamp: number;
}
