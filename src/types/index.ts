// 报告状态枚举
export enum ReportStatus {
  PENDING = "pending",
  GENERATING = "generating",
  COMPLETED = "completed",
  FAILED = "failed"
}

// 报告模型接口
export interface Report {
  id: string;
  title: string;
  outline?: string;
  fund_type: string;
  content_md: string | null;
  status: ReportStatus;
  created_at?: string;
}

// 创建报告请求参数
export interface CreateReportParams {
  title: string;
  outline: string;
  fund_type: string;
}

// 报告流式生成事件数据
export interface StreamEvent {
  content?: string;
  status?: 'completed' | 'error';
  message?: string;
}

// API错误响应
export interface ApiError {
  status: number;
  detail: string;
}

export * from './ModelConfig';
// export * from './Organization';
export * from './UserInfo';

