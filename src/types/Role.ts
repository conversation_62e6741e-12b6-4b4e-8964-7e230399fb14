// import { Organization } from "./Organization";

// 角色类型定义
export interface Role {
  id: string;
  name: string;        // 角色名称
  identifier: string;        // 角色标识
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  // organization?: Organization; // 所属机构
}

// 创建角色表单数据
export interface CreateRoleForm {
  name: string;        // 角色名称
  identifier: string;        // 角色标识
}