export interface Organization {
  id: string;
  name: string;         // 机构名称
  type: string;         // 机构类型
  contact_person: string; // 联系人
  contact_phone: string;  // 手机号码
  contact_email: string;  // 联系邮箱
  description: string | null; // 机构描述
  remarks: string | null;     // 备注信息
  code: string;         // 机构编码（唯一）
  is_active: boolean;   // 是否启用
  created_at: string;
  updated_at: string;
  is_trial: boolean;
  use_count: number;
  is_deleted: boolean;
  deleted_at?: string;  // 删除时间
  user_count?: number;  // 所属用户数
  token_usage?: number; // Token使用量
  api_call_count?: number; // API调用次数
  report_count?: number;   // 报告生成次数
  last_active_at?: string; // 最近活跃时间
  limit_count?: number;    // 最大使用次数
}

// 创建机构表单数据
export interface CreateOrganizationForm {
  name: string;         // 机构名称
  type: string;         // 机构类型
  is_trial: boolean;    // 是否体验
  contact_person: string; // 联系人
  contact_phone: string;  // 手机号码
  contact_email: string;  // 联系邮箱
  description?: string;   // 机构描述
  remarks?: string;       // 备注信息
  code: string;           // 机构编码
  is_active?: boolean;    // 是否启用
  limit_count?: number;   // 最大使用次数
}