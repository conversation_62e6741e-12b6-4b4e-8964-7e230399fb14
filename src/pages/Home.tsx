import CollegeAgentLogo from '@/assets/College-Agent-logo.svg';
import React from 'react';
import { Typography, Card, Row, Col, Space } from 'antd';
import '@/styles/Home.css';
import { useAuth } from '@/contexts/AuthContext';
import { 
  MessageOutlined,
  ClockCircleOutlined, 
  LineChartOutlined, 
  TrophyOutlined,
  CalendarOutlined,
  ThunderboltOutlined,
  FileOutlined,
  SafetyOutlined,
  FileTextOutlined,
  BookOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph } = Typography;

// 模拟图标组件
const IconCircle: React.FC<{ children: React.ReactNode; color: string }> = ({ children, color }) => (
  <div className="icon-circle" style={{ backgroundColor: color }}>
    {children}
  </div>
);

const Home: React.FC = () => {
  const { userInfo } = useAuth();
  const navigate = useNavigate();
  
  // 模拟当前日期和时间
  const currentDate = new Date();
  const options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric', 
    weekday: 'long' 
  };
  const dateString = currentDate.toLocaleDateString('zh-CN', options);
  
  // 模拟数据
  const learningDays = 7;
  const todayInteractions = 127;
  const todayStudyHours = 3.5;
  const averageGrade = "A+";

  // 快速入口配置
  const quickStartItems = [
    {
      title: "开始聊天",
      subtitle: "与AI助手开始对话",
      icon: <MessageOutlined />,
      path: "/college/chat",
      iconColor: '#4CAF50'
    },
    {
      title: "作业辅导",
      subtitle: "获取学习帮助",
      icon: <BookOutlined />,
      path: "/college/homework",
      iconColor: '#673AB7'
    },
    {
      title: "写作助手",
      subtitle: "论文写作指导",
      icon: <FileTextOutlined />,
      path: "/college/paper",
      iconColor: '#2196F3'
    },
    {
      title: "PPT制作",
      subtitle: "智能演示生成",
      icon: <FileOutlined />,
      path: "/college/ppt",
      iconColor: '#F44336'
    },
    {
      title: "降低AI",
      subtitle: "优化文档痕迹",
      icon: <SafetyOutlined />,
      path: "/college/ai-traces",
      iconColor: '#FF9800'
    }
  ];
  
  return (
    <div className="college-agent-home">
      {/* 欢迎区域 */}
      <Card className="welcome-card">
        <Row align="middle" justify="center">
          <Col xs={24} md={6} style={{ display: 'flex', justifyContent: 'center' }}>
            <img src={CollegeAgentLogo} alt="College Agent Logo" className="brain-icon" />
          </Col>
          <Col xs={24} md={18}>
            <Title level={2} className="welcome-title">
              欢迎回来，{userInfo?.realname || "同学"}！
            </Title>
            <Paragraph className="welcome-slogan">
              今天是学习的好时光，让我们一起进步吧 🚀
            </Paragraph>
            <Space size={24}>
              <div className="date-info">
                <CalendarOutlined className="info-icon" />
                {dateString}
              </div>
              <div className="streak-info">
                <ThunderboltOutlined className="info-icon" />
                连续学习 {learningDays} 天
              </div>
            </Space>
          </Col>
        </Row>
      </Card>
      
      {/* 统计卡片 */}
      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card className="stat-card">
            <div className="stat-card-inner">
              <IconCircle color="#eafbef">
                <MessageOutlined />
              </IconCircle>
              <div className="stat-content">
                <div className="stat-value">{todayInteractions}</div>
                <div className="stat-label">今日对话</div>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card className="stat-card">
            <div className="stat-card-inner">
              <IconCircle color="#e6f4ff">
                <ClockCircleOutlined />
              </IconCircle>
              <div className="stat-content">
                <div className="stat-value">{todayStudyHours}h</div>
                <div className="stat-label">今日学习</div>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card className="stat-card">
            <div className="stat-card-inner">
              <IconCircle color="#f5edff">
                <LineChartOutlined />
              </IconCircle>
              <div className="stat-content">
                <div className="stat-value">{learningDays}</div>
                <div className="stat-label">连续天数</div>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card className="stat-card">
            <div className="stat-card-inner">
              <IconCircle color="#fff7e6">
                <TrophyOutlined />
              </IconCircle>
              <div className="stat-content">
                <div className="stat-value">{averageGrade}</div>
                <div className="stat-label">平均成绩</div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 快速开始区域 */}
      <div className="quick-start-section">
        <Title level={4}>快速开始</Title>
        <Paragraph style={{ color: '#888' }}>选择一个功能开始你的学习之旅</Paragraph>
        
        <div className="quick-start-items-container">
          {quickStartItems.map((item, index) => (
            <div 
              className="quick-start-item"
              onClick={() => navigate(item.path)}
              key={index}
            >
              <div className="quick-start-icon" style={{ backgroundColor: item.iconColor }}>
                {item.icon}
              </div>
              <div className="quick-start-title">{item.title}</div>
              <div className="quick-start-subtitle">{item.subtitle}</div>
              {/* <div className="quick-start-arrow">›</div> */}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Home;
