import { HistoryOutlined } from '@ant-design/icons';
import { Button, Card, Space, Typography } from 'antd';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './PptGenerator.module.css';

const { Title, Paragraph } = Typography;

/**
 * PPT历史记录UI演示页面
 * 展示新的历史记录面板布局效果
 */
const UIDemo: React.FC = () => {
  const navigate = useNavigate();
  const [showHistory, setShowHistory] = useState(false);

  return (
    <div className={styles.pptPageContainer}>
      {/* 模拟历史记录面板 */}
      <div className={`${styles.pptHistoryPanel} ${showHistory ? styles.show : styles.hide}`}>
        <Card 
          title="历史记录面板" 
          style={{ height: '100%', margin: '16px' }}
          bodyStyle={{ height: 'calc(100% - 60px)', overflow: 'auto' }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>这是历史记录面板的内容区域</div>
            <div>面板从左侧滑出展开</div>
            <div>与论文写作助手页面保持一致的设计</div>
            <Button onClick={() => setShowHistory(false)}>关闭面板</Button>
          </Space>
        </Card>
      </div>

      <div className={styles.pptContainer}>
        {/* 页面标题 */}
        <div className={styles.pptTitle}>
          <div className={styles.titleLeft}>
            <div className={styles.titleText}>
              <Button
                type="default"
                icon={<HistoryOutlined />}
                onClick={() => setShowHistory(!showHistory)}
                className={styles.historyButton}
                title="查看历史记录"
              />
              AI PPT生成器
            </div>
            <div className={styles.titleDesc}>智能文档转PPT，快速生成专业演示文稿</div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div style={{ flex: 1, padding: '20px' }}>
          <Card title="UI布局演示" style={{ height: '100%' }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <Title level={4}>✅ 已实现的UI改进</Title>
                <ul>
                  <li>历史记录按钮移动到标题左侧（与论文页面一致）</li>
                  <li>移除模态框覆盖层设计</li>
                  <li>实现左侧滑出的面板布局</li>
                  <li>使用与论文页面相同的展开动画效果</li>
                  <li>保持历史记录列表组件功能不变</li>
                </ul>
              </div>

              <div>
                <Title level={4}>🎯 UI设计特点</Title>
                <ul>
                  <li><strong>面板位置</strong>：从页面左侧滑出，不覆盖主要内容</li>
                  <li><strong>动画效果</strong>：使用 transform translateX 实现平滑展开</li>
                  <li><strong>按钮位置</strong>：历史记录图标在标题左侧</li>
                  <li><strong>布局响应</strong>：展开时主要内容区域自动调整</li>
                </ul>
              </div>

              <div>
                <Title level={4}>🔧 技术实现</Title>
                <Paragraph>
                  <strong>CSS类名结构：</strong>
                  <br />
                  - <code>.pptPageContainer</code>：页面容器，flex布局
                  <br />
                  - <code>.pptHistoryPanel</code>：历史记录面板容器
                  <br />
                  - <code>.pptContainer</code>：主要内容容器
                  <br />
                  - <code>.show/.hide</code>：控制面板显示/隐藏状态
                </Paragraph>
              </div>

              <div>
                <Space>
                  <Button 
                    type="primary" 
                    onClick={() => setShowHistory(!showHistory)}
                  >
                    {showHistory ? '隐藏' : '显示'}历史记录面板
                  </Button>
                  <Button onClick={() => navigate('/college/ppt')}>
                    返回PPT生成页面
                  </Button>
                </Space>
              </div>
            </Space>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default UIDemo;
