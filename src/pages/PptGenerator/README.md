# AI PPT生成器

## 功能概述

AI PPT生成器是一个智能文档转PPT的功能模块，允许用户上传文档并自动生成专业的PPT演示文稿。

## 功能特性

### ✅ 已实现功能 (P0优先级)

1. **文件上传功能**
   - 支持文件格式：.doc, .docx, .txt, .pdf, .md, .markdown
   - 文件大小限制：最大2万字（约40MB）
   - 使用统一的FileUploadDragger组件，保持UI一致性
   - 完善的文件验证和错误提示

2. **PPT模板选择功能**
   - 提供3个预设学术PPT模板：
     - 学术经典模板 (academic_classic)
     - 现代研究模板 (research_modern)  
     - 商务专业模板 (business_professional)
   - 卡片式模板展示，支持选择和预览
   - 当前使用Mock数据，已标记便于后续替换

3. **PPT生成和预览功能**
   - 集成 `POST /api/ppt/generate` 接口
   - 支持文件和模板类型参数传递
   - 显示生成进度和状态反馈
   - 生成结果展示（文件信息、文档预览等）

4. **用户体验优化**
   - 响应式设计，适配不同屏幕尺寸
   - 完整的加载状态和错误处理
   - 统一的视觉风格，与现有页面保持一致

### 🚧 待实现功能 (P1优先级)

1. **下载功能**
   - PPT文件下载
   - 支持不同格式导出

2. **编辑功能**
   - 基础PPT编辑能力
   - 幻灯片内容修改

## 技术实现

### 组件结构

```
src/pages/PptGenerator/
├── PptGenerator.tsx          # 主组件
├── PptGenerator.module.css   # 样式文件
├── mockData.ts              # Mock数据（临时）
└── README.md                # 说明文档
```

### API集成

- **API文件**: `src/utils/api_ppt.ts`
- **类型定义**: `src/types/Ppt.ts`
- **主要接口**: `POST /api/ppt/generate`

### 路由配置

- **路径**: `/college/ppt`
- **菜单**: 已集成到主导航菜单
- **权限**: 使用统一的权限控制

## 使用说明

1. **上传文档**: 点击或拖拽上传支持格式的文档文件
2. **选择模板**: 从3个预设模板中选择适合的PPT样式
3. **生成PPT**: 点击生成按钮，系统将自动处理并生成PPT
4. **查看结果**: 生成完成后可查看文件信息和文档预览
5. **下载文件**: 点击下载按钮获取生成的PPT文件（待实现）
