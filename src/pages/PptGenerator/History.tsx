import { pptApi, PptHistoryItem } from '@/utils/api_ppt';
import { App, Button, Card, Input, Popconfirm, Spin, Tag, Tooltip } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { CloseOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import styles from './History.module.css';

// 状态颜色映射
const statusColorMap: { [key: string]: string } = {
  'pending': 'default',
  'processing': 'processing', 
  'completed': 'success',
  'failed': 'error',
  'cancelled': 'warning',
};

// 状态文本映射
const statusTextMap: { [key: string]: string } = {
  'pending': '等待中',
  'processing': '生成中',
  'completed': '已完成',
  'failed': '生成失败',
  'cancelled': '已取消',
};

interface HistoryPanelProps {
  onClose: () => void;
  shouldRefresh: boolean;
  onSelect: (pptId: string) => void;
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({ onClose, shouldRefresh, onSelect }) => {
  const { message } = App.useApp();
  
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [historyList, setHistoryList] = useState<PptHistoryItem[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [keyword, setKeyword] = useState('');
  
  const pageSize = 10;
  const listContainerRef = useRef<HTMLDivElement>(null);

  // 获取历史记录
  const fetchHistory = useCallback(async (isLoadMore = false, searchKeyword: string = '') => {
    // 使用函数式更新来获取最新的页码
    setPage(currentPage => {
      const pageToFetch = isLoadMore ? currentPage : 1;

      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        // 重置列表和分页状态
        setHistoryList([]);
        setHasMore(true);
      }

      pptApi
        .getPptHistory(pageToFetch, pageSize, searchKeyword)
        .then(res => {
          const items = res.items || [];
          console.log('PPT历史记录:', items);
          if (isLoadMore) {
            setHistoryList(prev => [...prev, ...items]);
          } else {
            setHistoryList(items);
          }

          if (items.length < pageSize) {
            setHasMore(false);
          }
        })
        .catch(error => {
          console.error('获取PPT历史记录失败:', error);
          message.error('获取历史记录失败');
        })
        .finally(() => {
          setLoading(false);
          setLoadingMore(false);
        });

      // 返回下一页的页码
      return isLoadMore ? currentPage + 1 : 2;
    });
  }, [message]);

  // 外部触发刷新
  useEffect(() => {
    if (shouldRefresh) {
      fetchHistory();
    }
  }, [shouldRefresh, fetchHistory]);

  // 无限滚动监听
  useEffect(() => {
    const container = listContainerRef.current;
    const handleScroll = () => {
      if (!container || loadingMore || !hasMore) return;
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollHeight - scrollTop - clientHeight < 100) {
        fetchHistory(true, keyword);
      }
    };
    container?.addEventListener('scroll', handleScroll);
    return () => container?.removeEventListener('scroll', handleScroll);
  }, [loadingMore, hasMore, fetchHistory, keyword]);

  // 删除PPT历史记录
  const handleDelete = async (pptId: string) => {
    try {
      await pptApi.deletePptHistory(pptId);
      message.success('删除成功');
      fetchHistory(false, keyword); // 删除后刷新列表
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 搜索处理
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      fetchHistory(false, keyword);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={styles.historyPanel}>
      <div className={styles.historyHeader}>
        <div className={styles.headerTitle}>
          历史记录
        </div>
        <Button 
          type="text" 
          icon={<CloseOutlined />} 
          onClick={onClose} 
          className={styles.closeButton}
        />
      </div>
      <div className={styles.toolbar}>
        <Input 
          placeholder="搜索历史记录"
          prefix={<SearchOutlined />}
          className={styles.searchInput}
          value={keyword}
          onChange={e => setKeyword(e.target.value)}
          onKeyPress={handleKeyPress}
          allowClear
        />
      </div>
      <div className={styles.historyList} ref={listContainerRef}>
        {loading && historyList.length === 0 ? (
          // 占位卡片
          Array.from({ length: 3 }).map((_, index) => 
            <Card key={index} loading style={{ marginBottom: 12 }} />
          )
        ) : (
          // 实际列表
          historyList.map(item => (
            <div 
              key={item.id} 
              className={styles.historyCard}
              onClick={() => onSelect(item.id)}
            >
              <div className={styles.cardHeader}>
                <div className={styles.cardTitle}>
                  {item.original_filename || '未知文件'}
                </div>
                <Tooltip title="删除">
                  <Popconfirm
                    title="确定要删除此PPT记录吗？"
                    onConfirm={e => {
                      e?.stopPropagation();
                      handleDelete(item.id);
                    }}
                    okText="确定"
                    cancelText="取消"
                    onCancel={e => {
                      e?.stopPropagation();
                    }}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      className={styles.deleteButton}
                      onClick={e => {
                        e.stopPropagation();
                      }}
                    />
                  </Popconfirm>
                </Tooltip>
              </div>
              <div className={styles.cardInfo}>
                <div className={styles.cardInfoItem}>
                  <span className={styles.cardInfoLabel}>文件大小:</span>
                  <span className={styles.cardInfoValue}>{formatFileSize(item.file_size)}</span>
                </div>
                <div className={styles.cardInfoItem}>
                  <span className={styles.cardInfoLabel}>模板类型:</span>
                  <span className={styles.cardInfoValue}>{item.model_type}</span>
                </div>
                <div className={styles.cardInfoItem}>
                  <span className={styles.cardInfoLabel}>下载次数:</span>
                  <span className={styles.cardInfoValue}>{item.download_count}</span>
                </div>
              </div>
              <div className={styles.cardFooter}>
                <div className={styles.cardTimestamp}>
                  {dayjs(item.created_at).format('YYYY/MM/DD HH:mm:ss')}
                </div>
                <Tag color={statusColorMap[item.status] || 'default'}>
                  {statusTextMap[item.status] || item.status}
                </Tag>
              </div>
            </div>
          ))
        )}
        {/* 加载更多指示器 */}
        {loadingMore && (
          <div style={{ textAlign: 'center', padding: '10px' }}>
            <Spin />
          </div>
        )}
        {/* 无更多数据提示 */}
        {!hasMore && historyList.length > 0 && (
          <div style={{ textAlign: 'center', padding: '10px', color: '#999', fontSize: 12 }}>
            已加载全部记录
          </div>
        )}
        {/* 空状态 */}
        {!loading && historyList.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px 20px', color: '#999' }}>
            暂无历史记录
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryPanel;
