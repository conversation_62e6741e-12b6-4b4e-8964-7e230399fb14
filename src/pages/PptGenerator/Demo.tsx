import { pptApi } from '@/utils/api_ppt';
import { But<PERSON>, Card, Divider, message, Space, Steps, Typography } from 'antd';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph, Text } = Typography;

/**
 * PPT历史记录系统演示页面
 * 展示完整的功能流程和使用方法
 */
const PptHistoryDemo: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [demoData, setDemoData] = useState<any>(null);

  // 演示步骤
  const steps = [
    {
      title: '访问PPT生成页面',
      description: '进入PPT生成功能主页面',
    },
    {
      title: '查看历史记录',
      description: '点击历史记录按钮查看过往生成记录',
    },
    {
      title: '选择历史记录',
      description: '点击历史记录项查看详情',
    },
    {
      title: '下载或重新生成',
      description: '在详情页面进行相关操作',
    },
  ];

  // 模拟获取历史记录数据
  const simulateGetHistory = async () => {
    try {
      message.loading('正在获取历史记录...', 1);
      
      // 模拟API调用
      const mockData = {
        items: [
          {
            id: 'ppt_001',
            original_filename: '学术论文_机器学习研究.docx',
            ppt_filename: '机器学习研究_presentation.pptx',
            file_type: 'docx',
            model_type: 'academic_classic',
            status: 'completed',
            file_size: 2048576,
            created_at: '2024-01-15T10:30:00Z',
            updated_at: '2024-01-15T10:35:00Z',
            download_count: 3
          },
          {
            id: 'ppt_002',
            original_filename: '项目报告_AI应用案例.pdf',
            ppt_filename: 'AI应用案例_presentation.pptx',
            file_type: 'pdf',
            model_type: 'research_modern',
            status: 'completed',
            file_size: 3145728,
            created_at: '2024-01-14T15:20:00Z',
            updated_at: '2024-01-14T15:28:00Z',
            download_count: 1
          }
        ],
        total: 2,
        page: 1,
        page_size: 10,
        has_more: false
      };
      
      setDemoData(mockData);
      message.success('历史记录获取成功');
      setCurrentStep(1);
    } catch (error) {
      message.error('获取历史记录失败');
    }
  };

  // 跳转到PPT生成页面
  const goToPptGenerator = () => {
    navigate('/college/ppt');
  };

  // 跳转到PPT详情页面
  const goToPptDetail = (pptId: string) => {
    navigate(`/college/ppt?pptid=${pptId}`);
  };

  // 模拟选择历史记录
  const selectHistoryItem = (item: any) => {
    message.info(`选择了历史记录: ${item.original_filename}`);
    setCurrentStep(2);
  };

  // 模拟下载操作
  const simulateDownload = () => {
    message.loading('正在下载PPT文件...', 2);
    setTimeout(() => {
      message.success('PPT文件下载成功');
      setCurrentStep(3);
    }, 2000);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>PPT历史记录系统演示</Title>
      
      <Paragraph>
        这是一个完整的PPT历史记录系统演示，展示了从查看历史记录到下载PPT文件的完整流程。
      </Paragraph>

      {/* 功能步骤 */}
      <Card title="功能流程" style={{ marginBottom: '24px' }}>
        <Steps
          current={currentStep}
          items={steps}
          direction="horizontal"
          size="small"
        />
      </Card>

      {/* 快速导航 */}
      <Card title="快速导航" style={{ marginBottom: '24px' }}>
        <Space wrap>
          <Button type="primary" onClick={goToPptGenerator}>
            进入PPT生成页面
          </Button>
          <Button onClick={() => goToPptDetail('demo_ppt_001')}>
            查看PPT详情示例
          </Button>
          <Button onClick={simulateGetHistory}>
            模拟获取历史记录
          </Button>
        </Space>
      </Card>

      {/* 演示数据 */}
      {demoData && (
        <Card title="历史记录演示数据" style={{ marginBottom: '24px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {demoData.items.map((item: any, index: number) => (
              <Card
                key={item.id}
                size="small"
                hoverable
                onClick={() => selectHistoryItem(item)}
                style={{ cursor: 'pointer' }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text strong>{item.original_filename}</Text>
                    <br />
                    <Text type="secondary">
                      模板: {item.model_type} | 状态: {item.status} | 
                      大小: {(item.file_size / 1024 / 1024).toFixed(2)} MB
                    </Text>
                  </div>
                  <Space>
                    <Button size="small" onClick={(e) => {
                      e.stopPropagation();
                      goToPptDetail(item.id);
                    }}>
                      查看详情
                    </Button>
                    <Button size="small" type="primary" onClick={(e) => {
                      e.stopPropagation();
                      simulateDownload();
                    }}>
                      下载
                    </Button>
                  </Space>
                </div>
              </Card>
            ))}
          </Space>
        </Card>
      )}

      <Divider />

      {/* 功能特性说明 */}
      <Card title="功能特性" style={{ marginBottom: '24px' }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
          <Card size="small" title="🏠 历史记录入口">
            <Paragraph>
              在PPT生成页面添加历史记录图标按钮，点击后展开历史记录列表
            </Paragraph>
          </Card>
          
          <Card size="small" title="📋 历史记录列表">
            <Paragraph>
              实现带无限滚动分页的历史记录列表组件，支持搜索和删除功能
            </Paragraph>
          </Card>
          
          <Card size="small" title="📄 详情页面">
            <Paragraph>
              点击历史记录项跳转到详情展示页面，复用现有PPT结果展示页面的UI
            </Paragraph>
          </Card>
          
          <Card size="small" title="🔗 URL路由">
            <Paragraph>
              实现路由支持，可通过URL参数直接访问PPT详情页面
            </Paragraph>
          </Card>
          
          <Card size="small" title="⬇️ 下载功能">
            <Paragraph>
              支持通过pptid下载PPT文件，处理二进制流响应
            </Paragraph>
          </Card>
          
          <Card size="small" title="🔄 重新生成">
            <Paragraph>
              在详情页面支持重新生成PPT功能（预留接口）
            </Paragraph>
          </Card>
        </div>
      </Card>

      {/* 技术实现说明 */}
      <Card title="技术实现" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>API接口:</Text>
            <ul>
              <li><code>GET /api/ppt/history</code> - 获取PPT生成历史记录（分页）</li>
              <li><code>GET /api/ppt/history/:id</code> - 获取PPT历史记录详情</li>
              <li><code>GET /api/ppt/download/by-id/:id</code> - 下载PPT文件</li>
              <li><code>DELETE /api/ppt/history/:id</code> - 删除PPT历史记录</li>
            </ul>
          </div>
          
          <div>
            <Text strong>组件结构:</Text>
            <ul>
              <li><code>PptRouter.tsx</code> - PPT路由组件，根据URL参数决定显示页面</li>
              <li><code>PptGeneratorWithHistory.tsx</code> - 带历史记录功能的PPT生成组件</li>
              <li><code>History.tsx</code> - 历史记录列表组件</li>
              <li><code>PptDetail.tsx</code> - PPT详情页面组件</li>
            </ul>
          </div>
          
          <div>
            <Text strong>数据流:</Text>
            <ol>
              <li>用户在PPT生成页面点击历史记录按钮</li>
              <li>调用历史记录API获取数据并展示列表</li>
              <li>用户点击历史记录项，跳转到详情页面</li>
              <li>详情页面根据pptid获取详细信息</li>
              <li>用户可以下载PPT文件或重新生成</li>
            </ol>
          </div>
        </Space>
      </Card>

      {/* 使用说明 */}
      <Card title="使用说明">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>1. 查看历史记录</Text>
            <Paragraph>
              在PPT生成页面点击右上角的"历史记录"按钮，历史记录面板会以模态框形式展开。
              支持搜索功能和无限滚动加载。
            </Paragraph>
          </div>
          
          <div>
            <Text strong>2. 查看PPT详情</Text>
            <Paragraph>
              在历史记录列表中点击任意记录，会自动跳转到PPT详情页面。
              URL格式为：<code>/college/ppt?pptid=xxx</code>
            </Paragraph>
          </div>
          
          <div>
            <Text strong>3. 下载PPT文件</Text>
            <Paragraph>
              在PPT详情页面点击"下载PPT"按钮，系统会调用下载API获取文件并自动触发浏览器下载。
            </Paragraph>
          </div>
          
          <div>
            <Text strong>4. 重新生成PPT</Text>
            <Paragraph>
              在PPT详情页面点击"重新生成"按钮，可以基于原始文件和模板重新生成PPT。
              （当前显示"功能开发中"提示）
            </Paragraph>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default PptHistoryDemo;
