import FileUploadDragger from '@/components/FileUploadDragger';
import { pptApi } from '@/utils/api_ppt';
import {
  DownloadOutlined,
  LoadingOutlined,
  PlayCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { App, <PERSON><PERSON>, Modal } from 'antd';
import React, { useState } from 'react';
import { mockPptTemplates } from './mockData';
import styles from './PptGenerator.module.css';

// PPT模板接口
interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: string;
}

// 上传文件接口
interface UploadedFile {
  file: File;
  name: string;
  size: number;
}

// PPT页面预览接口
interface PptPagePreview {
  page_number: number;
  thumbnail_url: string;
  title?: string;
}

// PPT生成结果接口
interface PptGenerateResult {
  id: string;
  file_path: string;
  filename: string;
  size: number;
  download_url: string;
  preview_url: string;
  document_content: string;
  generation_time: number;
  pages?: PptPagePreview[]; // 分页预览信息
  total_pages?: number; // 总页数
}

const PptGenerator: React.FC = () => {
  const { message } = App.useApp();
  // const navigate = useNavigate(); // 暂时注释，避免导入错误

  // 状态管理
  const [isUploading, setIsUploading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<PptTemplate | null>(null);
  const [generateResult, setGenerateResult] = useState<PptGenerateResult | null>(null);
  const [selectedPagePreview, setSelectedPagePreview] = useState<PptPagePreview | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  /** 处理文件上传 */
  const handleFileUpload = (file: File, fileList: File[]) => {
    if (isUploading) {
      message.warning("文件上传中，请稍后再试");
      return false;
    }

    if (isGenerating) {
      message.warning("PPT生成中，请稍后再试");
      return false;
    }

    // 文件大小检查（假设2万字约等于40MB，这里简单检查文件大小）
    const maxSize = 40 * 1024 * 1024; // 40MB
    if (file.size > maxSize) {
      message.error("文件过大，请确保文档内容不超过2万字");
      return false;
    }

    // 文件名长度检查
    if (file.name.length > 100) {
      message.error("文件名过长，请修改后重新上传（100字符以内）");
      return false;
    }

    console.log("准备上传PPT文档文件:", file.name);

    // 设置上传的文件信息
    setUploadedFile({
      file: file,
      name: file.name,
      size: file.size
    });

    message.success(`${file.name} 上传成功`);

    // 清除之前的生成结果
    setGenerateResult(null);

    return false; // 阻止默认上传行为
  };

  /** 处理文件删除 */
  const handleFileDelete = () => {
    setUploadedFile(null);
    setGenerateResult(null);
    message.success("文件已删除");
  };

  /** 处理页面预览点击 */
  const handlePagePreviewClick = (page: PptPagePreview) => {
    setSelectedPagePreview(page);
  };

  /** 关闭页面预览 */
  const handleClosePagePreview = () => {
    setSelectedPagePreview(null);
  };

  /** 选择PPT模板 */
  const handleTemplateSelect = (template: PptTemplate) => {
    setSelectedTemplate(template);
    message.success(`已选择模板：${template.name}`);
  };

  /** 生成PPT */
  const handleGeneratePpt = async () => {
    if (!uploadedFile) {
      message.error("请先上传文档文件");
      return;
    }
    
    if (!selectedTemplate) {
      message.error("请先选择PPT模板");
      return;
    }

    try {
      setIsGenerating(true);
      console.log("开始生成PPT:", uploadedFile.name, selectedTemplate.type);
      
      const formData = new FormData();
      formData.append("file", uploadedFile.file);
      formData.append("ppt_type", selectedTemplate.type);
      
      const result = await pptApi.generatePpt(formData);
      console.log("PPT生成成功:", result);


      const resultWithPages = {
        ...result
      };

      setGenerateResult(resultWithPages);
      message.success("PPT生成成功！");
      
    } catch (error) {
      console.error("PPT生成失败:", error);
      message.error("PPT生成失败，请稍后重试");
    } finally {
      setIsGenerating(false);
    }
  };

  /** 重新生成 */
  const handleRegenerate = () => {
    setGenerateResult(null);
    handleGeneratePpt();
  };

  /** 下载PPT */
  const handleDownload = async () => {
    if (!generateResult) {
      message.error("暂无可下载的PPT文件");
      return;
    }

    if (isDownloading) {
      message.warning("文件下载中，请稍后再试");
      return;
    }

    try {
      setIsDownloading(true);
      console.log("开始下载PPT文件:", generateResult.id);

      // 调用新的下载API
      const { blob, filename } = await pptApi.downloadPptById(generateResult.id);

      // 创建临时下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename || generateResult.filename || 'presentation.pptx';
      link.style.display = 'none';

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理临时链接
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success("PPT文件下载成功");
    } catch (error) {
      console.error("PPT下载失败:", error);

      // 根据错误类型显示不同的错误信息
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          message.error("PPT文件不存在，请重新生成");
        } else if (error.message.includes('403')) {
          message.error("没有权限下载此文件");
        } else if (error.message.includes('Network')) {
          message.error("网络连接失败，请检查网络后重试");
        } else {
          message.error("下载失败，请稍后重试");
        }
      } else {
        message.error("下载失败，请稍后重试");
      }
    } finally {
      setIsDownloading(false);
    }
  };

  /** 处理历史记录选择 */
  const handleHistorySelect = (pptId: string) => {
    // 跳转到PPT详情页面
    window.location.href = `/college/ppt?pptid=${pptId}`;
    // 自动关闭历史面板
    setShowHistory(false);
  };

  return (
    <div className={styles.pptContainer}>
      {/* 历史记录面板 */}
      {showHistory && (
        <div className={styles.historyPanelOverlay}>
          <div className={styles.historyPanelContainer}>
            {/* <HistoryPanel
              onClose={() => setShowHistory(false)}
              shouldRefresh={showHistory}
              onSelect={handleHistorySelect}
            /> */}
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <div>历史记录功能开发中</div>
              <Button onClick={() => setShowHistory(false)} style={{ marginTop: '10px' }}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 页面标题 */}
      <div className={styles.pptTitle}>
        <div className={styles.titleLeft}>
          <div className={styles.titleText}>AI PPT生成器</div>
          <div className={styles.titleDesc}>智能文档转PPT，快速生成专业演示文稿</div>
        </div>
        <div className={styles.titleRight}>
          {/* <Tooltip title="查看历史记录">
            <Button
              type="default"
              icon={<HistoryOutlined />}
              onClick={() => setShowHistory(!showHistory)}
              className={styles.historyButton}
            >
              历史记录
            </Button>
          </Tooltip> */}
          <Button
            type="default"
            onClick={() => setShowHistory(!showHistory)}
            className={styles.historyButton}
          >
            📋 历史记录
          </Button>
        </div>
      </div>

      <div className={styles.pptContent}>
        {/* 左侧：文件上传和模板选择 */}
        <div className={styles.pptLeft}>
          {/* 文件上传区域 */}
          <FileUploadDragger
            title="上传文档文件"
            description={
              <>
                支持 Word文档（DOC、DOCX）、PDF文件、文本文件（TXT）、Markdown文件（MD）
                <br />
                文件大小限制：最大2万字
              </>
            }
            accept=".doc,.docx,.txt,.pdf,.md,.markdown"
            onUpload={handleFileUpload}
            disabled={isUploading || isGenerating}
            isUploading={isUploading}
            uploadingText="正在上传文档，请稍候..."
            multiple={false}
            showUploadedFile={true}
            uploadedFile={uploadedFile ? { name: uploadedFile.name, size: uploadedFile.size } : null}
            onFileDelete={handleFileDelete}
            showReplaceConfirm={true}
            singleFileHint="一次PPT任务只能上传一个文件"
            disableDelete={isGenerating}
          />

          {/* PPT模板选择区域 */}
          <div className={styles.templateContainer}>
            <div className={styles.templateTitle}>选择PPT模板</div>
            <div className={styles.templateDesc}>
              选择适合的学术PPT模板，系统将根据您的文档内容自动生成演示文稿
            </div>
            
            <div className={styles.templateList}>
              {mockPptTemplates.map((template) => (
                <div
                  key={template.id}
                  className={`${styles.templateItem} ${
                    selectedTemplate?.id === template.id ? styles.templateItemSelected : ''
                  } ${isGenerating ? styles.templateItemDisabled : ''}`}
                  onClick={() => !isGenerating && handleTemplateSelect(template)}
                >
                  <div className={styles.templateIcon}>
                    <img src={template.thumbnail} alt={template.name} />
                  </div>
                  <div className={styles.templateInfo}>
                    <div className={styles.templateName}>{template.name}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧：PPT预览和操作 */}
        <div className={styles.pptMain}>
          <div className={styles.mainContainer}>
            <div className={styles.mainHeader}>
              <div className={styles.mainHeaderInfo}>
                <div className={styles.mainHeaderTitle}>PPT预览</div>
                <div className={styles.mainHeaderDesc}>
                  {generateResult ?
                    `共 ${generateResult.total_pages || 0} 页，点击页面可放大查看` :
                    "查看生成的PPT文件信息和预览"
                  }
                </div>
              </div>

              {generateResult && (
                <div className={styles.headerActions}>
                  <Button
                    className={styles.regenerateButton}
                    icon={<SyncOutlined />}
                    onClick={handleRegenerate}
                    loading={isGenerating}
                  >
                    重新生成PPT
                  </Button>
                  <Button
                    type="primary"
                    className={styles.downloadButton}
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                    loading={isDownloading}
                    disabled={isDownloading}
                  >
                    {isDownloading ? '下载中...' : '导出PPT'}
                  </Button>
                </div>
              )}
            </div>

            {/* 动态内容区域 */}
            <div className={styles.mainContent}>
              {isGenerating ? (
                <div className={styles.generatingContainer}>
                  <LoadingOutlined className={styles.generatingIcon} />
                  <div className={styles.generatingTitle}>正在生成PPT...</div>
                  <div className={styles.generatingDesc}>
                    AI正在分析您的文档内容并生成专业的PPT演示文稿，请稍候
                  </div>
                  <div className={styles.generatingWarning}>
                    ⚠️ 请勿离开此页面，生成过程中离开可能导致任务中断
                  </div>
                </div>
              ) : generateResult ? (
                <div className={styles.resultContainer}>
                  {/* PPT页面预览网格 */}
                  {generateResult.pages && generateResult.pages.length > 0 ? (
                    <div className={styles.pagesGrid}>
                      {generateResult.pages.map((page) => (
                        <div
                          key={page.page_number}
                          className={styles.pageCard}
                          onClick={() => handlePagePreviewClick(page)}
                        >
                          <div className={styles.pageCardThumbnail}>
                            <img
                              src={page.thumbnail_url}
                              alt={`第${page.page_number}页`}
                              className={styles.pageCardImage}
                            />
                          </div>
                          <div className={styles.pageCardInfo}>
                            <div className={styles.pageCardNumber}>第{page.page_number}页</div>
                            {page.title && (
                              <div className={styles.pageCardTitle}>{page.title}</div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className={styles.noPreviewContainer}>
                      <div className={styles.noPreviewIcon}>📄</div>
                      <div className={styles.noPreviewTitle}>暂无页面预览</div>
                      <div className={styles.noPreviewDesc}>
                        PPT已生成完成，但暂时无法显示页面预览
                      </div>
                      <div className={styles.resultInfo}>
                        <div className={styles.resultInfoItem}>
                          <span className={styles.resultInfoLabel}>文件名：</span>
                          <span className={styles.resultInfoValue}>{generateResult.filename}</span>
                        </div>
                        <div className={styles.resultInfoItem}>
                          <span className={styles.resultInfoLabel}>文件大小：</span>
                          <span className={styles.resultInfoValue}>
                            {(generateResult.size / 1024 / 1024).toFixed(2)} MB
                          </span>
                        </div>
                        <div className={styles.resultInfoItem}>
                          <span className={styles.resultInfoLabel}>生成耗时：</span>
                          <span className={styles.resultInfoValue}>{generateResult.generation_time} 秒</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className={styles.emptyContainer}>
                  <div className={styles.emptyIcon}>
                    <PlayCircleOutlined />
                  </div>
                  <div className={styles.emptyTitle}>准备生成PPT</div>
                  <div className={styles.emptyDesc}>
                    请上传文档文件并选择PPT模板，然后点击生成按钮
                  </div>
                  
                  {uploadedFile && selectedTemplate && (
                    <Button
                      type="primary"
                      size="large"
                      icon={<PlayCircleOutlined />}
                      onClick={handleGeneratePpt}
                      loading={isGenerating}
                      className={styles.generateButton}
                    >
                      生成PPT
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 页面预览Modal */}
      <Modal
        title={selectedPagePreview ? `第${selectedPagePreview.page_number}页预览` : '页面预览'}
        open={!!selectedPagePreview}
        onCancel={handleClosePagePreview}
        footer={null}
        width={800}
        centered
        className={styles.pagePreviewModal}
      >
        {selectedPagePreview && (
          <div className={styles.pagePreviewContent}>
            <img
              src={selectedPagePreview.thumbnail_url}
              alt={`第${selectedPagePreview.page_number}页`}
              className={styles.pagePreviewImage}
            />
            {selectedPagePreview.title && (
              <div className={styles.pagePreviewTitle}>
                {selectedPagePreview.title}
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PptGenerator;
