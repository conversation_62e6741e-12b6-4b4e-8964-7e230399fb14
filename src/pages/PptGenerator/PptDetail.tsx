import { pptApi, PptHistoryItem } from '@/utils/api_ppt';
import {
    ArrowLeftOutlined,
    DownloadOutlined,
    LoadingOutlined,
    SyncOutlined
} from '@ant-design/icons';
import { App, <PERSON><PERSON>, Card, Spin, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import styles from './PptDetail.module.css';

// 状态颜色映射
const statusColorMap: { [key: string]: string } = {
  'pending': 'default',
  'processing': 'processing',
  'completed': 'success',
  'failed': 'error',
  'cancelled': 'warning',
};

// 状态文本映射
const statusTextMap: { [key: string]: string } = {
  'pending': '等待中',
  'processing': '生成中',
  'completed': '已完成',
  'failed': '生成失败',
  'cancelled': '已取消',
};

// PPT模板类型映射
const pptTypeMap: { [key: string]: string } = {
  'simple': '简约模板',
  'creative': '创意模板',
  'graduation': '毕业答辩模板',
  'academic_classic': '学术经典模板',
  'research_modern': '现代研究模板',
  'business_professional': '商务专业模板',
};

const PptDetail: React.FC = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const pptId = searchParams.get('pptid');

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [pptDetail, setPptDetail] = useState<PptHistoryItem | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);

  // 获取PPT详情
  const fetchPptDetail = async () => {
    if (!pptId) {
      message.error('PPT ID参数缺失');
      navigate('/college/ppt');
      return;
    }

    try {
      setLoading(true);
      const detail = await pptApi.getPptHistoryById(pptId);
      setPptDetail(detail);
      console.log('PPT详情:', detail);
    } catch (error) {
      console.error('获取PPT详情失败:', error);
      message.error('获取PPT详情失败');
      navigate('/college/ppt');
    } finally {
      setLoading(false);
    }
  };

  // 模拟下载PPT文件（仅UI展示）
  const handleDownload = async () => {
    if (!pptId || !pptDetail) {
      message.error('无法下载，PPT信息缺失');
      return;
    }

    try {
      setIsDownloading(true);
      console.log('模拟下载PPT:', pptId);

      // 调用模拟下载API
      const { filename, size } = await pptApi.simulateDownloadPpt(pptId);

      // 显示下载信息（仅UI展示，不实际下载）
      message.success(`模拟下载成功: ${filename} (${formatFileSize(size)})`);
      console.log('PPT文件信息:', { filename, size, path: pptDetail.ppt_file_path });

    } catch (error) {
      console.error('模拟下载失败:', error);
      message.error('模拟下载失败，请稍后重试');
    } finally {
      setIsDownloading(false);
    }
  };

  // 重新生成PPT
  const handleRegenerate = async () => {
    if (!pptDetail) {
      message.error('无法重新生成，PPT信息缺失');
      return;
    }

    try {
      setIsRegenerating(true);
      message.info('重新生成功能开发中，敬请期待');
      // TODO: 实现重新生成逻辑
      // 需要获取原始文件和模板信息，重新调用生成接口
    } catch (error) {
      console.error('重新生成失败:', error);
      message.error('重新生成失败，请稍后重试');
    } finally {
      setIsRegenerating(false);
    }
  };

  // 返回PPT生成页面
  const handleBack = () => {
    navigate('/college/ppt');
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  useEffect(() => {
    fetchPptDetail();
  }, [pptId]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
        <div className={styles.loadingText}>正在加载PPT详情...</div>
      </div>
    );
  }

  if (!pptDetail) {
    return (
      <div className={styles.errorContainer}>
        <div className={styles.errorText}>PPT详情加载失败</div>
        <Button type="primary" onClick={handleBack}>
          返回PPT生成页面
        </Button>
      </div>
    );
  }

  return (
    <div className={styles.pptDetailContainer}>
      {/* 头部导航 */}
      <div className={styles.header}>
        <Button 
          type="text" 
          icon={<ArrowLeftOutlined />} 
          onClick={handleBack}
          className={styles.backButton}
        >
          返回
        </Button>
        <div className={styles.headerTitle}>PPT详情</div>
        <div className={styles.headerActions}>
          <Button
            type="default"
            icon={<SyncOutlined />}
            onClick={handleRegenerate}
            loading={isRegenerating}
            disabled={pptDetail.status !== 'completed'}
          >
            重新生成
          </Button>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleDownload}
            loading={isDownloading}
            disabled={pptDetail.status !== 'completed'}
          >
            下载PPT
          </Button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className={styles.content}>
        <div className={styles.pptInfo}>
          <Card title="PPT信息" className={styles.infoCard}>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>原始文件名:</span>
                <span className={styles.infoValue}>{pptDetail.original_filename}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>PPT文件名:</span>
                <span className={styles.infoValue}>{pptDetail.ppt_filename}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>原始文件类型:</span>
                <span className={styles.infoValue}>{pptDetail.file_type}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>PPT模板类型:</span>
                <span className={styles.infoValue}>{pptTypeMap[pptDetail.ppt_type] || pptDetail.ppt_type}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>PPT文件大小:</span>
                <span className={styles.infoValue}>{formatFileSize(pptDetail.file_size)}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>生成状态:</span>
                <Tag color={statusColorMap[pptDetail.status] || 'default'}>
                  {statusTextMap[pptDetail.status] || pptDetail.status}
                </Tag>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>原始文件路径:</span>
                <span className={styles.infoValue} title={pptDetail.original_file_path}>
                  {pptDetail.original_file_path.length > 50
                    ? `...${pptDetail.original_file_path.slice(-50)}`
                    : pptDetail.original_file_path}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>PPT文件路径:</span>
                <span className={styles.infoValue} title={pptDetail.ppt_file_path}>
                  {pptDetail.ppt_file_path.length > 50
                    ? `...${pptDetail.ppt_file_path.slice(-50)}`
                    : pptDetail.ppt_file_path}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>创建时间:</span>
                <span className={styles.infoValue}>
                  {dayjs(pptDetail.created_at).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>更新时间:</span>
                <span className={styles.infoValue}>
                  {dayjs(pptDetail.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
            </div>
            
            {/* 错误信息显示 */}
            {pptDetail.error_message && (
              <div className={styles.errorMessage}>
                <div className={styles.errorTitle}>错误信息:</div>
                <div className={styles.errorContent}>{pptDetail.error_message}</div>
              </div>
            )}
          </Card>
        </div>

        {/* PPT预览区域 - 预留给后续功能 */}
        <div className={styles.pptPreview}>
          <Card title="PPT预览" className={styles.previewCard}>
            <div className={styles.previewPlaceholder}>
              <div className={styles.previewIcon}>
                <LoadingOutlined />
              </div>
              <div className={styles.previewText}>PPT预览功能开发中</div>
              <div className={styles.previewDesc}>
                后续版本将支持在线预览PPT内容
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PptDetail;
