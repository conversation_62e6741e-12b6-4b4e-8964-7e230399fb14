/* AI PPT生成器页面样式 */

/* 页面容器 */
.pptPageContainer {
  width: 100%;
  height: 100vh;
  flex: 1;
  display: flex;
  flex-direction: row;
  padding: 16px;
  background-color: #EEF3FF;
  overflow: hidden;
}

/* 主容器 */
.pptContainer {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-right 0.3s ease-in-out;
}

/* 顶部标题 */
.pptTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: fit-content;
  margin-bottom: 16px;
}

.titleLeft {
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleText {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleDesc {
  font-size: 16px;
  color: #555;
}

.titleRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.historyButton {
  border-radius: 6px;
  font-weight: 500;
  color: #666;
}

/* 历史记录面板 */
.pptHistoryPanel {
  width: 25%;
  height: 100%;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  overflow: hidden;
  z-index: 10;
}

.pptHistoryPanel.show {
  opacity: 1;
  transform: translateX(0);
  margin-right: 16px;

  transition:
    width 0.3s ease,
    margin-right 0.2s ease,
    transform 0.3s ease,
    opacity 0.2s ease;
}

.pptHistoryPanel.hide {
  width: 0px;
  opacity: 0;
  transform: translateX(-100%);
  margin-right: 0px;

  transition:
    width 0.3s ease,
    margin-right 0.2s ease,
    transform 0.3s ease,
    opacity 0.2s ease;
}

/* 内容区域 */
.pptContent {
  display: flex;
  flex-direction: row;
  gap: 16px;
  width: 100%;
  height: calc(100vh - 215px);
}

.pptLeft {
  width: 30%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pptMain {
  width: 70%;
  height: 100%;
}

/* PPT模板选择区域 */
.templateContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  flex: 1;
  overflow: hidden;
}

.templateTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.templateDesc {
  font-size: 14px;
  color: #555;
  margin-bottom: 16px;
}

.templateList {
  display: flex;
  flex-direction: row;
  gap: 12px;
  overflow-x: auto;
  overflow-y: hidden;
  flex: 1;
  padding: 8px 0;
}

.templateItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  flex-shrink: 0;
  background-color: #fafafa;
}

.templateItem:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  background-color: #f0f8ff;
}

.templateItemSelected {
  border-color: #1890ff !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  background-color: #e6f7ff;
}

.templateIcon {
  width: 60px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e5e5;
}

.templateIcon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.templateInfo {
  text-align: center;
}

.templateName {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  word-break: break-all;
}

/* 模板禁用状态 */
.templateItemDisabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.templateItemDisabled:hover {
  border-color: transparent !important;
  box-shadow: none !important;
  background-color: #fafafa !important;
}

.templateItemDisabled .templateIcon {
  opacity: 0.6;
}

.templateItemDisabled .templateName {
  color: #999 !important;
}

/* 右侧主内容区域 */
.mainContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.mainHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: fit-content;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 16px;
  margin-bottom: 20px;
}

.mainHeaderInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mainHeaderTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.mainHeaderDesc {
  font-size: 14px;
  color: #666;
}

.headerActions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.regenerateButton {
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
}

.downloadButton {
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
}

.mainContent {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* 生成中状态 */
.generatingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
}

.generatingIcon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.generatingTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.generatingDesc {
  font-size: 14px;
  color: #666;
  max-width: 400px;
}

.generatingWarning {
  font-size: 14px;
  color: #ff7a00;
  font-weight: 500;
  margin-top: 16px;
  padding: 12px 16px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  max-width: 400px;
}

/* PPT页面预览网格 */
.pagesGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  width: 100%;
}

.pageCard {
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pageCard:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.pageCardThumbnail {
  width: 100%;
  height: 200px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pageCardImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.pageCardInfo {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
}

.pageCardNumber {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.pageCardTitle {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 无预览状态 */
.noPreviewContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  min-height: 300px;
}

.noPreviewIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.noPreviewTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.noPreviewDesc {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  max-width: 400px;
}

/* 页面预览Modal */
.pagePreviewModal .ant-modal-body {
  padding: 20px;
}

.pagePreviewContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.pagePreviewImage {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagePreviewTitle {
  margin-top: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 生成结果 */
.resultContainer {
  width: 100%;
  padding: 20px;
}

.resultHeader {
  text-align: center;
  margin-bottom: 24px;
}

.resultTitle {
  font-size: 20px;
  font-weight: bold;
  color: #52c41a;
  margin-bottom: 8px;
}

.resultMessage {
  font-size: 14px;
  color: #666;
}

.resultInfo {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.resultInfoItem {
  display: flex;
  margin-bottom: 8px;
}

.resultInfoItem:last-child {
  margin-bottom: 0;
}

.resultInfoLabel {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

.resultInfoValue {
  color: #666;
}

.documentPreview {
  margin-bottom: 24px;
}

.previewTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.previewContent {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  max-height: 200px;
  overflow-y: auto;
}

.actionButtons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.previewButton {
  height: 44px;
  padding: 0 32px;
  font-size: 16px;
  border-radius: 8px;
  border-color: #1890ff;
  color: #1890ff;
}

.previewButton:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.downloadButton {
  height: 44px;
  padding: 0 32px;
  font-size: 16px;
  border-radius: 8px;
}

/* 空状态 */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
}

.emptyIcon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
  font-size: 40px;
  color: #999;
  margin-bottom: 20px;
}

.emptyTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.emptyDesc {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  max-width: 400px;
}

.generateButton {
  height: 44px;
  padding: 0 32px;
  font-size: 16px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pptContent {
    flex-direction: column;
  }
  
  .pptLeft,
  .pptMain {
    width: 100%;
  }
  
  .pptLeft {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .pptContainer {
    padding: 16px;
  }

  .titleText {
    font-size: 20px;
  }

  .titleDesc {
    font-size: 14px;
  }

  .templateContainer,
  .mainContainer {
    padding: 16px;
  }

  .templateList {
    gap: 8px;
  }

  .templateThumbnail {
    height: 100px;
  }

  /* 页面预览网格响应式 */
  .pagesGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .pageCardThumbnail {
    height: 150px;
  }

  .mainContent {
    padding: 16px;
  }

  .headerActions {
    flex-direction: column;
    gap: 8px;
  }

  .regenerateButton,
  .downloadButton {
    width: 100%;
    height: 32px;
    font-size: 14px;
  }
}
