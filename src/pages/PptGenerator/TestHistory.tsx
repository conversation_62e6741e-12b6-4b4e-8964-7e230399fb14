import { pptApi } from '@/utils/api_ppt';
import { <PERSON><PERSON>, Card, message, Space } from 'antd';
import React, { useState } from 'react';

/**
 * PPT历史记录功能测试页面
 * 用于测试PPT历史记录API接口
 */
const TestHistory: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [historyData, setHistoryData] = useState<any>(null);

  // 测试获取历史记录
  const testGetHistory = async () => {
    try {
      setLoading(true);
      console.log('开始测试PPT历史记录API...');

      const result = await pptApi.getPptHistory(1, 10);
      console.log('PPT历史记录API测试结果:', result);

      setHistoryData(result);
      message.success(`PPT历史记录API测试成功，共获取到 ${result.items.length} 条记录`);
    } catch (error) {
      console.error('PPT历史记录API测试失败:', error);
      message.error('PPT历史记录API测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试模拟下载功能
  const testDownload = async () => {
    if (!historyData?.items?.length) {
      message.warning('请先获取历史记录');
      return;
    }

    try {
      const firstItem = historyData.items[0];
      console.log('开始测试PPT模拟下载API...', firstItem.id);

      const { filename, size } = await pptApi.simulateDownloadPpt(firstItem.id);
      console.log('PPT模拟下载API测试结果:', { filename, size });

      message.success(`PPT模拟下载API测试成功: ${filename} (${(size / 1024 / 1024).toFixed(2)} MB)`);
    } catch (error) {
      console.error('PPT模拟下载API测试失败:', error);
      message.error('PPT模拟下载API测试失败');
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="PPT历史记录功能测试" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <h3>API接口测试</h3>
            <Space>
              <Button 
                type="primary" 
                onClick={testGetHistory}
                loading={loading}
              >
                测试获取历史记录
              </Button>
              <Button
                onClick={testDownload}
                disabled={!historyData?.items?.length}
              >
                测试模拟下载功能
              </Button>
            </Space>
          </div>

          {historyData && (
            <div>
              <h3>历史记录数据</h3>
              <Card size="small">
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {JSON.stringify(historyData, null, 2)}
                </pre>
              </Card>
            </div>
          )}

          <div>
            <h3>功能说明</h3>
            <ul>
              <li>✅ PPT历史记录API接口 - /api/ppt/history</li>
              <li>✅ PPT历史记录列表组件 - HistoryPanel</li>
              <li>✅ PPT详情页面组件 - PptDetail</li>
              <li>✅ URL路由支持 - /college/ppt?pptid=xxx</li>
              <li>✅ 无限滚动分页加载</li>
              <li>✅ 搜索功能</li>
              <li>✅ 删除功能</li>
              <li>✅ 模拟下载功能（仅UI展示，不实际下载文件）</li>
              <li>✅ 完整的数据结构支持（包含文件路径、模板类型等）</li>
              <li>✅ 错误处理和状态管理</li>
            </ul>
          </div>

          <div>
            <h3>使用方式</h3>
            <ol>
              <li>在PPT生成页面点击"历史记录"按钮</li>
              <li>在历史记录列表中点击任意记录</li>
              <li>跳转到PPT详情页面查看详细信息</li>
              <li>在详情页面可以下载PPT文件或重新生成</li>
            </ol>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default TestHistory;
