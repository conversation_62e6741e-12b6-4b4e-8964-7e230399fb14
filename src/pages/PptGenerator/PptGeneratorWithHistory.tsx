import FileUploadDragger from '@/components/FileUploadDragger';
import { pptApi } from '@/utils/api_ppt';
import {
  DownloadOutlined,
  HistoryOutlined,
  LoadingOutlined,
  PlayCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { App, <PERSON><PERSON>, <PERSON>dal, Tooltip } from 'antd';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import HistoryPanel from './History';
import { mockPptTemplates } from './mockData';
import styles from './PptGenerator.module.css';

// PPT模板接口
interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: string;
}

// 上传文件接口
interface UploadedFile {
  file: File;
  name: string;
  size: number;
}

// PPT页面预览接口
interface PptPagePreview {
  page_number: number;
  thumbnail_url: string;
  title?: string;
}

// PPT生成结果接口
interface PptGenerateResult {
  id: string;
  file_path: string;
  filename: string;
  size: number;
  download_url: string;
  preview_url: string;
  document_content: string;
  generation_time: number;
  pages?: PptPagePreview[]; // 分页预览信息
  total_pages?: number; // 总页数
}

const PptGeneratorWithHistory: React.FC = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();
  
  // 状态管理
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<PptTemplate | null>(null);
  const [generateResult, setGenerateResult] = useState<PptGenerateResult | null>(null);
  const [selectedPagePreview, setSelectedPagePreview] = useState<PptPagePreview | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  /** 处理文件上传 */
  const handleFileUpload = (file: File, _fileList: File[]) => {
    if (isGenerating) {
      message.warning("PPT生成中，请稍后再试");
      return false;
    }

    console.log("文件上传:", file.name, file.size);
    
    // 文件大小检查（2万字约40MB）
    const maxSize = 40 * 1024 * 1024; // 40MB
    if (file.size > maxSize) {
      message.error("文件过大，请选择小于40MB的文件");
      return false;
    }

    // 文件类型检查
    const allowedTypes = ['.doc', '.docx', '.txt', '.pdf', '.md', '.markdown'];
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
      message.error("不支持的文件格式，请选择Word、PDF、TXT或Markdown文件");
      return false;
    }

    setUploadedFile({
      file,
      name: file.name,
      size: file.size
    });

    message.success("文件上传成功");
    return true;
  };

  /** 处理文件删除 */
  const handleFileDelete = () => {
    if (isGenerating) {
      message.warning("PPT生成中，无法删除文件");
      return;
    }
    
    setUploadedFile(null);
    message.success("文件已删除");
  };

  /** 处理模板选择 */
  const handleTemplateSelect = (template: PptTemplate) => {
    if (isGenerating) {
      message.warning("PPT生成中，无法更换模板");
      return;
    }
    
    setSelectedTemplate(template);
    console.log("选择模板:", template.name);
  };

  /** 生成PPT */
  const handleGeneratePpt = async () => {
    if (!uploadedFile) {
      message.error("请先上传文档文件");
      return;
    }
    
    if (!selectedTemplate) {
      message.error("请先选择PPT模板");
      return;
    }

    try {
      setIsGenerating(true);
      console.log("开始生成PPT:", uploadedFile.name, selectedTemplate.type);
      
      const formData = new FormData();
      formData.append("file", uploadedFile.file);
      formData.append("ppt_type", selectedTemplate.type);
      
      const result = await pptApi.generatePpt(formData);
      console.log("PPT生成成功:", result);

      const resultWithPages = {
        ...result
      };

      setGenerateResult(resultWithPages);
      message.success("PPT生成成功！");
      
    } catch (error) {
      console.error("PPT生成失败:", error);
      message.error("PPT生成失败，请稍后重试");
    } finally {
      setIsGenerating(false);
    }
  };

  /** 处理页面预览点击 */
  const handlePagePreviewClick = (page: PptPagePreview) => {
    setSelectedPagePreview(page);
  };

  /** 关闭页面预览模态框 */
  const handleClosePagePreview = () => {
    setSelectedPagePreview(null);
  };

  /** 重新生成PPT */
  const handleRegeneratePpt = async () => {
    if (!uploadedFile || !selectedTemplate) {
      message.error("缺少必要信息，无法重新生成");
      return;
    }

    // 清除之前的结果
    setGenerateResult(null);
    
    // 重新生成
    await handleGeneratePpt();
  };

  /** 模拟下载PPT文件（仅UI展示） */
  const handleDownloadPpt = async () => {
    if (!generateResult) {
      message.error("没有可下载的PPT文件");
      return;
    }

    try {
      setIsDownloading(true);
      console.log("模拟下载PPT:", generateResult.id);

      // 调用模拟下载API
      const { filename, size } = await pptApi.simulateDownloadPpt(generateResult.id);

      // 显示下载信息（仅UI展示，不实际下载）
      message.success(`模拟下载成功: ${filename} (${(size / 1024 / 1024).toFixed(2)} MB)`);
      console.log('PPT文件信息:', {
        filename,
        size,
        originalFilename: generateResult.filename,
        downloadUrl: generateResult.download_url
      });

    } catch (error) {
      console.error("模拟下载失败:", error);
      message.error("模拟下载失败，请稍后重试");
    } finally {
      setIsDownloading(false);
    }
  };

  /** 处理历史记录选择 */
  const handleHistorySelect = (pptId: string) => {
    // 跳转到PPT详情页面
    navigate(`/college/ppt?pptid=${pptId}`);
    // 自动关闭历史面板
    setShowHistory(false);
  };

  return (
    <div className={styles.pptPageContainer}>
      {/* 历史记录面板 */}
      <div className={`${styles.pptHistoryPanel} ${showHistory ? styles.show : styles.hide}`}>
        <HistoryPanel
          onClose={() => setShowHistory(false)}
          shouldRefresh={showHistory}
          onSelect={handleHistorySelect}
        />
      </div>

      <div className={styles.pptContainer}>
        {/* 页面标题 */}
        <div className={styles.pptTitle}>
          <div className={styles.titleLeft}>
            <div className={styles.titleText}>
              <Tooltip title="查看历史记录">
                <Button
                  type="default"
                  icon={<HistoryOutlined />}
                  onClick={() => setShowHistory(!showHistory)}
                  className={styles.historyButton}
                />
              </Tooltip>
              AI PPT生成器
            </div>
            <div className={styles.titleDesc}>智能文档转PPT，快速生成专业演示文稿</div>
          </div>
        </div>

      <div className={styles.pptContent}>
        {/* 左侧：文件上传和模板选择 */}
        <div className={styles.pptLeft}>
          {/* 文件上传区域 */}
          <FileUploadDragger
            title="上传文档文件"
            description={
              <>
                支持 Word文档（DOC、DOCX）、PDF文件、文本文件（TXT）、Markdown文件（MD）
                <br />
                文件大小限制：最大2万字
              </>
            }
            accept=".doc,.docx,.txt,.pdf,.md,.markdown"
            onUpload={handleFileUpload}
            disabled={isGenerating}
            isUploading={false}
            uploadingText="正在上传文档，请稍候..."
            multiple={false}
            showUploadedFile={true}
            uploadedFile={uploadedFile ? { name: uploadedFile.name, size: uploadedFile.size } : null}
            onFileDelete={handleFileDelete}
            showReplaceConfirm={true}
            singleFileHint="一次PPT任务只能上传一个文件"
            disableDelete={isGenerating}
          />

          {/* PPT模板选择区域 */}
          <div className={styles.templateContainer}>
            <div className={styles.templateTitle}>选择PPT模板</div>
            <div className={styles.templateDesc}>
              选择适合的学术PPT模板，系统将根据您的文档内容自动生成演示文稿
            </div>
            
            <div className={styles.templateList}>
              {mockPptTemplates.map((template) => (
                <div
                  key={template.id}
                  className={`${styles.templateItem} ${
                    selectedTemplate?.id === template.id ? styles.templateItemSelected : ''
                  } ${isGenerating ? styles.templateItemDisabled : ''}`}
                  onClick={() => !isGenerating && handleTemplateSelect(template)}
                >
                  <div className={styles.templateIcon}>
                    <img src={template.thumbnail} alt={template.name} />
                  </div>
                  <div className={styles.templateInfo}>
                    <div className={styles.templateName}>{template.name}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧：生成结果展示 */}
        <div className={styles.pptRight}>
          <div className={styles.rightHeader}>
            <div className={styles.rightTitle}>生成结果</div>
            
            {/* 操作按钮 */}
            {generateResult && (
              <div className={styles.actionButtons}>
                <Button
                  type="default"
                  icon={<SyncOutlined />}
                  onClick={handleRegeneratePpt}
                  loading={isGenerating}
                  disabled={!uploadedFile || !selectedTemplate}
                >
                  重新生成
                </Button>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={handleDownloadPpt}
                  loading={isDownloading}
                >
                  下载PPT
                </Button>
              </div>
            )}
          </div>

          {/* 动态内容区域 */}
          <div className={styles.mainContent}>
            {isGenerating ? (
              <div className={styles.generatingContainer}>
                <LoadingOutlined className={styles.generatingIcon} />
                <div className={styles.generatingTitle}>正在生成PPT...</div>
                <div className={styles.generatingDesc}>
                  AI正在分析您的文档内容并生成专业的PPT演示文稿，请稍候
                </div>
                <div className={styles.generatingWarning}>
                  ⚠️ 请勿离开此页面，生成过程中离开可能导致任务中断
                </div>
              </div>
            ) : generateResult ? (
              <div className={styles.resultContainer}>
                {/* PPT页面预览网格 */}
                {generateResult.pages && generateResult.pages.length > 0 ? (
                  <div className={styles.pagesGrid}>
                    {generateResult.pages.map((page) => (
                      <div
                        key={page.page_number}
                        className={styles.pageCard}
                        onClick={() => handlePagePreviewClick(page)}
                      >
                        <div className={styles.pageCardThumbnail}>
                          <img
                            src={page.thumbnail_url}
                            alt={`第${page.page_number}页`}
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/placeholder-slide.png';
                            }}
                          />
                        </div>
                        <div className={styles.pageCardInfo}>
                          <div className={styles.pageCardTitle}>
                            {page.title || `第${page.page_number}页`}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className={styles.resultInfo}>
                    <div className={styles.resultInfoGrid}>
                      <div className={styles.resultInfoItem}>
                        <span className={styles.resultInfoLabel}>文件名:</span>
                        <span className={styles.resultInfoValue}>{generateResult.filename}</span>
                      </div>
                      <div className={styles.resultInfoItem}>
                        <span className={styles.resultInfoLabel}>文件大小:</span>
                        <span className={styles.resultInfoValue}>
                          {(generateResult.size / 1024 / 1024).toFixed(2)} MB
                        </span>
                      </div>
                      <div className={styles.resultInfoItem}>
                        <span className={styles.resultInfoLabel}>生成耗时:</span>
                        <span className={styles.resultInfoValue}>{generateResult.generation_time} 秒</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className={styles.emptyContainer}>
                <div className={styles.emptyIcon}>
                  <PlayCircleOutlined />
                </div>
                <div className={styles.emptyTitle}>准备生成PPT</div>
                <div className={styles.emptyDesc}>
                  请上传文档文件并选择PPT模板，然后点击生成按钮
                </div>
                
                {uploadedFile && selectedTemplate && (
                  <Button
                    type="primary"
                    size="large"
                    icon={<PlayCircleOutlined />}
                    onClick={handleGeneratePpt}
                    loading={isGenerating}
                    className={styles.generateButton}
                  >
                    生成PPT
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 页面预览模态框 */}
      <Modal
        title={`第${selectedPagePreview?.page_number}页预览`}
        open={!!selectedPagePreview}
        onCancel={handleClosePagePreview}
        footer={null}
        width={800}
        centered
      >
        {selectedPagePreview && (
          <div className={styles.pagePreviewModal}>
            <img
              src={selectedPagePreview.thumbnail_url}
              alt={`第${selectedPagePreview.page_number}页`}
              style={{ width: '100%', height: 'auto' }}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/placeholder-slide.png';
              }}
            />
          </div>
        )}
      </Modal>
      </div>
    </div>
  );
};

export default PptGeneratorWithHistory;
