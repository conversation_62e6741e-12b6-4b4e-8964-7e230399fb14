.pptDetailContainer {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.backButton {
  color: #666;
  font-size: 14px;
}

.headerTitle {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.headerActions {
  display: flex;
  gap: 12px;
}

.content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px 24px;
  overflow: hidden;
}

.pptInfo {
  width: 400px;
  flex-shrink: 0;
}

.pptPreview {
  flex: 1;
  min-width: 0;
}

.infoCard,
.previewCard {
  height: 100%;
}

.infoGrid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.infoItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.infoItem:last-child {
  border-bottom: none;
}

.infoLabel {
  color: #666;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.infoValue {
  color: #333;
  text-align: right;
  word-break: break-all;
  flex: 1;
  margin-left: 16px;
}

.errorMessage {
  margin-top: 16px;
  padding: 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
}

.errorTitle {
  color: #cf1322;
  font-weight: 500;
  margin-bottom: 8px;
}

.errorContent {
  color: #a8071a;
  font-size: 13px;
  line-height: 1.5;
}

.previewPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
}

.previewIcon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.previewText {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.previewDesc {
  font-size: 14px;
  color: #bbb;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loadingText {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.errorText {
  font-size: 18px;
  color: #ff4d4f;
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content {
    flex-direction: column;
  }
  
  .pptInfo {
    width: 100%;
  }
  
  .pptPreview {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
  }
  
  .headerTitle {
    font-size: 16px;
  }
  
  .headerActions {
    gap: 8px;
  }
  
  .content {
    padding: 12px 16px;
    gap: 12px;
  }
  
  .infoItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .infoValue {
    text-align: left;
    margin-left: 0;
  }
}
