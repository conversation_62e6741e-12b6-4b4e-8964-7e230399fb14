/**
 * PPT模板Mock数据
 * 注意：这是临时的Mock数据，用于前端开发和测试
 * 后续需要替换为真实的后端API接口
 */

// PPT模板接口定义
export interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: string;
}

// Mock PPT模板数据
export const mockPptTemplates: PptTemplate[] = [
  {
    id: "graduation",
    name: "毕业答辩模板",
    description: "适用于毕业论文答辩，包含封面、目录、研究背景、方法、结果、结论等标准章节",
    thumbnail: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSIzMCIgZmlsbD0iIzE4OTBGRiIvPgo8dGV4dCB4PSIxMDAiIHk9IjMwIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5q+V5Lia562U6L6pPC90ZXh0Pgo8cmVjdCB4PSIxMCIgeT0iNTAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgZmlsbD0iI0Y5RjlGOSIgc3Ryb2tlPSIjRTVFNUU1Ii8+CjxyZWN0IHg9IjEwMCIgeT0iNTAiIHdpZHRoPSI5MCIgaGVpZ2h0PSIxNSIgZmlsbD0iI0U1RTVFNSIvPgo8cmVjdCB4PSIxMDAiIHk9IjcwIiB3aWR0aD0iOTAiIGhlaWdodD0iMTUiIGZpbGw9IiNFNUU1RTUiLz4KPHJlY3QgeD0iMTAwIiB5PSI5MCIgd2lkdGg9IjkwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjRTVFNUU1Ii8+CjxyZWN0IHg9IjEwMCIgeT0iMTEwIiB3aWR0aD0iOTAiIGhlaWdodD0iMTUiIGZpbGw9IiNFNUU1RTUiLz4KPC9zdmc+",
    type: "graduation"
  },
  {
    id: "creative",
    name: "创意模板",
    description: "创意设计风格，适用于创新项目展示，突出视觉效果和创意表达",
    thumbnail: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRkFGQUZBIi8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSIyNSIgZmlsbD0iIzUyQzQxQSIvPgo8dGV4dCB4PSIxMDAiIHk9IjI4IiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5Yib5oSP5qih5p2/PC90ZXh0Pgo8Y2lyY2xlIGN4PSI1MCIgY3k9IjgwIiByPSIyNSIgZmlsbD0iIzUyQzQxQSIgb3BhY2l0eT0iMC4yIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjgwIiByPSIyMCIgZmlsbD0iIzUyQzQxQSIgb3BhY2l0eT0iMC40Ii8+CjxjaXJjbGUgY3g9IjE1MCIgY3k9IjgwIiByPSIzMCIgZmlsbD0iIzUyQzQxQSIgb3BhY2l0eT0iMC4zIi8+CjxyZWN0IHg9IjEwIiB5PSIxMjAiIHdpZHRoPSIxODAiIGhlaWdodD0iMjAiIGZpbGw9IiNGMEYwRjAiLz4KPC9zdmc+",
    type: "creative"
  },
  {
    id: "simple",
    name: "简约模板",
    description: "简约清爽设计，适用于商务汇报、学术展示等正式场合的演示",
    thumbnail: "data:image/svg+xml,%3Csvg width='200' height='150' viewBox='0 0 200 150' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='200' height='150' fill='%23FFFFFF'/%3E%3Crect x='0' y='0' width='200' height='40' fill='%23666666'/%3E%3Ctext x='100' y='25' fill='white' font-size='14' text-anchor='middle'%3E简约模板%3C/text%3E%3Crect x='20' y='60' width='60' height='40' fill='%23F5F5F5' stroke='%23E5E5E5'/%3E%3Crect x='90' y='60' width='90' height='40' fill='%23F5F5F5' stroke='%23E5E5E5'/%3E%3Crect x='20' y='110' width='160' height='20' fill='%23F0F0F0'/%3E%3C/svg%3E",
    type: "simple"
  }
];

/**
 * 获取PPT模板列表
 * TODO: 后续替换为真实的API调用
 */
export const getPptTemplates = (): Promise<PptTemplate[]> => {
  return new Promise((resolve) => {
    // 模拟API延迟
    setTimeout(() => {
      resolve(mockPptTemplates);
    }, 300);
  });
};

/**
 * 根据ID获取PPT模板详情
 * TODO: 后续替换为真实的API调用
 */
export const getPptTemplateById = (id: string): Promise<PptTemplate | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const template = mockPptTemplates.find(t => t.id === id);
      resolve(template || null);
    }, 200);
  });
};
