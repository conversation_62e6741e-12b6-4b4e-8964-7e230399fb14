import React from 'react';
import { useSearchParams } from 'react-router-dom';
import PptDetail from './PptDetail';
import PptGeneratorWithHistory from "./PptGeneratorWithHistory";

/**
 * PPT路由组件
 * 根据URL参数决定显示PPT生成页面还是PPT详情页面
 */
const PptRouter: React.FC = () => {
  const [searchParams] = useSearchParams();
  const pptId = searchParams.get('pptid');

  // 如果有pptid参数，显示PPT详情页面
  if (pptId) {
    return <PptDetail />;
  }

  // 否则显示PPT生成页面
  return <PptGeneratorWithHistory />;
};

export default PptRouter;
