/* 思考内容折叠组件 */
.thinkingCollapse {
  margin-bottom: 16px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background: #f8f9fa;
  overflow: hidden;
}

.thinkingHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  user-select: none;
}

.thinkingHeader:hover {
  background: #e8e8e8;
}

.thinkingTitle {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
}

.thinkingToggle {
  color: #999;
  font-size: 12px;
  transition: transform 0.2s ease;
}

.thinkingStreaming {
  font-size: 12px;
  color: #1890ff;
  font-style: italic;
}

.thinkingMarkdown {
  font-size: 13px !important;
  line-height: 1.5 !important;
  padding: 14px;
  background: #fafafa;
  border-top: 1px solid #e5e5e5;
}
