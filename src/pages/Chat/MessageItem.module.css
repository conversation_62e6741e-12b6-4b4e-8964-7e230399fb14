/* 消息容器 */
.messageContainer {
  display: flex;
  width: 100%;
}

.messageContainer.questionMessage {
  justify-content: flex-end;
}

.messageContainer.answerMessage {
  justify-content: flex-start;
}

.messageBubble {
  max-width: 60%;
  min-width: 200px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  overflow: hidden;
}

.messageContainer.questionMessage .messageBubble {
  background: #1890ff;
  color: #fff;
}

.messageContainer.answerMessage .messageBubble {
  width: 60%;
  background: #fff;
  color: #333;
}

.llmContent {
  padding: 12px 16px;
  word-wrap: break-word;
  word-break: break-word;
}

.textContent {
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.messageMarkdown {
  font-size: 14px;
  line-height: 1.6 !important;
}

/* katex基础样式 */
.messageMarkdown :global(.katex) {
  font-size: 1.1em !important;
  color: #333 !important;
}

.messageMarkdown :global(.katex-display) {
  margin: 1em 0 !important;
  text-align: center !important;
  overflow-x: auto;
  overflow-y: hidden;
  clear: both;
}

.messageMarkdown :global(.katex-html) {
  overflow-x: auto;
  overflow-y: hidden;
}

.messageFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  background: rgba(0, 0, 0, 0.02);
}

.messageContainer.questionMessage .messageFooter {
  background: rgba(255, 255, 255, 0.1);
  border-top-color: rgba(255, 255, 255, 0.2);
}

.messageTime {
  font-size: 12px;
  color: #999;
}

.messageContainer.questionMessage .messageTime {
  color: rgba(255, 255, 255, 0.8);
}

.refreshButton {
  color: #666 !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 4px !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-left: auto;
}

.refreshButton:hover {
  background: rgba(0, 0, 0, 0.04) !important;
  color: #333 !important;
}

/* 流式状态显示 */
.streamingText {
  font-size: 12px;
  color: #1890ff;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 4px;
}

.streamingText::after {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #1890ff;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
