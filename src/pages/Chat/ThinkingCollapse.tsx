import { DownOutlined, UpOutlined } from "@ant-design/icons";
import "github-markdown-css/github-markdown-light.css";
import React, { useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import styles from "./ThinkingCollapse.module.css";

interface ThinkingCollapseProps {
  /** 思考内容 */
  thinkingContent: string;
  /** 是否正在流式更新 */
  isStreaming?: boolean;
  /** 初始展开状态 */
  defaultExpanded?: boolean;
}

const ThinkingCollapse: React.FC<ThinkingCollapseProps> = ({
  thinkingContent,
  isStreaming = false,
  defaultExpanded = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(isStreaming || defaultExpanded);

  // 流式更新时自动展开，完成后自动折叠
  React.useEffect(() => {
    if (isStreaming) {
      setIsExpanded(true);
    } else {
      // 当流式更新结束后，自动折叠
      setIsExpanded(false);
    }
  }, [isStreaming]);

  if (!thinkingContent.trim()) {
    return null;
  }

  return (
    <div className={styles.thinkingCollapse}>
      <div
        className={styles.thinkingHeader}
        onClick={() => !isStreaming && setIsExpanded(!isExpanded)}
      >
        <span className={styles.thinkingTitle}>AI思考过程</span>
        {!isStreaming && (
          <span className={styles.thinkingToggle}>
            {isExpanded ? <UpOutlined /> : <DownOutlined />}
          </span>
        )}
        {isStreaming && <span className={styles.thinkingStreaming}>思考中...</span>}
      </div>

      {isExpanded && (
        <div className={`${styles.thinkingMarkdown} markdown-body`}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>{thinkingContent}</ReactMarkdown>
        </div>
      )}
    </div>
  );
};

export default ThinkingCollapse;
