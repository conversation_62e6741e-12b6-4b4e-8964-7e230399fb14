import { ReloadOutlined } from "@ant-design/icons";
import { Button } from "antd";
import "github-markdown-css/github-markdown-light.css";
import "katex/dist/katex.min.css";
import React from "react";
import ReactMarkdown from "react-markdown";
import rehypeKatex from "rehype-katex";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import styles from "./MessageItem.module.css";
import ThinkingCollapse from "./ThinkingCollapse";

interface ChatMessage {
  id: string;
  content: string;
  type: "question" | "answer";
  timestamp: string;
  /** 是否正在流式更新 */
  isStreaming?: boolean;
}

interface MessageItemProps {
  message: ChatMessage;
  /** 流式内容（仅在流式更新时使用） */
  streamContent?: string;
  onRefresh?: (messageId: string) => void;
}

/** 解析流式内容中的思考内容（包括未完成的标签） */
const parseThinkingContent = (content: string) => {
  // 先匹配完整的think标签
  const completeThinkRegex = /<think>([\s\S]*?)<\/think>/g;
  const completeParts: string[] = [];
  let match;

  // 提取所有完整的思考内容
  while ((match = completeThinkRegex.exec(content)) !== null) {
    completeParts.push(match[1]);
  }

  // 移除所有完整的think标签内容
  let remainingContent = content.replace(completeThinkRegex, "");

  // 检查是否有未完成的think标签（开始但没有结束）
  const incompleteThinkMatch = remainingContent.match(/<think>([\s\S]*?)$/);
  let incompletePart = "";

  if (incompleteThinkMatch) {
    incompletePart = incompleteThinkMatch[1];
    // 移除未完成的think标签内容
    remainingContent = remainingContent.replace(/<think>[\s\S]*?$/, "");
  }

  // 剩余内容就是回复内容
  const replyContent = remainingContent.trim();

  // 合并所有思考内容
  const allThinkingParts = [...completeParts];
  if (incompletePart) {
    allThinkingParts.push(incompletePart);
  }

  return {
    thinkingContent: allThinkingParts.join("\n\n"),
    replyContent,
  };
};

const MessageItem: React.FC<MessageItemProps> = ({ message, streamContent, onRefresh }) => {
  const isAnswer = message.type === "answer";

  // 获取实际要显示的内容（流式更新时使用streamContent，否则使用message.content）
  const displayContent = message.isStreaming && streamContent ? streamContent : message.content;

  // 解析思考内容和回复内容
  const { thinkingContent, replyContent } = isAnswer
    ? parseThinkingContent(displayContent)
    : { thinkingContent: "", replyContent: displayContent };

  return (
    <div
      className={`${styles.messageContainer} ${message.type === "question" ? styles.questionMessage : styles.answerMessage}`}
    >
      <div className={styles.messageBubble}>
        {/* 消息内容 */}
        <div className={styles.llmContent}>
          {isAnswer ? (
            <div>
              {/* 思考内容（如果存在） */}
              {thinkingContent && (
                <ThinkingCollapse
                  thinkingContent={thinkingContent}
                  isStreaming={message.isStreaming}
                  defaultExpanded={message.isStreaming}
                />
              )}

              {/* AI回复内容 */}
              {replyContent && (
                <div className={`${styles.messageMarkdown} markdown-body`}>
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm, remarkMath]}
                    rehypePlugins={[
                      [
                        rehypeKatex,
                        {
                          strict: false, // 关闭严格检查，尽可能渲染内容
                          output: "html", // 控制katex输出为HTML而不是MathML，解决产生额外的空白和高度问题
                        },
                      ],
                    ]}
                  >
                    {/* {processLatexContent(replyContent)} */}
                    {replyContent}
                  </ReactMarkdown>
                </div>
              )}
            </div>
          ) : (
            // 用户消息纯文本显示
            <div className={styles.textContent}>{displayContent}</div>
          )}
        </div>

        {/* 消息元信息 */}
        <div className={styles.messageFooter}>
          <span
            className={styles.messageTime}
            style={isAnswer ? { marginRight: "auto" } : { marginLeft: "auto" }}
          >
            {message.timestamp}
          </span>
          {isAnswer && onRefresh && !message.isStreaming && (
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              className={styles.refreshButton}
              onClick={() => onRefresh(message.id)}
            />
          )}
          {message.isStreaming && <span className={styles.streamingText}>正在回复...</span>}
        </div>
      </div>
    </div>
  );
};

export default MessageItem;
