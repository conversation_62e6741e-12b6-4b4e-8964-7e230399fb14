/* 外层容器 */
.chatContainer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  background-color: #f5f5f5;
}

/* 顶部Header */
.chatHeader {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: fit-content;
  margin-bottom: 20px;
  gap: 16px;
}

.historyButton {
  color: #333 !important;
  background: #fff !important;
  border: 1px solid #e5e5e5 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  width: 40px !important;
  height: 40px !important;
}

.historyButton:hover {
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

.titleContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.titleText {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.titleDesc {
  font-size: 16px;
  color: #555;
}

/* 下方内容区域 */
.chatContent {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  min-height: 0;
}

/* 左侧历史对话列表 */
.historyPanel {
  width: 30%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  overflow: hidden;
}

.historyPanel.show {
  opacity: 1;
  transform: translateX(0);
  margin-right: 20px;

  transition:
    width 0.3s ease,
    margin-right 0.2s ease,
    transform 0.3s ease,
    opacity 0.2s ease;
}

.historyPanel.hide {
  width: 0px;
  opacity: 0;
  transform: translateX(-100%);
  margin-right: 0px;

  transition:
    width 0.3s ease,
    margin-right 0.2s ease,
    transform 0.3s ease,
    opacity 0.2s ease;
}

.historyHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.searchContainer {
  flex: 1;
}

.searchInput {
  border-radius: 8px !important;
}

.newChatButton {
  border-radius: 8px !important;
  width: 40px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.historyList {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.historyItem {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.historyItem:hover {
  background-color: #f5f5f5;
}

.historyTitle {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.historyMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.historyTime {
  font-size: 12px;
  color: #999;
}

.historyCount {
  font-size: 12px;
  color: #666;
}

/* 右侧当前对话面板 */
.chatPanel {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  overflow: hidden;
}

/* 当历史面板隐藏时，聊天面板占满全宽 */
/* .historyPanel.hide + .chatPanel {
  width: 100%;
} */

.messageList {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 消息输入框 */
.inputContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #e5e5e5;
  background: #fafafa;
}

.inputContent {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
}

.uploadedFileList {
  display: flex;
  gap: 12px;
  width: 100%;
  min-height: fit-content;
  flex-wrap: wrap;
}

.uploadedFileItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  font-size: 12px;
  position: relative;
}

.uploadedFileIcon {
  color: #1890ff;
  font-size: 14px;
}

.uploadedFileName {
  color: #555;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.deleteFileButton {
  color: #999 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 2px !important;
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  font-size: 10px !important;
}

.deleteFileButton:hover {
  color: #ff4d4f !important;
  background: #fff2f0 !important;
}

.uploadButton {
  color: #666 !important;
  border: 1px solid #e5e5e5 !important;
  background: #fff !important;
  border-radius: 8px !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.uploadButton:hover {
  color: #333 !important;
  border-color: #d9d9d9 !important;
  background: #f5f5f5 !important;
}

.messageInput {
  flex: 1;
  border-radius: 20px !important;
  height: 40px !important;
  padding: 0 16px !important;
  border: 1px solid #e5e5e5 !important;
  background: #fff !important;
}

.messageInput:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.sendButton {
  border-radius: 8px !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.sendButton:disabled {
  opacity: 0.5 !important;
}
