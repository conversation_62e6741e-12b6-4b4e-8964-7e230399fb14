export const mdContent = `# 数学公式渲染测试

## 基础公式测试

### 行内公式
这是行内公式测试：质能方程 $E = mc^2$，二次方程判别式 $\\Delta = b^2 - 4ac$，以及简单分数 $\\frac{1}{2}$。

### 块级公式
二次方程求根公式：
$$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

## 复杂公式测试

### 积分与极限
$$\\lim_{n \\to \\infty} \\sum_{i=1}^{n} \\frac{1}{n} \\cdot f\\left(\\frac{i}{n}\\right) = \\int_{0}^{1} f(x) dx$$

### 矩阵与行列式
$$\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix} \\begin{pmatrix} x \\\\ y \\end{pmatrix} = \\begin{pmatrix} ax + by \\\\ cx + dy \\end{pmatrix}$$

### 求和与连分数
$$\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}, \\quad \\sqrt{2} = 1 + \\cfrac{1}{2 + \\cfrac{1}{2 + \\cfrac{1}{2 + \\ddots}}}$$

### 长公式测试
$$f(x) = \\frac{1}{\\sqrt{2\\pi\\sigma^2}} \\exp\\left(-\\frac{(x-\\mu)^2}{2\\sigma^2}\\right) \\cdot \\prod_{i=1}^{n} \\left(1 + \\frac{x_i}{\\lambda}\\right)$$

## 混合内容测试

### 文本与公式混合
对于函数 $f(x) = x^2 + 2x + 1$，我们有：
- 当 $x = 1$ 时，$f(1) = 4$
- 导数为 $f'(x) = 2x + 2$
- 积分为 $\\int f(x) dx = \\frac{x^3}{3} + x^2 + x + C$

### 表格中的公式
| 函数 | 导数 | 积分 |
|------|------|------|
| $x^2$ | $2x$ | $\\frac{x^3}{3}$ |
| $e^x$ | $e^x$ | $e^x$ |
| $\\sin x$ | $\\cos x$ | $-\\cos x$ |

### 引用块中的公式
> 著名的欧拉公式：$e^{i\\pi} + 1 = 0$
> 
> 这个公式包含了数学中五个最重要的常数：$e$, $i$, $\\pi$, $1$, $0$

## 边界情况测试

### 特殊符号
$$\\alpha + \\beta = \\gamma, \\quad \\sum_{\\alpha=1}^{\\infty} \\frac{1}{\\alpha!} = e$$

### 上下标嵌套
$$x_{a_1}^{b^2} + y_{i,j}^{(k)} = z_{\\text{max}}^{\\text{new}}$$

### 分式嵌套
$$\\frac{\\frac{a}{b}}{\\frac{c}{d}} = \\frac{ad}{bc}$$

## 代码与公式
\`\`\`python
# 计算二次方程的根
import math

def quadratic_roots(a, b, c):
    discriminant = b**2 - 4*a*c  # Δ = b² - 4ac
    if discriminant >= 0:
        root1 = (-b + math.sqrt(discriminant)) / (2*a)
        root2 = (-b - math.sqrt(discriminant)) / (2*a)
        return root1, root2
    else:
        return None  # 无实数解
\`\`\`

## 总结
以上测试涵盖了：
- ✅ 行内公式：$\\checkmark$
- ✅ 块级公式：$\\checkmark$  
- ✅ 复杂公式：$\\checkmark$
- ✅ 混合内容：$\\checkmark$

**最终答案**：$\\boxed{\\text{测试完成}}$`;
