.homeworkContainer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding: 20px;
  overflow: hidden;
}
/* 顶部标题 */
.homeworkTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: fit-content;
  margin-bottom: 30px;
}

.titleLeft {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: fit-content;
}

.titleText {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.titleDesc {
  font-size: 16px;
  color: #555;
}

/* 内容区域 */
.homeworkContent {
  display: flex;
  flex-direction: row;
  gap: 20px;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.homeworkLeft {
  width: 23%;
  display: flex;
  flex-direction: column;
}

.homeworkMain {
  width: 77%;
}

/* 左侧区域 */
/* .uploaderContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: fit-content;

  padding: 24px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.uploaderInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  min-height: fit-content;

  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.uploaderTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.uploaderDesc {
  font-size: 14px;
  color: #555;
}

.uploadDragger {
  background: #fff;
  margin-bottom: 16px;
}

.dragInner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.dragText {
  font-size: 14px;
  color: #999;
}

.dragIcon {
  font-size: 32px;
  font-weight: bold;
  color: #999;
}

.uploadingInfo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 16px 0px;
}

.uploadingText {
  margin-left: 8px;
  font-size: 14px;
  color: #999;
} */

.fileListTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 6px;
}

.uploadedFileList {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  min-height: fit-content;
}

.uploadedFileItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: #fafafa;
}

.uploadedFileIcon {
  width: 40px;
  min-width: 40px;
  height: 40px;
  min-height: 40px;

  display: flex;
  align-items: center;
  justify-content: center;

  border-radius: 8px;
  background: #dbeafe;

  font-size: 20px;
  font-weight: bold;
  color: #2563eb;
}

.uploadedFileName {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  margin-left: 12px;

  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

/* 右侧区域 */
.mainContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.mainHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: fit-content;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.mainHeaderInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.mainHeaderTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.mainHeaderDesc {
  font-size: 14px;
  color: #555;
}

.refreshButton {
  color: #333 !important;
  border-radius: 16px !important;
  background: #fff !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: fit-content;
  padding: 20px;
  margin: auto;
}
.emptyIcon {
  width: 70px;
  min-width: 70px;
  height: 70px;
  min-height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
  font-size: 35px;
  color: #999;
  margin-bottom: 20px;
}
.emptyTitle {
  font-size: 16px;
  font-weight: bold;
  color: #555;
  margin-bottom: 10px;
}
.emptyDesc {
  font-size: 14px;
  color: #999;
}

/* 内容渲染区域 */
.contentContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.contentHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: fit-content;
  padding: 16px;
  padding-top: 0px;
}

.streamingIndicator {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #1890ff;
}

.streamingIndicator span {
  color: #1890ff;
}

.llmContent {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
  line-height: 1.6;
  color: #333;
}

.homeworkMarkdown {
  font-size: 14px;
  line-height: 1.6 !important;
}

/* katex基础样式 */
.homeworkMarkdown :global(.katex) {
  font-size: 1.1em !important;
  color: #333 !important;
}

.homeworkMarkdown :global(.katex-display) {
  margin: 1em 0 !important;
  text-align: center !important;
  overflow-x: auto;
  overflow-y: hidden;
  clear: both;
}

.homeworkMarkdown :global(.katex-html) {
  overflow-x: auto;
  overflow-y: hidden;
}

.downloadContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: fit-content;
  padding: 0px;
  padding-top: 32px;
  gap: 30px;
}

.downloadItem {
  width: 100%;
  height: 44px;
  border-radius: 10px;
  font-weight: 500;
  font-size: 14px;
}

.downloadItemWord {
  color: #fff !important;
  background: #3b82f6 !important;
}

.downloadItemPDF {
  color: #333 !important;
  background: #e5e5e5 !important;
}

.downloadIcon {
  font-size: 20px;
  font-weight: bold;
}
