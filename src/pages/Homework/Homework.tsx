import FileUploadDragger from "@/components/FileUploadDragger";
import { getFileTypeForDify } from "@/utils";
import { aiChatApi } from "@/utils/api_chat";
import { aiHomeworkApi } from "@/utils/api_homework";
import { sendDifyMessage } from "@/utils/send_dify_message";
import {
  DownloadOutlined,
  FileTextOutlined,
  LoadingOutlined,
  ReadOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { App, Button } from "antd";
import dayjs from "dayjs";
import "github-markdown-css/github-markdown-light.css";
import "katex/dist/katex.min.css";
import React, { useEffect, useRef, useState } from "react";
import ReactMarkdown from "react-markdown";
import rehypeKatex from "rehype-katex";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import ThinkingCollapse from "../Chat/ThinkingCollapse";
import styles from "./Homework.module.css";

interface DifyFile {
  created_at: number;
  created_by: string;
  extension: string;
  id: string;
  mime_type: string;
  name: string;
  size: number;
}

interface UploadedFile extends DifyFile {
  file_name: string;
  category: string;
}

const Homework: React.FC = () => {
  const { message } = App.useApp();
  const [isUploading, setIsUploading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  // const [streamContent, setStreamContent] = useState<string>(mdContent);
  const [streamContent, setStreamContent] = useState<string>("");
  const [conversationId, setConversationId] = useState<string>("");
  const [isDownloading, setIsDownloading] = useState(false);

  const markdownEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    markdownEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [streamContent]);

  /** 文件上传 */
  const handleFileUpload = (file: File, fileList: File[]) => {
    if (isUploading) {
      message.warning("文件上传中，请稍后再试");
      return;
    }
    if (isStreaming) {
      message.warning("题目解答中，请稍后再试");
      return;
    }
    if (file.name.length > 100) {
      message.error("文件名过长，请修改后重新上传（100字符以内）");
      return;
    }
    setIsUploading(true);
    setUploadedFiles([]);

    const formData = new FormData();
    formData.append("file", file);
    console.log("准备上传参考资料文件:", file.name, "当前已有参考资料数量:", uploadedFiles.length);

    aiChatApi
      .uploadMultipart(formData)
      .then(res => {
        console.log("参考资料上传成功响应:", res);
        const result = {
          ...res,
          file_name: file.name,
          category: getFileTypeForDify(file.type || res.mime_type),
        };
        setUploadedFiles([result]);
        message.success(`${file.name} 上传成功`);
      })
      .catch(error => {
        console.error("参考资料上传失败:", error);
      })
      .finally(() => {
        setIsUploading(false);
      });

    return false; // 阻止默认上传行为
  };

  useEffect(() => {
    if (uploadedFiles.length > 0) {
      handleSendMessage();
    }
  }, [uploadedFiles]);

  // 处理发送消息
  const handleSendMessage = () => {
    if (uploadedFiles.length === 0) {
      console.warn("没有上传文件");
      return;
    }

    const files = uploadedFiles.map(file => ({
      type: file.category,
      transfer_method: "local_file",
      upload_file_id: file.id,
    }));

    setIsStreaming(true);

    const controller = sendDifyMessage(
      aiHomeworkApi.getHomeworkUrl(),
      {
        query: "请帮我解答这道题目",
        conversation_id: "",
        files: files,
      },
      {
        onMessage: (chunk, fullContent, event) => {
          //   console.log("收到消息片段:", chunk);
          //   console.log("完整内容:", fullContent);
          //   console.log("事件信息:", event);
          setStreamContent(fullContent);
        },
        onComplete: (finalContent, conversationId, messageId) => {
          console.log("消息完成:", finalContent);
          console.log("会话ID:", conversationId);
          console.log("消息ID:", messageId);
          setConversationId(conversationId || "");
          setIsStreaming(false);
        },
        onError: (error, errorEvent) => {
          console.error("发生错误:", error, errorEvent);
        },
      },
    );
  };

  const onRefresh = () => {
    if (isStreaming) {
      console.warn("onRefresh, 解答中");
      return;
    }
    if (isUploading || isDownloading) {
      console.warn("onRefresh, 上传、下载中");
      return;
    }
    if (uploadedFiles.length > 0) {
      handleSendMessage();
    }
  };

  const getDownloadFileName = (fileName: string, fileType: string): string => {
    if (!fileName) {
      // 没有文件名，使用默认值
      const defaultName = `download-${dayjs().format("YYYY-MM-DD")}.${fileType}`;
      return defaultName;
    }

    const lastDotIndex = fileName.lastIndexOf(".");
    if (lastDotIndex === -1) {
      // 没有扩展名，直接加上
      return `${fileName}.${fileType}`;
    }
    return `${fileName.slice(0, lastDotIndex)}.${fileType}`;
  };

  const onDownload = async (type: "docx" | "pdf") => {
    if (!streamContent) {
      console.warn("onDownload, 暂无内容");
      return;
    }
    if (isDownloading) {
      console.warn("onDownload, 下载中");
      return;
    }
    if (isStreaming || isUploading) {
      console.warn("onDownload, 解答、上传中");
      return;
    }
    setIsDownloading(true);
    const { replyContent } = parseThinkingContent(streamContent);
    const promise =
      type === "docx" ? aiHomeworkApi.exportHomeworkInWORD : aiHomeworkApi.exportHomeworkInPDF;
    try {
      const res = await promise({ content: replyContent });
      console.log("onDownload===", res);
      const blob = new Blob([res.data], { type: res.data.type || "application/octet-stream" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = getDownloadFileName(uploadedFiles[0]?.file_name || "", type);
      link.click();
    } catch (error) {
      console.error("下载失败:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  /** 解析流式内容中的思考内容（包括未完成的标签） */
  const parseThinkingContent = (content: string) => {
    // 先匹配完整的think标签
    const completeThinkRegex = /<think>([\s\S]*?)<\/think>/g;
    const completeParts: string[] = [];
    let match;

    // 提取所有完整的思考内容
    while ((match = completeThinkRegex.exec(content)) !== null) {
      completeParts.push(match[1]);
    }

    // 移除所有完整的think标签内容
    let remainingContent = content.replace(completeThinkRegex, "");

    // 检查是否有未完成的think标签（开始但没有结束）
    const incompleteThinkMatch = remainingContent.match(/<think>([\s\S]*?)$/);
    let incompletePart = "";

    if (incompleteThinkMatch) {
      incompletePart = incompleteThinkMatch[1];
      // 移除未完成的think标签内容
      remainingContent = remainingContent.replace(/<think>[\s\S]*?$/, "");
    }

    // 剩余内容就是回复内容
    const replyContent = remainingContent.trim();

    // 合并所有思考内容
    const allThinkingParts = [...completeParts];
    if (incompletePart) {
      allThinkingParts.push(incompletePart);
    }

    return {
      thinkingContent: allThinkingParts.join("\n\n"),
      replyContent,
    };
  };

  const renderContent = () => {
    // 如果没有内容，返回null不渲染任何内容
    if (!streamContent.trim()) {
      return null;
    }

    // 解析思考内容和回复内容
    const { thinkingContent, replyContent } = parseThinkingContent(streamContent);

    return (
      <div className={styles.contentContainer}>
        {isStreaming && (
          <div className={styles.contentHeader}>
            <div className={styles.streamingIndicator}>
              <LoadingOutlined spin />
              <span>AI正在解答中...</span>
            </div>
          </div>
        )}
        <div className={styles.llmContent}>
          {thinkingContent && (
            <ThinkingCollapse
              thinkingContent={thinkingContent}
              isStreaming={isStreaming}
              defaultExpanded={isStreaming}
            />
          )}
          {replyContent && (
            <div className={`${styles.homeworkMarkdown} markdown-body`}>
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkMath]}
                rehypePlugins={[
                  [
                    rehypeKatex,
                    {
                      strict: false, // 关闭严格检查，尽可能渲染内容
                      output: "html", // 控制katex输出为HTML而不是MathML，解决产生额外的空白和高度问题
                    },
                  ],
                ]}
              >
                {replyContent}
              </ReactMarkdown>
            </div>
          )}
          <div ref={markdownEndRef} />
        </div>
        <div className={styles.downloadContainer}>
          <Button
            className={`${styles.downloadItem} ${styles.downloadItemWord}`}
            variant="filled"
            color="primary"
            icon={<DownloadOutlined className={styles.downloadIcon} />}
            loading={isDownloading}
            disabled={isStreaming || isUploading}
            onClick={() => onDownload("docx")}
          >
            下载WORD 版本
          </Button>
          <Button
            className={`${styles.downloadItem} ${styles.downloadItemPDF}`}
            variant="filled"
            color="primary"
            icon={<DownloadOutlined className={styles.downloadIcon} />}
            loading={isDownloading}
            disabled={isStreaming || isUploading}
            onClick={() => onDownload("pdf")}
          >
            下载PDF版本
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.homeworkContainer}>
      {/* 页面标题 */}
      <div className={styles.homeworkTitle}>
        <div className={styles.titleLeft}>
          <div className={styles.titleText}>AI理科作业助手</div>
          <div className={styles.titleDesc}>支持高数、物理、化学作业智能解答与文档生成</div>
        </div>
      </div>
      <div className={styles.homeworkContent}>
        {/* 左侧：文件上传 */}
        <div className={styles.homeworkLeft}>
          {/* 文件上传区域 */}
          <FileUploadDragger
            title="上传作业文件"
            description={
              <>
                1. 每次仅支持上传一个文件
                <br />
                2. 支持图片（JPG、PNG、BMP、TIFF、WEBP）、Word文档（DOC、DOCX）、PDF文件，最大50MB
              </>
            }
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.bmp,.tiff,.webp"
            onUpload={(file, fileList) => {
              console.log("file=================", file);
              console.log("fileList=================", fileList);
              return handleFileUpload(file, fileList);
            }}
            disabled={isUploading || isStreaming || isDownloading}
            isUploading={isUploading}
            uploadingText="正在上传参考资料，请稍候..."
            multiple={false}
          >
            <div>
              {/* 上传文件列表 */}
              {uploadedFiles.length > 0 && <div className={styles.fileListTitle}>文件列表</div>}
              {uploadedFiles.length > 0 && (
                <div className={styles.uploadedFileList}>
                  {uploadedFiles.map(file => (
                    <div key={file.id} className={styles.uploadedFileItem}>
                      <div className={styles.uploadedFileIcon}>
                        <FileTextOutlined />
                      </div>
                      <div className={styles.uploadedFileName}>{file.file_name}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </FileUploadDragger>
        </div>
        {/* 右侧：解题详情*/}
        <div className={styles.homeworkMain}>
          <div className={styles.mainContainer}>
            <div className={styles.mainHeader}>
              <div className={styles.mainHeaderInfo}>
                <div className={styles.mainHeaderTitle}>解题详情</div>
                <div className={styles.mainHeaderDesc}>查看当前题目的详细答案和解题过程</div>
              </div>
              <Button
                className={styles.refreshButton}
                variant="filled"
                color="primary"
                loading={isStreaming}
                disabled={isUploading || isDownloading}
                icon={<SyncOutlined />}
                onClick={onRefresh}
              >
                刷新
              </Button>
            </div>
            {/* 动态内容区域 */}
            {streamContent.trim() ? (
              renderContent()
            ) : (
              <div className={styles.emptyContainer}>
                <div className={styles.emptyIcon}>
                  <ReadOutlined />
                </div>
                <div className={styles.emptyTitle}>暂无选中题目</div>
                <div className={styles.emptyDesc}>请先上传作业文件</div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Homework;
