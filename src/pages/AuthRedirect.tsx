import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import LoadingDots from '../components/LoadingDots';
import { authApi } from '@/utils/api';
import { useAuth } from '@/contexts/AuthContext';

/**
 * 授权中间页
 * 解析URL参数，自动调用后端API获取JWT，存储后跳转业务页
 */
const AuthRedirect: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { login } = useAuth();

  console.log('[AuthRedirect] 组件渲染');

  // 健壮的参数解析，兼容未编码的 redirect_url
  function getQueryParam(name: string) {
    // 支持 ?a=1&b=2 也支持 /auth/redirect?... 形式
    const search = window.location.search || window.location.href;
    const match = search.match(new RegExp(`[?&]${name}=([^&]+)`));
    return match ? decodeURIComponent(match[1]) : null;
  }

  useEffect(() => {    
    const code = getQueryParam('code');
    const platform = getQueryParam('platform');
    const redirectUrl = getQueryParam('redirect_url');

    if (!code || !platform || !redirectUrl) {
      setError('缺少必要参数');
      setLoading(false);
      return;
    }

    // 先展示 loading，所有认证准备好后再跳转
    authApi.saasLogin({ code, platform })
      .then(async (res: any) => {
        await login(res.jwt_token);
        window.location.href = redirectUrl;
      })
      .catch((err) => {
        setError(err.message || '授权失败');
        setLoading(false);
      });
  }, [login]);

  return (
    <div style={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column',
      alignItems: 'center', 
      justifyContent: 'center',
      background: '#f0f2f5'
    }}>
      {loading ? (
        <div style={{ textAlign: 'center' }}>
          <Spin size="large" />
          <div style={{ marginTop: 20, fontSize: 16 }}>
            <LoadingDots />
            <p>正在登录中，请稍候...</p>
          </div>
        </div>
      ) : (
        <div style={{ color: 'red', fontSize: 16 }}>
          {error || '授权失败，请重试'}
        </div>
      )}
    </div>
  );
};

export default AuthRedirect;