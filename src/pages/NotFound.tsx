import { Button, Result } from 'antd';
import React from 'react';
import { Link } from 'react-router-dom';




const NotFound: React.FC = () => {
    return (
        <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
        }}>
            <Result
                status="404"
                title="404"
                subTitle="访问的页面不存在"
                extra={
                    <Link to="/">
                        <Button type="primary">返回首页</Button>
                    </Link>
                }
            />
        </div>
    );
};

export default NotFound;
