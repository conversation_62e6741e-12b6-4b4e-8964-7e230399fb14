import CollegeAgentLogo from '@/assets/College-Agent-logo.svg';
import LoginDecor1 from '@/assets/login01.png';
import LoginDecor2 from '@/assets/login02.png';
import LoginDecor3 from '@/assets/login03.png';
import LoginDecor4 from '@/assets/login04.png';
import LoginDecor5 from '@/assets/login05.png';
import LoginDecor6 from '@/assets/login06.png';
import LoginDecor7 from '@/assets/login07.png';
import loginBg from '@/assets/logonbg.png';
import LoadingDots from '@/components/LoadingDots';
import MarkdownEditor from '@/components/MarkdownEditor';
import { theme } from '@/config/theme';
import { useAuth } from '@/contexts/AuthContext';
import { usePermission } from '@/contexts/PermissionContext';
import { authApi } from '@/utils/api';
import { decrypt, encrypt } from '@/utils/crypto'; // 导入加密/解密函数
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { App, Button, Card, Checkbox, Flex, Form, Input, Modal, Spin, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// MD协议文件
import privacyMarkdownRaw from '@/assets/privacy.md?raw';
import termsMarkdownRaw from '@/assets/terms.md?raw';

// 提取MD内容
const extractMarkdownContent = (raw: string) => {
  // 移除 "export default " 前缀和首尾的引号
  if (raw.startsWith('export default ')) {
    const jsonContent = raw.substring('export default '.length);
    try {
      return JSON.parse(jsonContent);
    } catch (e) {
      console.error('解析Markdown内容失败:', e);
      return raw;
    }
  }
  return raw;
};

const termsMarkdown = extractMarkdownContent(termsMarkdownRaw);
const privacyMarkdown = extractMarkdownContent(privacyMarkdownRaw);

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const { login, userInfo, loading: authLoading } = useAuth();
  const { authorizedMenus, loading: permissionLoading } = usePermission();
  
  // 新增状态：是否显示欢迎界面
  const [showWelcome, setShowWelcome] = useState(false);
  
  // 新增状态：是否同意用户协议
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [termsModalVisible, setTermsModalVisible] = useState(false);
  const [privacyModalVisible, setPrivacyModalVisible] = useState(false);

  // 手机号登录相关状态
  const [loginType, setLoginType] = useState<'password' | 'mobile'>('mobile'); // 默认显示手机号登录
  const [countdown, setCountdown] = useState(0); // 验证码倒计时
  const [sendingCode, setSendingCode] = useState(false); // 发送验证码loading状态
  
  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 验证码倒计时
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);
  
  // 获取重定向地址，默认为首页
  const from = location.state?.from?.pathname || '/';
  
  // 页面加载时尝试从localStorage获取存储的凭据
  useEffect(() => {
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
      try {
        const userData = JSON.parse(rememberedUser);
        const now = new Date().getTime();
        // console.log('encryptedPassword', userData.encryptedPassword);
        // 检查存储时间是否在30天内
        if (userData.expiry && userData.expiry > now) {
          // 从存储中获取加密的密码
          const encryptedPassword = userData.encryptedPassword;
          // 解密密码用于展示
          const decryptedPassword = decrypt(encryptedPassword);
          
          form.setFieldsValue({
            username: userData.username,
            password: decryptedPassword,
            remember: true,
          });
        } else {
          // 如果已过期，清除存储
          localStorage.removeItem('rememberedUser');
        }
      } catch (e) {
        console.error('解析存储的用户数据出错:', e);
        localStorage.removeItem('rememberedUser');
      }
    }
  }, [form]);
  
  // 当用户信息和权限加载完成后，跳转
  useEffect(() => {
    // 只有在显示欢迎界面时执行跳转逻辑
    if (!showWelcome) {
      return;
    }
    
    // 用户信息和权限都加载完成后跳转
    if (!authLoading && !permissionLoading && userInfo && authorizedMenus && authorizedMenus.length > 0) {
      const timer = setTimeout(() => {
        const targetPath = from === '/login' ? getDefaultHomePage() : from;
        // console.log('准备跳转到:', targetPath);
        navigate(targetPath, { replace: true });
      }, 1500);
      
      return () => clearTimeout(timer);
    }
    
  }, [authLoading, permissionLoading, userInfo, authorizedMenus, showWelcome]);
  
  // 找出第一个有效的菜单路径作为默认首页
  const getDefaultHomePage = (): string => {
    if (!authorizedMenus || authorizedMenus.length === 0) {
      return '/home'; // 如果没有授权菜单，默认到home页面
    }

    // 找第一个有效的菜单
    const firstMenu = authorizedMenus[0];
    
    // 如果第一个菜单有子菜单，使用第一个子菜单的路径
    if (firstMenu.children && firstMenu.children.length > 0) {
      const firstChildPath = firstMenu.children[0].path;
      // 检查路径是否需要与父路径组合
      if (firstChildPath.startsWith('/')) {
        return firstChildPath;
      } else {
        return `${firstMenu.path}${firstChildPath}`;
      }
    }
    
    // 直接使用第一个菜单的路径
    return firstMenu.path;
  };

  // 发送短信验证码
  const sendSmsCode = async () => {
    const mobile = form.getFieldValue('mobile');
    if (!mobile) {
      message.error('请输入手机号');
      return;
    }

    // 简单的手机号格式验证
    const mobileRegex = /^1[3-9]\d{9}$/;
    if (!mobileRegex.test(mobile)) {
      message.error('请输入正确的手机号格式');
      return;
    }

    try {
      setSendingCode(true);
      await authApi.sendSmsCode(mobile);
      message.success('验证码已发送');
      setCountdown(60); // 开始60秒倒计时
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      message.error(error.message || '发送验证码失败，请稍后重试');
    } finally {
      setSendingCode(false);
    }
  };

  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      let response;

      if (loginType === 'mobile') {
        // 手机号验证码登录
        const { mobile, code } = values;
        if (!mobile || !code) {
          message.error('请输入手机号和验证码');
          return;
        }
        response = await authApi.mobileLogin(mobile, code);
      } else {
        // 用户名密码登录
        const { username, password, remember } = values;
        const passwordToSend = encrypt(password);
        response = await authApi.adminLogin(username, passwordToSend);

        // 如果用户选择了"记住密码"，则存储凭据到localStorage，30天
        if (remember) {
          const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
          const expiry = new Date().getTime() + thirtyDaysInMs;

          localStorage.setItem('rememberedUser', JSON.stringify({
            username,
            encryptedPassword: passwordToSend,
            expiry
          }));
        } else {
          localStorage.removeItem('rememberedUser');
        }
      }
      
      if (response && response.access_token) {
        // 先显示欢迎界面
        setShowWelcome(true);
        message.success('登录成功');

        // 调用login方法并等待完成，确保token已保存
        await login(response.access_token);
      }
    } catch (error) {
      console.error('登录错误:', error);
      setShowWelcome(false); // 确保错误时重置欢迎状态
    } finally {
      setLoading(false);
    }
  };
  
  // 欢迎区域组件
  const WelcomeArea = () => (
    <Flex vertical align="center" justify="center" style={{ height: '100%', width: '100%' }}>
      <Title level={1} style={{ color: '#1890ff', marginBottom: 0 }}>
        College-Agent
      </Title>
      
      <Title level={4} style={{ marginBottom: 24, fontWeight: 'normal', color: '#ffffff' }}>
        高质量学生助手
      </Title>
      
      {(authLoading || permissionLoading) && (
        <Flex vertical align="center" gap={16}>
          <LoadingDots />
          <Text style={{ color: '#ffffff' }}>加载中，请稍候...</Text>
        </Flex>
      )}
      
      {!authLoading && !permissionLoading && (
        <Flex vertical align="center" gap={16}>
          <Spin size="large" />
          <Text style={{ color: '#ffffff' }}>准备进入系统...</Text>
        </Flex>
      )}
    </Flex>
  );
  
  // 添加协议弹窗函数
  const showTermsModal = (e: React.MouseEvent) => {
    e.preventDefault();
    setTermsModalVisible(true);
    // console.log('showTermsModal==', termsMarkdown);
  };

  const showPrivacyModal = (e: React.MouseEvent) => {
    e.preventDefault();
    setPrivacyModalVisible(true);
    // console.log('showPrivacyModal==', privacyMarkdown);
  };
  
  return (
    <div style={{ 
      display: 'flex',
      minHeight: '100vh',
      height: '100vh',
      position: 'relative',
      // background: `url(${LoginBg}) no-repeat center center`,
      // backgroundSize: 'cover',
      background: 'linear-gradient(135deg, #130215 0%, #070650 100%)',
      overflow: 'hidden',
    }}>
      {/* 左侧区域 - 在移动端隐藏 */}
      {!isMobile && (
        <div style={{
          position: 'relative',
          // width: '70%',
          flex: 1,
          display: 'flex',
          alignItems: 'center',
        }}>
          {/* <img
            src={LoginSlogan}
            alt=""
            style={{
              position: 'absolute',
              bottom: '11%',
              left: '20%',
              maxWidth: '100%',
              // maxHeight: '100%',
              objectFit: 'contain'
            }}
          /> */}
          <img
            src={loginBg}
            alt=""
            style={{
              position: 'absolute',
              left: '-0%',
              top: '-10%',
              // bottom: '5%',
              maxWidth: '100%',
              // width: '1298px',
              height: '994px',
              objectFit: 'contain'
            }}
          />
          
          {/* 左侧装饰 */}
          <img 
            src={LoginDecor1} 
            alt="" 
            style={{
              position: 'absolute',
              left: '13%',
              top: '12%',
              width: '182px',
              animation: 'float 6s ease-in-out infinite',
            }}
          />
          <img 
            src={LoginDecor2} 
            alt="" 
            style={{
              position: 'absolute',
              left: '30%',
              top: '8%',
              width: '171px',
              animation: 'float 6s ease-in-out infinite 1s',
            }}
          />
          <img 
            src={LoginDecor3} 
            alt="" 
            style={{
              position: 'absolute',
              left: '45%',
              top: '15%',
              width: '160px',
              animation: 'float 6s ease-in-out infinite 2s',
            }}
          />
          <img 
            src={LoginDecor4} 
            alt="" 
            style={{
              position: 'absolute',
              left: '26%',
              top: '26%',
              width: '98px',
              animation: 'float 6s ease-in-out infinite 1.5s',
            }}
          />
          <img 
            src={LoginDecor5} 
            alt="" 
            style={{
              position: 'absolute',
              left: '36%',
              top: '34%',
              width: '116px',
              animation: 'float 6s ease-in-out infinite 2.5s',
            }}
          />
          <img 
            src={LoginDecor6} 
            alt="" 
            style={{
              position: 'absolute',
              left: '25%',
              top: '46%',
              width: '104px',
              animation: 'float 6s ease-in-out infinite 3s',
            }}
          />
          <img 
            src={LoginDecor7} 
            alt="" 
            style={{
              position: 'absolute',
              left: '32%',
              top: '55%',
              width: '90px',
              animation: 'float 6s ease-in-out infinite 3.5s',
            }}
          />
        </div>
      )}

      {/* 右侧区域 - 登录表单或欢迎界面 */}
      <div style={{
        position: isMobile ? 'relative' : 'absolute',
        top: isMobile ? 0 : '50%',
        left: isMobile ? 0 : '60%',
        width: isMobile ? '100%' : '40%',
        height: isMobile ? '100%' : 'auto',
        transform: isMobile ? 'none' : 'translateY(-50%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: isMobile ? '0 20px' : 0,
      }}>
        {/* 登录表单 - 当showWelcome为false时显示 */}
        {!showWelcome ? (
          <Card 
            style={{ 
              width: '100%',
              maxWidth: '420px',
              padding: '10px',
              borderRadius: '16px',
              border: 'none',
              boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
              animation: 'fadeIn 0.8s ease-out',
              margin: isMobile ? '40px 0' : 0,
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: 32 }}>
              <div 
                style={{ 
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <img 
                  src={CollegeAgentLogo} 
                  alt="Ideagen Logo" 
                  style={{ 
                    width: 120,
                    height: 120,
                    animation: 'pulse 2s infinite',
                  }} 
                />
              </div>
              <Title level={2} style={{ marginBottom: 8, fontSize: '28px' }}>College-Agent</Title>
              <Title level={4} style={{ 
                fontFamily: 'SourceHanSansCN-Medium',
                fontSize: '16px',
                color: '#666666',
                letterSpacing: '0',
                fontWeight: 500,
                marginTop: 0, 
              }}>
                高质量学生助手
              </Title>
            </div>
            
            {/* 登录方式切换 - 暂时隐藏，只显示手机号登录 */}
            {false && (
              <div style={{ marginBottom: 24, textAlign: 'center' }}>
                <Button.Group>
                  <Button
                    type={loginType === 'mobile' ? 'primary' : 'default'}
                    onClick={() => setLoginType('mobile')}
                  >
                    手机号登录
                  </Button>
                  <Button
                    type={loginType === 'password' ? 'primary' : 'default'}
                    onClick={() => setLoginType('password')}
                  >
                    密码登录
                  </Button>
                </Button.Group>
              </div>
            )}

            <Form
              form={form}
              name="login"
              initialValues={{ remember: false }}
              onFinish={onFinish}
              autoComplete="off"
              size="large"
              layout="vertical"
            >
              {loginType === 'mobile' ? (
                // 手机号验证码登录表单
                <>
                  <Form.Item
                    name="mobile"
                    rules={[
                      { required: true, message: '请输入手机号' },
                      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式' }
                    ]}
                  >
                    <Input
                      prefix={<UserOutlined style={{ color: '#aaa' }} />}
                      placeholder="请输入手机号"
                      style={{
                        height: '48px',
                        borderRadius: '8px',
                        borderColor: '#e2e8f0'
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    name="code"
                    rules={[
                      { required: true, message: '请输入验证码' },
                      { len: 6, message: '验证码为6位数字' }
                    ]}
                  >
                    <Input
                      prefix={<LockOutlined style={{ color: '#aaa' }} />}
                      placeholder="请输入验证码"
                      suffix={
                        <Button
                          type="link"
                          size="small"
                          loading={sendingCode}
                          disabled={countdown > 0}
                          onClick={sendSmsCode}
                          style={{
                            padding: 0,
                            height: 'auto',
                            fontSize: '14px'
                          }}
                        >
                          {countdown > 0 ? `${countdown}s` : '获取验证码'}
                        </Button>
                      }
                      style={{
                        height: '48px',
                        borderRadius: '8px',
                        borderColor: '#e2e8f0'
                      }}
                    />
                  </Form.Item>
                </>
              ) : (
                // 用户名密码登录表单（暂时隐藏）
                <>
                  <Form.Item
                    name="username"
                    rules={[
                      { required: true, message: '请输入用户名' },
                      { min: 4, message: '用户名不能少于4个字符' },
                      { max: 20, message: '用户名不能超过20个字符' }
                    ]}
                  >
                    <Input
                      prefix={<UserOutlined style={{ color: '#aaa' }} />}
                      placeholder="用户名"
                      style={{
                        height: '48px',
                        borderRadius: '8px',
                        borderColor: '#e2e8f0'
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    name="password"
                    rules={[
                      { required: true, message: '请输入密码' },
                      { min: 6, message: '密码不能少于6个字符' },
                      { max: 50, message: '密码不能超过50个字符' },
                      {
                        pattern: /^(?=.*[a-z])(?=.*[A-Z0-9@#$%^&+=]).+$/,
                        message: '密码需包含小写字母和大写字母/数字/特殊字符'
                      }
                    ]}
                  >
                    <Input.Password
                      prefix={<LockOutlined style={{ color: '#aaa' }} />}
                      placeholder="密码"
                      style={{
                        height: '48px',
                        borderRadius: '8px',
                        borderColor: '#e2e8f0'
                      }}
                    />
                  </Form.Item>

                  <Form.Item name="remember" valuePropName="checked">
                    <Checkbox>记住密码</Checkbox>
                  </Form.Item>
                </>
              )}

              <Form.Item style={{ marginBottom: 16 }}>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading} 
                  // disabled={!agreeTerms}
                  onClick={(e) => {
                    if (!agreeTerms) {
                      e.preventDefault();
                      // 抖动提示
                      const termsCheckbox = document.getElementById('agreement-checkbox');
                      if (termsCheckbox) {
                        termsCheckbox.classList.add('shake-animation');
                        setTimeout(() => {
                          termsCheckbox.classList.remove('shake-animation');
                        }, 500);
                      }
                      return;
                    }
                  }}
                  style={{ 
                    width: '100%',
                    height: '48px',
                    borderRadius: '8px',
                    fontSize: '16px',
                    fontWeight: 500,
                    marginTop: '0',
                    color: '#fff',
                    background: theme.primaryColor,
                    boxShadow: `0 8px 16px -4px ${theme.primaryColor}40`,
                    transition: 'all 0.3s ease',
                    opacity: agreeTerms ? 1 : 0.7,
                  }}
                >
                  {loginType === 'mobile' ? '登录/注册' : '登录'}
                </Button>
              </Form.Item>

              <Form.Item style={{ marginBottom: 16 }}>
                <div id="agreement-checkbox">
                  <Checkbox 
                    checked={agreeTerms} 
                    onChange={(e) => setAgreeTerms(e.target.checked)}
                  >
                    我已阅读并同意
                    <a href="#" onClick={showTermsModal} style={{ marginLeft: 4 }}>《用户服务协议》</a>
                    与
                    <a href="#" onClick={showPrivacyModal}>《隐私政策》</a>
                  </Checkbox>
                </div>
              </Form.Item>
            </Form>
            
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '14px' }}>
                登录后即可使用完整功能
              </Text>
            </div>
          </Card>
        ) : (
          // 欢迎界面 - 当showWelcome为true时显示
          <div 
            style={{ 
              width: '100%',
              maxWidth: '420px',
              animation: 'fadeIn 0.8s ease-out',
              margin: isMobile ? '40px 0' : 0,
              // backgroundColor: '#ffffff',
            }}
          >
            <WelcomeArea />
          </div>
        )}
      </div>

      {/* 用户服务协议弹窗 */}
      <Modal
        title="用户服务协议"
        open={termsModalVisible}
        onCancel={() => setTermsModalVisible(false)}
        footer={[
          <Button key="close" type="primary" onClick={() => setTermsModalVisible(false)}>
            我已阅读
          </Button>
        ]}
        width={700}
      >
        <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
          <MarkdownEditor
            content={termsMarkdown}
            isEditMode={false}
            height="500px"
          />
        </div>
      </Modal>

      {/* 隐私政策弹窗 */}
      <Modal
        title="隐私政策"
        open={privacyModalVisible}
        onCancel={() => setPrivacyModalVisible(false)}
        footer={[
          <Button key="close" type="primary" onClick={() => setPrivacyModalVisible(false)}>
            我已阅读
          </Button>
        ]}
        width={700}
      >
        <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
          <MarkdownEditor
            content={privacyMarkdown}
            isEditMode={false}
            height="500px"
          />
        </div>
      </Modal>

      <style>
        {`
          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
          }
          
          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
          }
          
          @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
          }

          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
          }
          .shake-animation {
            animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
          }
          
          .ant-input:hover, .ant-input-password:hover {
            border-color: ${theme.primaryColor} !important;
            box-shadow: 0 0 0 2px ${theme.primaryColor}10 !important;
          }
          
          .ant-input:focus, .ant-input-password:focus {
            border-color: ${theme.primaryColor} !important;
            box-shadow: 0 0 0 2px ${theme.primaryColor}20 !important;
          }
          
          .ant-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px -4px ${theme.primaryColor}40 !important;
          }
          
          .ant-checkbox-wrapper:hover .ant-checkbox-inner,
          .ant-checkbox:hover .ant-checkbox-inner,
          .ant-checkbox-input:focus + .ant-checkbox-inner {
            border-color: ${theme.primaryColor} !important;
          }
          
          .ant-checkbox-checked .ant-checkbox-inner {
            background-color: ${theme.primaryColor} !important;
            border-color: ${theme.primaryColor} !important;
          }
          
          /* 响应式媒体查询 */
          @media (max-width: 992px) {
            .ant-card {
              padding: 24px !important;
            }
          }
          
          @media (max-width: 480px) {
            .ant-card {
              padding: 16px !important;
            }
            
            .ant-typography h2 {
              font-size: 24px !important;
            }
            
            .ant-typography h4 {
              font-size: 14px !important;
            }
          }

          .toastui-editor-contents {
            font-size: 14px !important;
          }
          .toastui-editor-contents h1 {
            font-size: 18px !important;
            margin: 16px 0 8px 0 !important;
          }
          .toastui-editor-contents h2 {
            font-size: 16px !important;
            margin: 16px 0 8px 0 !important;
          }
        `}
      </style>
    </div>
  );
};

export default Login; 