import { aiTrace<PERSON>pi } from "@/utils/api_report_config";
import { AuthManager } from "@/utils/auth";
import { AiTraceResponse, UploadedTraceFile } from "@/types/AiTrace";
import {
  CloudUploadOutlined,
  FileTextOutlined,
  LoadingOutlined,
  DownloadOutlined,
  SafetyOutlined,
} from "@ant-design/icons";
import { Button, message, Upload, Popconfirm, Space } from "antd";
import React, { useState } from "react";
import axios from "axios";
import styles from "./AiTraces.module.css";

const AiTraces: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedTraceFile | null>(null);
  const [isDownloadingWord, setIsDownloadingWord] = useState(false);
  const [isDownloadingPdf, setIsDownloadingPdf] = useState(false);

  /** 处理AI去痕文件上传 */
  const handleFileUpload = (file: File, fileList: File[]) => {
    if (isUploading) {
      message.warning("文件上传中，请稍后再试");
      return;
    }

    if (file.name.length > 100) {
      message.error("文件名过长，请修改后重新上传（100字符以内）");
      return;
    }

    try {
      setIsUploading(true);
      setUploadedFile(null);
      const formData = new FormData();
      formData.append("file", file);
      console.log("准备上传AI去痕文件:", file.name);

      // 调用后端AI去痕上传接口
      aiTraceApi
        .uploadAiTraces(formData)
        .then((res: AiTraceResponse) => {
          console.log("AI去痕上传成功响应:", res);
          const uploadedFileData: UploadedTraceFile = {
            trace_id: res.trace_id,
            file_name: res.file_name,
            file_size: res.file_size,
            char_count: res.char_count,
            processed_sections: res.processed_sections,
            trace_result: res.trace_result,
            upload_time: new Date().toLocaleString(),
          };
          setUploadedFile(uploadedFileData);
          // message.success(`${file.name} AI去痕处理完成`);
        })
        .catch(error => {
          console.error("AI去痕上传失败:", error);
          setIsUploading(false);
        })
        .finally(() => {
          setIsUploading(false);
        });
    } catch (error) {
      console.error("AI去痕上传失败:", error);
      setIsUploading(false);
    }
    return false; // 阻止默认上传行为
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 下载优化后的内容
  const handleDownload = async () => {
    if (!uploadedFile || !uploadedFile.trace_id) {
      message.warning("暂无可下载的内容");
      return;
    }
    if (isDownloadingWord) {
      message.warning("正在下载中，请稍后");
      return;
    }

    setIsDownloadingWord(true);
    try {
      message.loading({ content: '正在准备Word下载...', key: 'download' });
      // 过滤掉原文件名后缀
      const originalFilename = uploadedFile.file_name;
      const lastDotIndex = originalFilename.lastIndexOf('.');
      const baseName = lastDotIndex !== -1 ? originalFilename.substring(0, lastDotIndex) : originalFilename;
      const filename = `${baseName}_AI去痕.docx`;
      await aiTraceApi.downloadAiTraces(uploadedFile.trace_id, filename);
      message.success({ content: '下载成功', key: 'download' });
    } catch (error) {
      console.error('下载失败:', error);
      const errorMessage = error instanceof Error ? error.message : '下载失败，请稍后重试';
      message.error({ content: errorMessage, key: 'download' });
    } finally {
      setIsDownloadingWord(false);
    }
  };

  // 下载优化后的PDF内容
  const handleDownloadPdf = async () => {
    if (!uploadedFile || !uploadedFile.trace_id) {
      message.warning("暂无可下载的PDF内容");
      return;
    }
    if (isDownloadingPdf) {
      message.warning("正在下载中，请稍后");
      return;
    }

    setIsDownloadingPdf(true);
    try {
      message.loading({ content: '正在准备PDF下载...', key: 'downloadPdf' });
      // 过滤掉原文件名后缀
      const originalFilename = uploadedFile.file_name;
      const lastDotIndex = originalFilename.lastIndexOf('.');
      const baseName = lastDotIndex !== -1 ? originalFilename.substring(0, lastDotIndex) : originalFilename;
      const filename = `${baseName}_AI去痕.pdf`;
      await aiTraceApi.downloadAiTracesPdf(uploadedFile.trace_id, filename);
      message.success({ content: 'PDF下载成功', key: 'downloadPdf' });
    } catch (error) {
      console.error('PDF下载失败:', error);
      const errorMessage = error instanceof Error ? error.message : 'PDF下载失败，请稍后重试';
      message.error({ content: errorMessage, key: 'downloadPdf' });
    } finally {
      setIsDownloadingPdf(false);
    }
  };

  // 渲染对比内容
  const renderComparisonContent = () => {
    if (isUploading) {
      return (
        <div className={styles.emptyContainer}>
          <LoadingOutlined className={styles.emptyIcon} style={{ fontSize: '48px' }}/>
          <div className={styles.emptyTitle}>正在进行AI去痕处理...</div>
          <div className={styles.emptyDesc}>请稍候，这可能需要一些时间</div>
        </div>
      );
    }

    if (!uploadedFile || !uploadedFile.trace_result || uploadedFile.trace_result.length === 0) {
      return (
        <div className={styles.emptyContainer}>
          <div className={styles.emptyIcon}>
            <SafetyOutlined />
          </div>
          <div className={styles.emptyTitle}>暂未上传文档</div>
          <div className={styles.emptyDesc}>请先上传文件进行AI痕迹降低</div>
        </div>
      );
    }

    return (
      <div className={styles.comparisonContainer}>
        {uploadedFile.trace_result.map((result, index) => (
          <div key={index} className={styles.comparisonContent}>
            <div className={styles.originalSection}>
              <div className={styles.sectionTitle}>
                原始内容
              </div>
              <div className={`${styles.originalBox} ${styles.contentBox}`}>
                {(result.original || '暂无原始内容').split('\n').map((line: string, lineIndex: number) => {
                  return (
                    <p key={lineIndex}> {line} </p>
                  );
                })}
              </div>
              <div className={styles.modifiedSectionActions}></div>
            </div>
            
            <div className={styles.modifiedSection}>
              <div className={styles.sectionTitle}>
                优化后内容
              </div>
              <div className={`${styles.modifiedBox} ${styles.contentBox}`}>
                {(result.modified || '暂无优化内容').split('\n').map((line: string, lineIndex: number) => {
                  return (
                    <p key={lineIndex}> {line} </p>
                  );
                })}
              </div>
              <div className={styles.modifiedSectionActions}>
                <Popconfirm
                  title="选择下载格式"
                  description={
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button 
                        icon={<DownloadOutlined />} 
                        onClick={handleDownload} 
                        style={{ width: '100%' }}
                        loading={isDownloadingWord}
                      >
                        下载Word文档
                      </Button>
                      <Button 
                        icon={<DownloadOutlined />} 
                        onClick={handleDownloadPdf}
                        style={{ width: '100%' }}
                        loading={isDownloadingPdf}
                      >
                        下载PDF文档
                      </Button>
                    </Space>
                  }
                  okButtonProps={{ style: { display: 'none' } }}
                  cancelButtonProps={{ style: { display: 'none' } }}
                  placement="topRight"
                >
                  <Button
                    className={styles.downloadButton}
                    icon={<DownloadOutlined />}
                    size="small"
                    disabled={isUploading}
                  >
                    下载优化内容
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={styles.aiTracesContainer}>
      {/* 页面标题 */}
      <div className={styles.aiTracesTitle}>
        <div className={styles.titleLeft}>
          <div className={styles.titleText}>AI痕迹降低工具</div>
          <div className={styles.titleDesc}>智能优化文档内容，降低AI生成痕迹</div>
        </div>
      </div>
        
      <div className={styles.aiTracesContent}>
        {/* 左侧：文件上传 */}
        <div className={styles.aiTracesLeft}>
          {/* 文件上传区域 */}
          <div className={styles.uploaderContainer}>
            <div className={styles.uploaderInfo}>
              <div className={styles.uploaderTitle}>上传文档</div>
              <div className={styles.uploaderDesc}>
                支持PDF、Word文档、TXT、Markdown文件，最多1.5万字
              </div>
            </div>
            <div className={styles.uploadDragger}>
              <Upload.Dragger
                accept=".pdf,.doc,.docx,.txt,.md,.markdown"
                beforeUpload={(file, fileList) => {
                  console.log("AI去痕文件=================", file);
                  console.log("文件列表=================", fileList);
                  return handleFileUpload(file, fileList);
                }}
                showUploadList={false}
                multiple={false}
                disabled={isUploading}
              >
                <div className={styles.dragInner}>
                  <SafetyOutlined className={styles.dragIcon} />
                  <div className={styles.dragText}>拖拽文件到此处或点击上传</div>
                </div>
              </Upload.Dragger>
            </div>

            {/* 上传状态显示 */}
            {isUploading && (
              <div className={styles.uploadingInfo}>
                <LoadingOutlined />
                <div className={styles.uploadingText}>正在进行AI去痕处理，请稍候...</div>
              </div>
            )}

            {/* 已上传文件信息 */}
            {uploadedFile && (
              <div className={styles.uploadedFileContainer}>
                <div className={styles.uploadingTitle}>
                  文件信息
                </div>
                <div className={styles.uploadedFileItem}>
                  <div className={styles.uploadedFileIcon}>
                    <FileTextOutlined />
                  </div>
                  <div className={styles.uploadedFileDetails}>
                    <div className={styles.uploadedFileName}>
                      {uploadedFile.file_name}
                    </div>
                    <div className={styles.uploadedFileInfo}>
                      <span>大小: {formatFileSize(uploadedFile.file_size)}</span>
                      <span>字符数: {uploadedFile.char_count.toLocaleString()}</span>
                      {/* <span>处理片段: {uploadedFile.processed_sections}</span> */}
                    </div>
                    <div className={styles.uploadTime}>上传时间: {uploadedFile.upload_time}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧：内容对比*/}
        <div className={styles.aiTracesMain}>
          <div className={styles.mainContainer}>
            <div className={styles.mainHeader}>
              <div className={styles.mainHeaderInfo}>
                <div className={styles.mainHeaderTitle}>内容对比</div>
                <div className={styles.mainHeaderDesc}>选择文档查看对比结果</div>
              </div>
              {/* <Button
                  className={styles.refreshButton}
                  variant="filled"
                  color="primary"
                  icon={<SyncOutlined />}
              >
                  刷新
              </Button> */}
            </div>
            
            <div className={styles.mainContent}>
              {renderComparisonContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AiTraces;
