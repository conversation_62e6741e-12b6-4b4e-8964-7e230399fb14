import { Button, Result } from 'antd';
import React from 'react';
import { Link } from 'react-router-dom';

interface ComingSoonProps {
    title?: string;
    subTitle?: string;
    status?: "success" | "error" | "info" | "warning" | "404" | "403" | "500";
}

const ComingSoon: React.FC<ComingSoonProps> = ({
    title = "即将推出",
    subTitle = "敬请期待",
    status = "403"
}) => {
    return (
        <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
        }}>
            <Result
                status={status}
                title={title}
                subTitle={subTitle}
                extra={
                    <Link to="/">
                        <Button type="primary">返回首页</Button>
                    </Link>
                }
            />
        </div>
    );
};

export default ComingSoon;
