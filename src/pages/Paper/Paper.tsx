import CollegeAgentLogo from '@/assets/College-Agent-logo.svg';
import showLimitedModal from "@/components/LimitedModal";
import ToastMarkdownEditor, { MarkdownEditorRef } from "@/components/MarkdownEditor";
import ReportConfigPanel, { ReportConfigMethods } from "./Config.tsx";
import WorkflowPanel, { WorkflowMethods, WorkflowStage } from "./Workflow.tsx";
import { theme } from "@/config/theme";
import { useAuth } from "@/contexts/AuthContext";
import "./Paper.css";
import { ProjectConfig, ProjectStatus } from "@/types/ReportConfig";
import { historyReportApi } from "@/utils/api";
import { projectReportApi } from "@/utils/api_report";
import { projectConfigApi } from "@/utils/api_report_config";
import { AuthManager } from "@/utils/auth";
import { fetchSSE } from "@/utils/fetch_sse";
import {
  DownloadOutlined,
  EditOutlined,
  ReadOutlined,
  SaveOutlined,
  SendOutlined,
  StopOutlined,
  HistoryOutlined,
} from "@ant-design/icons";
import { App, Button, Col, Modal, Row, Space, Spin, Tooltip, Typography } from "antd";
import React, { useEffect, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import HistoryPanel from "./History";

const { Title, Text } = Typography;

// 本地报告数据
interface ReportLocal {
  id: string;
  name: string;
  status: ProjectStatus;
  outline_md?: string;
  report_md?: string;
}

/** 最大重连次数 */
const MAX_RETRY_COUNT = 3;
let retry_count = 0;

/** 体验内容长度 */
let reach_limit_length = false;

const Home: React.FC = () => {
  const { isTrial, enableGenerate, updateUserInfo } = useAuth();
  const { message } = App.useApp();
  const configPanelRef = useRef<ReportConfigMethods>(null);
  const workflowPanelRef = useRef<WorkflowMethods>(null);
  // Toast UI Editor引用
  const editorRef = useRef<MarkdownEditorRef>(null);
  // 流式数据连接引用
  const streamConnectionRef = useRef<EventSource | null>(null);
  // url获取id
  const [searchParams, setSearchParams] = useSearchParams();
  const id = searchParams.get("id");
  // 是否是生成中项目
  const [isProjectGenerating, setProjectGenerating] = useState(false);
  // 配置面板收起状态
  const [configPanelCollapsed, setConfigPanelCollapsed] = useState(
    JSON.parse(localStorage.getItem("CONFIG_PANEL_COLLAPSED") || "false"),
  );
  // 目录区域显示状态
  const [guideVisible, setGuideVisible] = useState(false);
  // 历史记录面板的显示
  const [showHistory, setShowHistory] = useState(false);

  // 内容生成与加载状态
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [reportData, setReportData] = useState<ReportLocal>({
    status: ProjectStatus.CONFIGURING,
  } as ReportLocal);
  // 流式数据 + 被终止前的临时内容
  const [streamContent, setStreamContent] = useState("");
  // 编辑器html内容，用于解析目录导航
  const [htmlContent, setHtmlContent] = useState("");
  // 工作流阶段
  const [workflowStage, setWorkflowStage] = useState<WorkflowStage>(WorkflowStage.INITIAL);
  // 当前流式标识，用于拦截过期流式请求
  const currentStreamIdRef = useRef<string>("");
  // 源码编辑：编辑器模式状态
  const [editorMode, setEditorMode] = useState<'wysiwyg' | 'markdown'>('wysiwyg');

  // 组件卸载时清理连接
  useEffect(() => {
    return () => closeStreamConnection();
  }, []);

  useEffect(() => {
    // 获取链接id
    if (!id) return;
    getProjectConfigFromUrl(id);
  }, [id]);

  const getProjectConfigFromUrl = async (configId: string) => {
    try {
      const res: ProjectConfig = await projectConfigApi.getProjectConfigById(configId);
      console.log("getProjectConfigFromUrl===", res);
      setReportData({
        id: configId,
        name: res.name,
        status: res.status,
        outline_md: "",
        report_md: "",
      } as ReportLocal);
      // 开始生成大纲～大纲生成成功，直接开始获取大纲内容
      if (
        [
          ProjectStatus.OUTLINE_GENERATING, // 大纲生成中
          ProjectStatus.OUTLINE_GENERATED, // 大纲生成完成
          ProjectStatus.REPORT_CANCELED, // 报告取消
          ProjectStatus.REPORT_FAILED, // 报告生成失败
        ].includes(res.status)
      ) {
        getOutlineStream(res.id);
      }
      // 开始生成论文～报告生成成功，直接开始获取报告内容
      if (
        [
          ProjectStatus.REPORT_GENERATING, // 报告生成中
          ProjectStatus.REPORT_GENERATED, // 报告生成完成
        ].includes(res.status)
      ) {
        getReportStream(res.id);
      }
    } catch (error) {
      console.error("查询项目配置数据失败:", error);
    }
  };

  /** 更新工作流面板 */
  const refreshWorkflow = () => {
    if (!workflowPanelRef.current) {
      console.warn("工作流面板未加载完成");
      return;
    }
    workflowPanelRef.current.refreshWorkflow();
  };

  /** url拼接id（静默） */
  const updateUrlWithProjectId = (configId: string) => {
    setSearchParams({ id: configId });
  };

  /** 处理历史记录选择 */
  const handleHistorySelect = (selectedId: string) => {
    if (id !== selectedId) {
      updateUrlWithProjectId(selectedId);
    }
    // 自动关闭历史面板
    setShowHistory(false);
  };

  /** 生成大纲 */
  const handleGenerateOutlineClick = async () => {
    if (!enableGenerate) {
      showLimitedModal("usage");
      return;
    }
    if (!configPanelRef.current) {
      console.warn("配置面板未加载完成");
      return;
    }
    setIsLoading(true);
    try {
      // 从配置面板获取项目配置ID
      const projectConfig = await configPanelRef.current.exportConfigData();
      if (!projectConfig?.id) {
        setIsLoading(false);
        return;
      }
      console.log("项目配置ID:", projectConfig.id);
      updateUrlWithProjectId(projectConfig.id);
      // 更新新建报告状态
      setReportData(prev => ({
        ...prev,
        status: ProjectStatus.OUTLINE_GENERATING,
        name: projectConfig.name,
      }));
      getOutlineStream(projectConfig.id);
    } catch (error) {
      onGenerationError(error, "生成大纲失败");
      updateUserInfo();
    }
  };

  /** 根据配置ID获取大纲内容 */
  const getOutlineStream = (configId: string) => {
    // 保存id
    const report = {
      id: configId,
      outline_md: "",
      report_md: "",
    };
    console.warn("getOutlineStream, report===========", report);
    setReportData(prev => ({ ...prev, ...report }));
    // 获取大纲流式内容
    const streamUrl = projectReportApi.getOutlineStreamUrl(configId);
    console.warn("getOutlineStream, streamUrl===========", streamUrl);
    const sId = updateStreamId(configId);
    startContentStream(configId, streamUrl, sId, false);
  };

  /** 生成论文 */
  const handleGenerateReportClick = async () => {
    if (!enableGenerate) {
      showLimitedModal("usage");
      return;
    }
    if (!reportData || !reportData.id) {
      message.error("无法获取项目配置ID");
      return;
    }

    // 显示确认弹窗
    // Modal.confirm({
    //   title: "确认生成",
    //   content: "若生成论文内容，将扣除一次使用次数。请问是否确认进行此操作？",
    //   okText: "确认",
    //   cancelText: "取消",
    //   onOk: async () => {
        setIsLoading(true);
        try {
          // 生成论文
          await projectReportApi.generateReport(reportData.id);
          // 更新新建报告状态
          setReportData(prev => ({ ...prev, status: ProjectStatus.REPORT_GENERATING }));
          // 获取报告
          getReportStream(reportData.id);
        } catch (error) {
          onGenerationError(error, "生成论文失败");
        }
    //   },
    //   onCancel: () => {
    //     console.log("取消生成");
    //   },
    // });
  };
  /** 根据配置ID获取报告内容 */
  const getReportStream = (configId: string, jumpOut: boolean = false) => {
    // 获取报告流式内容
    const streamUrl = projectReportApi.getReportStreamUrl(configId);
    const sId = updateStreamId(configId);
    console.warn("getReportStreamUrl, streamUrl===========", streamUrl, sId, jumpOut);
    startContentStream(configId, streamUrl, sId, true, jumpOut);
  };

  const updateStreamId = (configId: string) => {
    const streamId = `${configId}-${Date.now()}-${Math.random()}`;
    currentStreamIdRef.current = streamId;
    return streamId;
  };

  /** 启动流式内容获取 */
  const startContentStream = async (
    configId: string,
    streamUrl: string,
    streamId: string,
    isReport: boolean = false,
    jumpOut: boolean = false, // 刷新一次后强制跳出，防止死循环，用于刷新后端处理后的最新内容（脚标截断问题）
  ) => {
    // 检查是否是当前有效的流
    const isCurrentStream = () => streamId === currentStreamIdRef.current;
    const data = await projectConfigApi.getProjectConfigById(configId);
    const isOutlineGenerating = data.status === ProjectStatus.OUTLINE_GENERATING; // 大纲生成中
    const isReportGenerating = data.status === ProjectStatus.REPORT_GENERATING; // 论文生成中
    // 当前为大纲视图
    const isOutlineView = [
      ProjectStatus.OUTLINE_GENERATING,
      ProjectStatus.OUTLINE_GENERATED,
      ProjectStatus.OUTLINE_FAILED,
      ProjectStatus.OUTLINE_CANCELED,
    ].includes(data.status);

    // 关闭之前的连接
    closeStreamConnection();

    // 重置状态和设置模式
    setIsStreaming(true);
    setIsLoading(true);
    setStreamContent("");
    reach_limit_length = false;
    // 使用fetchSSE获取流式内容
    const controller = fetchSSE(streamUrl, {
      onStart: () => {
        if (!isCurrentStream()) {
          console.warn("Stream已过期，取消onStart回调");
          return;
        }
        // 刷新工作流
        refreshWorkflow();
      },
      onMessage: (chunk, fullContent) => {
        if (!isCurrentStream()) {
          console.warn("Stream已过期，取消onMessage回调");
          return;
        }
        // 更新React状态，实时显示内容
        setStreamContent(fullContent);
      },
      onComplete: finalContent => {
        if (!isCurrentStream()) {
          console.warn("Stream已过期，取消onComplete回调");
          return;
        }
        // 更新本地数据
        const report = isReport ? { report_md: finalContent } : { outline_md: finalContent };
        console.warn("onComplete, report===========", report);
        setReportData(prev => ({ ...prev, ...report }));
        // 更新状态
        getProjectStatus(configId);
        // 刷新工作流
        refreshWorkflow();
        // 重置流状态
        setStreamContent("");
        setIsStreaming(false);
        setIsLoading(false);
        console.log(`${isReport ? "论文" : "大纲"}生成完成，内容长度：`, finalContent.length);
        retry_count = 0;
        updateUserInfo();
        console.log("onComplete, jumpOut111===========", data.status, jumpOut);
        if (isTrial && !isOutlineView) {
          reach_limit_length = true;
        }
        // 大纲生成完成，重新获取
        if (isOutlineGenerating && !jumpOut) {
          console.log("onComplete, jumpOut333===========", data.status, jumpOut);
          getOutlineStream(configId);
        }
        // 论文生成完成，重新获取
        if (isReportGenerating && !jumpOut) {
          console.log("onComplete, jumpOut222===========", data.status, jumpOut);
          getReportStream(configId, true);
        }
      },
      onError: error => {
        if (!isCurrentStream()) {
          console.warn("Stream已过期，取消onError回调");
          return;
        }
        // 清理本地数据
        const report = isReport ? { report_md: "" } : { outline_md: "" };
        setReportData(prev => ({ ...prev, ...report }));
        // 更新状态
        getProjectStatus(configId);
        // 刷新工作流
        refreshWorkflow();
        // 重置流状态
        // setStreamContent(""); // 发生错误、主动终止时，后端不留存已生成内容，这里同步清空，不让用户继续编辑
        setIsStreaming(false);
        setIsLoading(false);
        // 错误处理
        console.error(`${isReport ? "论文" : "大纲"}生成错误:`, error);
        if (String(error).includes("network error")) {
          // 特殊处理网络连接错误
          if (retry_count < MAX_RETRY_COUNT) {
            message.warning("网络连接错误，正在重连...");
            console.warn("retry count===", `（${retry_count + 1}/${MAX_RETRY_COUNT}）`);
            setTimeout(() => startContentStream(configId, streamUrl, streamId, isReport), 1000);
            retry_count++;
          } else {
            message.error("网络连接错误，请手动刷新后重试");
            retry_count = 0;
          }
        } else {
          message.error(error.message);
        }
        updateUserInfo();
      },
    }, data.name);
    // 保存控制器引用，支持中止操作
    streamConnectionRef.current = {
      close: () => controller.abort(),
    } as unknown as EventSource;
  };
  /** 获取最新project，用于更新状态 */
  const getProjectStatus = async (configId: string) => {
    try {
      const res: ProjectConfig = await projectConfigApi.getProjectConfigById(configId);
      console.log("getProjectStatus===", res);
      // 更新报告状态
      setReportData(prev => ({ ...prev, status: res.status }));
    } catch (error) {
      console.error("查询项目配置状态失败:", error);
      // message.error("查询项目配置状态失败");
    }
  };

  /** 处理生成过程中的错误 */
  const onGenerationError = (error: any, errorMessage: string) => {
    console.error(`${errorMessage}:`, error);
    // message.error(`${errorMessage}`);
    setStreamContent("");
    setIsLoading(false);
  };

  /** 关闭流式连接 */
  const closeStreamConnection = () => {
    if (streamConnectionRef.current) {
      console.warn("closeStreamConnection=======");
      streamConnectionRef.current.close();
      streamConnectionRef.current = null;
    }
  };

  /** 进入编辑模式 - wysiwyg(所见即所得) 、 markdown(源码编辑) */
  const onEditContent = (isReport: boolean = false, editMode: 'wysiwyg' | 'markdown' = 'wysiwyg') => {
    if (!reportData) return;
    if (isTrial) {
      showLimitedModal("edit");
      return;
    }
    const content = isReport ? reportData?.report_md || "" : reportData?.outline_md || "";
    if (!content) return;
    
    // 设置编辑模式
    setEditorMode(editMode);
    setIsEditing(true);
    // 编辑时隐藏目录区域
    setGuideVisible(false);
  };

  /** 保存编辑后的内容 */
  const onSaveContent = async (isReport: boolean = false) => {
    // console.log("onSaveContent reportData11111===========", reportData)
    if (!editorRef.current || !reportData || !reportData.id) {
      console.error("无法获取项目配置ID");
      return;
    }
    if (isTrial) {
      showLimitedModal("save");
      return;
    }
    // console.log("onSaveContent reportData22222===========", reportData)
    setIsLoading(true);
    try {
      const newContent = editorRef.current.getMarkdown();
      // 调用API保存大纲内容到后端
      let promise = isReport
        ? projectConfigApi.updateReport(reportData.id, { manual_modified_report: newContent })
        : projectConfigApi.updateManualOutline(reportData.id, {
            manual_modified_outline: newContent,
          });
      await promise;
      message.success("内容保存成功");
      // 更新本地数据
      const content = isReport ? { report_md: newContent } : { outline_md: newContent };
      setReportData(prev => ({ ...prev, ...content }));
      refreshWorkflow();
    } catch (error) {
      console.error(`保存${isReport ? "论文" : "大纲"}内容失败:`, error);
      // message.error(`保存${isReport ? "报告" : "大纲"}内容失败`);
    } finally {
      // 退出编辑模式
      setIsEditing(false);
      setIsLoading(false);
    }
  };

  /** 终止生成内容 */
  const handleStopGeneration = async (isReport: boolean = false) => {
    if (!reportData || !reportData.id) {
      console.error("无法获取项目配置ID");
      return;
    }
    setIsLoading(true);
    try {
      // 调用API终止生成
      let promise = isReport
        ? projectReportApi.stopReportGeneration(reportData.id)
        : projectReportApi.stopOutlineGeneration(reportData.id);
      await promise;
      console.log(`已终止${isReport ? "论文" : "大纲"}生成`);
    } catch (error) {
      console.error(`终止${isReport ? "论文" : "大纲"}生成失败:`, error);
      // message.error(`终止${isReport ? "论文" : "大纲"}生成失败`);
    } finally {
      setIsLoading(false);
    }
  };

  /** 渲染论文内容区域 */
  const renderContentArea = () => {
    // 加载中或流式获取中
    if (isStreaming) {
      return (
        <>
          <Spin spinning={true} tip="正在生成材料，在此期间，你可以离开页面...">
            <div className="spin-content" style={{ height: "60px", width: "100%" }} />
          </Spin>
          {streamContent && (
            <ToastMarkdownEditor
              projectId={reportData.id || ""}
              content={streamContent}
              isEditMode={false}
              isStreaming={isStreaming}
              // 源码编辑：流式显示时也传递编辑器模式（虽然是预览模式，但保持一致性）
              editorType={editorMode}
            />
          )}
        </>
      );
    }
    // 已有内容 - 编辑模式或查看模式
    // streamContent - 存在临时内容，仅提供只读功能，刷新清除
    if (reportData && (reportData?.outline_md || reportData?.report_md || streamContent)) {
      const currentContent = isAfterReportGenerated()
        ? reportData?.report_md || ""
        : reportData?.outline_md || "";

      // 优先展示streamContent
      return (
        <ToastMarkdownEditor
          ref={editorRef}
          projectId={reportData.id || ""}
          content={streamContent ? streamContent : currentContent}
          isEditMode={isEditing}
          trialMask={isTrial && reach_limit_length}
          isStreaming={isStreaming}
          // 源码编辑：传递编辑器模式
          editorType={editorMode}
        />
      );
    }
    // 空状态 - 无大纲，无临时内容
    if (isBeforeOutlineGenerated() && !streamContent) {
      return (
        <div className={`empty-area ${!enableGenerate ? "nostart" : ""}`}>
          <h3>开始您的学术写作</h3>
          <div className="title">请在左侧填写论文基本信息，然后点击"生成大纲"开始创作</div>
          <Button 
            type="primary" 
            onClick={handleGenerateOutlineClick} 
          >
            生成大纲
          </Button>
          {/* <div className="start-btn-wrapper" onClick={handleGenerateOutlineClick}>
            <img className="start-btn" src={CollegeAgentLogo} />
          </div> */}
        </div>
      );
    }
  };

  /** 大纲未生成前 */
  const isBeforeOutlineGenerated = () => {
    return [
      ProjectStatus.CONFIGURING, // 配置中
      ProjectStatus.OUTLINE_GENERATING, // 大纲生成中
      ProjectStatus.OUTLINE_CANCELED, // 大纲取消
      ProjectStatus.OUTLINE_FAILED, // 大纲生成失败
    ].includes(reportData.status);
  };

  /** 大纲已生成后 */
  const isAfterOutlineGenerated = () => {
    return [
      ProjectStatus.OUTLINE_GENERATING, // 大纲生成中
      ProjectStatus.OUTLINE_GENERATED, // 大纲生成完成
      ProjectStatus.REPORT_CANCELED, // 报告取消
      ProjectStatus.REPORT_FAILED, // 报告生成失败
    ].includes(reportData.status);
  };

  /** 报告已生成后 */
  const isAfterReportGenerated = () => {
    const res = [
      ProjectStatus.REPORT_GENERATED, // 报告生成完成
    ];
    /**
     * 体验用户需要增加论文生成中状态，
     * 因为字数达上限后，前端需要表现为【论文完成】，
     * 但后端状态仍然为【论文中】，等待实际生成结束后才更新为【论文完成】
     * 在此处统一处理，不影响正常用户
     */
    if (isTrial) {
      res.push(ProjectStatus.REPORT_GENERATING);
    }

    return res.includes(reportData.status);
  };

  /** 判断是否可以编辑内容 */
  const canEditContent = () => {
    return [
      ProjectStatus.REPORT_GENERATED, // 报告生成完成
    ].includes(reportData.status);
  };

  /** 判断是否在生成过程中 */
  const isGenerating = () => {
    return [
      ProjectStatus.OUTLINE_GENERATING, // 大纲生成中
      ProjectStatus.REPORT_GENERATING, // 报告生成中
    ].includes(reportData.status);
  };

  /** 判断是否可以重新生成
   * 只有在报告生成完成，且不在审查或去痕过程中才能重新生成
   */
  const canRegenerate = () => {
    return (
      [
        ProjectStatus.REPORT_GENERATED, // 报告生成完成
        ProjectStatus.REPORT_FAILED, // 报告生成失败
        ProjectStatus.REPORT_CANCELED, // 报告取消
      ].includes(reportData.status)
    );
  };

  /** 渲染顶部操作按钮 */
  const renderActionButtons = () => {
    /** 需要编辑、保存 - 有大纲或报告，非加载中，无临时内容 */
    const needUpdate =
      reportData &&
      (reportData?.outline_md || reportData?.report_md) &&
      !isStreaming &&
      !streamContent;
    /** 需要终止按钮 - 大纲或报告生成中，非体验用户被限制状态 */
    const needStop = isGenerating() && !(isTrial && reach_limit_length);
    /** 需要禁用导出按钮 - 无大纲，无报告，加载中，编辑中，有临时内容 */
    const needDisableExport =
      (!reportData.outline_md && !reportData.report_md) ||
      isStreaming ||
      isEditing ||
      !!streamContent;
    /** 需要禁用目录按钮 - 无大纲，无报告，加载中，编辑中 */
    const needDisableGuide =
      (!reportData.outline_md && !reportData.report_md) ||
      isStreaming ||
      isEditing;

    return (
      <div className="action-bar">
        <Title
          className="title"
          level={4}
          style={{
            marginLeft: !needDisableGuide ? 20 : 0,
          }}
        >
          论文写作
        </Title>
        {/* {!needDisableGuide && (
          <Tooltip title="查看目录" placement="right">
            <Button
              className="guide-btn"
              type="primary"
              // color="primary"
              variant="outlined"
              icon={<ReadOutlined />}
              disabled={needDisableGuide}
              // onClick={onPoperClick}
              onClick={onGuideClick}
            >
            </Button>
          </Tooltip>
        )} */}
        <Space>
          {needUpdate && (
            <>
              {/* 可视编辑按钮 */}
              {(!isEditing || (isEditing && editorMode === 'wysiwyg')) && (
                <Button
                  color="primary"
                  variant="outlined"
                  icon={!isEditing ? <EditOutlined /> : <SaveOutlined />}
                  // disabled={isTrial}
                  // className={isTrial ? "nostart" : ""}
                  onClick={() => {
                    if (!isEditing) {
                      onEditContent(isAfterReportGenerated(), 'wysiwyg'); // 可视编辑模式
                    } else {
                      onSaveContent(isAfterReportGenerated()); // 可视保存
                    }
                  }}
                >
                  {!isEditing ? `可视编辑` : `可视更新`}
                </Button>
              )}
                 
              {/* Markdown编辑按钮 */}
              {(!isEditing || (isEditing && editorMode === 'markdown')) && (
                <Button
                  color="primary"
                  variant="outlined"
                  icon={!isEditing ? <EditOutlined /> : <SaveOutlined />}
                  // disabled={isTrial}
                  // className={isTrial ? "nostart" : ""}
                  onClick={() => {
                    if (!isEditing) {
                      onEditContent(isAfterReportGenerated(), 'markdown'); // Markdown编辑模式
                    } else {
                      onSaveContent(isAfterReportGenerated()); // Markdown模式保存
                    }
                  }}
                >
                  {!isEditing ? `Markdown编辑` : `Markdown更新`}
                </Button>
              )}
            </>
          )}
          {!needDisableExport && (
            <Tooltip title="导出为word文档">
              <Button
                // className={isTrial ? "nostart" : "export-btn"}
                className={"export-btn"}
                color="primary"
                variant="outlined"
                disabled={needDisableExport || isDownloading}
                icon={<DownloadOutlined />}
                loading={isDownloading}
                onClick={handleDownload}
              >
                {/* 导出 */}
              </Button>
            </Tooltip>
          )}
          {/* {needStop && (
            <Button
              className="stop-btn"
              type="primary"
              danger
              icon={<StopOutlined />}
              onClick={() => handleStopGeneration(!isBeforeOutlineGenerated())}
              style={{
                height: 50,
                borderRadius: theme.borderRadius,
                fontSize: 16,
                boxShadow: theme.buttonShadow,
                flex: 1,
                background: "linear-gradient(45deg, #ff4d4f, #ff7875)",
              }}
            >
              {isBeforeOutlineGenerated() ? "终止生成大纲" : "终止生成论文"}
            </Button>
          )} */}
          {/* 生成大纲 - 无大纲，非加载中，有临时内容*/}
          {isBeforeOutlineGenerated() && !isStreaming && streamContent && (
            // 有临时内容，内容区生成按钮被遮挡，需要顶部生成按钮
            <Button
              className="generate-btn"
              type="primary"
              icon={<SendOutlined />}
              onClick={() => handleGenerateOutlineClick()}
              disabled={isEditing || isLoading || !enableGenerate}
              style={{
                opacity: isEditing || isLoading || !enableGenerate ? 0.6 : 1,
              }}
            >
              生成大纲
            </Button>
          )}
          {/* 生成论文 */}
          {!isBeforeOutlineGenerated() &&
            !isStreaming &&
            // 体验用户 + 达到限制
            !(isTrial && reach_limit_length) && (
              <Button
                className="generate-btn"
                type="primary"
                icon={<SendOutlined />}
                onClick={() => handleGenerateReportClick()}
                disabled={isEditing || isLoading || !enableGenerate}
                style={{
                  opacity: isEditing || isLoading || !enableGenerate ? 0.6 : 1,
                }}
              >
                {isAfterReportGenerated() ? "重新" : ""}
                生成论文
              </Button>
            )}
        </Space>
      </div>
    );
  };

  const onGuideClick = () => {
    if (!editorRef.current) {
      console.warn("editorRef is null !!!");
    }
    const content = editorRef.current?.getHTML();
    setHtmlContent(content || "");
    // 切换目录区域显示
    setGuideVisible(!guideVisible);
    if (guideVisible) {
      // 切换配置面板收起
      setConfigPanelCollapsed(false);
    } else {
      // 切换配置面板展开
      setConfigPanelCollapsed(true);
    }
  };

  /** 下载报告文件 */
  const handleDownload = async () => {
    if (!reportData.id) return;
    if (isTrial) {
      showLimitedModal("export");
      return;
    }
    if (isDownloading) {
      message.warning("正在下载中，请稍后");
      return;
    }
    const isOutline = reportData.status === ProjectStatus.OUTLINE_GENERATED;
    const type = isOutline ? "大纲" : "论文";
    setIsDownloading(true);
    try {
      message.loading({ content: `正在下载${type}...`, key: 'download' });
      const filename = `${reportData.name}_${type}.docx`;
      
      const promise = isOutline
        ? historyReportApi.downloadOutline(reportData.id, filename)
        : historyReportApi.downloadReport(reportData.id, filename);
      await promise;
      message.success({ content: `${type}下载成功`, key: 'download' });
    } catch (error) {
      console.error(`下载${type}失败:`, error);
      const errorMessage = error instanceof Error ? error.message : "下载失败，请稍后重试";
      message.error({ content: errorMessage, key: 'download' });
    } finally {
      setIsDownloading(false);
    }
  };

  // 配置面板收起状态变化
  const handleConfigPanelCollapse = (collapsed: boolean) => {
    setConfigPanelCollapsed(collapsed);
    localStorage.setItem("CONFIG_PANEL_COLLAPSED", JSON.stringify(collapsed));
  };

  return (
    <div className="paper-page-container">
      {/* 历史记录面板 */}
      <div className={`paper-history-panel ${showHistory ? 'show' : 'hide'}`}>
        <HistoryPanel 
          onClose={() => setShowHistory(false)}
          shouldRefresh={showHistory}
          onSelect={handleHistorySelect} // 历史id
        />
      </div>

      <div className="paper-container">
        {/* 上方工作流程面板 */}
        <div className="header-content">
          <div className="titleText">
            <Tooltip title="查看历史记录">
              <Button
                type="default"
                icon={<HistoryOutlined />}
                className="historyButton"
                onClick={() => setShowHistory(!showHistory)}
              />
            </Tooltip>
            论文写作助手
          </div>
          <div className="workflow-card">
            <WorkflowPanel
              ref={workflowPanelRef}
              configId={reportData.id || ""}
            />
          </div>
        </div>

        {/* 下方左右布局 */}
        <div className="main-content-area">
          <Row gutter={16} style={{ height: "100%" }}>
            {/* 左侧配置面板 */}
            <Col 
              span={configPanelCollapsed ? 2 : 6}
              style={{ height: "100%" }}
            >
              <div className="config-card">
                <ReportConfigPanel
                  ref={configPanelRef}
                  configId={reportData.id || ""}
                  status={reportData.status}
                  isLoading={isLoading || isStreaming}
                  collapsed={configPanelCollapsed}
                  onCollapseChange={handleConfigPanelCollapse}
                />
              </div>
            </Col>

            {/* 右侧内容区域 */}
            <Col 
              span={configPanelCollapsed ? 22 : 18}
              style={{ height: "100%" }}
            >
              <div className="content-card">
                <div
                  style={{
                    height: "100%",
                    overflow: "hidden",
                    display: "flex",
                    flexDirection: "column",
                    background: "#fff",
                    borderRadius: "12px",
                  }}
                >
                  {renderActionButtons()}
                  {renderContentArea()}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </div>
  );
};

export default Home;
