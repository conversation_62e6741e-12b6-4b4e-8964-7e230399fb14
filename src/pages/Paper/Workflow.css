.workflow-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title-container {
    height: 50px;
    min-height: 50px;
    padding: 10px 16px;
    border-bottom: 1px solid #f0f0f0;

    .ant-typography {
      margin: 0;
    }
  }

  .workflow-steps-container {
    flex: 1;
    padding: 8px 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    .workflow-step-item {
      display: flex;
      align-items: center;
      
      .step-content {
        display: flex;
        align-items: center;
        text-align: center;
        
        .step-icon {
          width: 50px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;
          transition: all 0.3s ease;
          
          img {
            width: 28px;
            height: 28px;
          }
        }

        .step-title {
          font-size: 16px;
          font-weight: 600;
          white-space: nowrap;
          transition: all 0.3s ease;
        }
      }

      .step-connector {
        width: 100px;
        height: 2px;
        margin: 0 16px;
        position: relative;
        
        .connector-line {
          width: 100%;
          height: 2px;
          background: #e8e8e8;
          position: relative;
        }
      }
    }
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .workflow-panel {
    .workflow-steps-container {
      padding: 20px 16px;
      flex-direction: column;
      gap: 24px;
      
      .workflow-step-item {
        flex-direction: column;
        
        .step-content {
          .step-icon {
            width: 48px;
            height: 48px;
            
            img {
              width: 24px;
              height: 24px;
            }
          }
          
          .step-title {
            font-size: 14px;
          }
        }

        .step-connector {
          width: 2px;
          height: 40px;
          margin: 12px 0;
          
          .connector-line {
            width: 2px;
            height: 100%;
          }
        }
      }
    }
  }
}
