import side_img from "@/assets/side-icon.png";
import { theme } from "@/config/theme";
import {
  ProjectConfig,
  ProjectConfigCreate,
  ProjectStatus,
} from "@/types/ReportConfig";
import { projectReportApi } from "@/utils/api_report";
import {
  projectConfigApi,
  customTemplateApi,
} from "@/utils/api_report_config";
import {
  CloudUploadOutlined,
  ContainerOutlined,
  FileAddFilled,
  FileTextOutlined,
  FileWordOutlined,
  LoadingOutlined,
  MinusCircleOutlined,
  FileAddOutlined
} from "@ant-design/icons";
import {
  App,
  Button,
  Divider,
  Form,
  Input,
  List,
  Modal,
  Tooltip,
  Typography,
  Upload,
} from "antd";
import dayjs from "dayjs";
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import "./Config.css";

const { Title, Text } = Typography;
const { Item } = Form;

export interface ReportConfigProps {
  configId?: string; // 项目配置id
  status: ProjectStatus; // 最新报告状态
  isLoading: boolean; // 正在加载或流式
  collapsed?: boolean; // 收起状态
  onCollapseChange?: (collapsed: boolean) => void; // 收起状态变化回调
}
export interface ReportConfigMethods {
  exportConfigData: () => Promise<ProjectConfig | null>;
}

/** 字数要求最小值  */
const WORD_COUNT_MIN = 5000;
/** 字数要求最大值 */
const WORD_COUNT_MAX = 50000;
/** 字数要求步长 */
const WORD_COUNT_STEP = 5000;
/** 参考资料最大数量限制 */
const MAX_REFERENCE_FILES = 10;
/** 配置面板收起 localStorage key */
const CONFIG_PANEL_COLLAPSED = "CONFIG_PANEL_COLLAPSED";
const config_panel_collapsed = JSON.parse(localStorage.getItem(CONFIG_PANEL_COLLAPSED) || "false");

const ReportConfigPanel = forwardRef<ReportConfigMethods, ReportConfigProps>(
  ({ configId, status, isLoading, collapsed, onCollapseChange }, ref) => {
    const { message } = App.useApp();
    // const { isTrial } = useAuth(); // 获取用户角色，判断是否为试用账户
    const navigate = useNavigate();
    /** 课题名称 */
    const [title, setTitle] = useState("");
    /** 额外信息补充 */
    const [userAddPrompt, setUserAddPrompt] = useState<string>("");
    /** 自定义模板ID */
    const [userAddDemoId, setUserAddDemoId] = useState<string>("");
    /** 自定义模板文件 */
    const [userDemoFile, setUserDemoFile] = useState<{
      id?: string;
      file_path: string;
      file_name: string;
      created_at: string;
    } | null>(null);
    /** 自定义模板上传加载状态 */
    const [demoFileUploading, setDemoFileUploading] = useState(false);

    /** 配置面板收起 */
    const [configPanelCollapsed, setConfigPanelCollapsed] = useState(
      collapsed ?? config_panel_collapsed,
    );

    /** 判断是否已经开始或完成了大纲生成 */
    const isOutlineStatus = () => {
      return [
        ProjectStatus.OUTLINE_GENERATING, // 生成大纲中
        ProjectStatus.OUTLINE_GENERATED, // 大纲生成完成
        // ProjectStatus.OUTLINE_FAILED,       // 大纲生成失败
        // ProjectStatus.OUTLINE_CANCELED,     // 大纲生成取消
        ProjectStatus.REPORT_GENERATING, // 生成正文中
        ProjectStatus.REPORT_GENERATED, // 报告生成完成
        ProjectStatus.REPORT_FAILED, // 报告生成失败
        ProjectStatus.REPORT_CANCELED, // 报告生成取消
      ].includes(status);
    };

    /** 判断是否已经开始或完成了报告生成 */
    const isReportStatus = () => {
      return [
        ProjectStatus.REPORT_GENERATING, // 生成正文中
        ProjectStatus.REPORT_GENERATED, // 报告生成完成
        ProjectStatus.REPORT_CANCELED, // 报告生成取消
        ProjectStatus.REPORT_FAILED, // 报告生成失败
      ].includes(status);
    };

    /** 判断基础配置是否被禁用 */
    const isConfigDisabled = () => {
      // console.log("isConfigDisabled===========", isLoading, isOutlineStatus())
      return isLoading || isOutlineStatus();
    };

    /** 判断所有配置是否被禁用 */
    const isAllConfigDisabled = () => {
      // console.log("isAllConfigDisabled===========", isLoading, isReportStatus())
      return isLoading || isReportStatus();
    };

    /** 修改限制提示 */
    const showConfigDisabled = (isReport = false) => {
      if (isLoading) {
        message.warning("正在生成中，不可修改配置");
        return;
      }
      if (isReport) {
        message.warning("大纲或论文已生成，不可修改所有配置");
      } else {
        message.warning("大纲已生成，不可修改基本配置");
      }
    };

    /** 存储配置ID的ref，用来判断是否已生成大纲 */
    const configIdRef = useRef<string | undefined>(undefined);
    
    // 当configId变化时更新ref
    useEffect(() => {
      configIdRef.current = configId;
    }, [configId]);
  
    /** 校验表单数据 */
    const validateStatus = () => {
      const validError = (msg: string) => {
        message.error(msg);
        return false;
      };
      if (!title?.trim()) return validError("课题名称不能为空");
      return true;
    };

    /** 获取项目配置ID */
    const getConfigData = async () => {
      try {
        const data: ProjectConfigCreate = {
          name: title,
          user_add_prompt: userAddPrompt, // 额外信息补充
          user_add_demo_id: (userAddDemoId || null), // 自定义模版ID
        } as ProjectConfigCreate;

        // console.log("createProjectConfig===========", data)
        let promise = configId
          ? projectConfigApi.updateProjectConfig(configId, data)
          : projectConfigApi.createProjectConfig(data);
        const res = await promise;
        // const res = await projectConfigApi.createProjectConfig(data);
        console.log("getConfigData===========", res);
        return res;
      } catch (error) {
        console.log("创建项目配置失败:", error);
        return null;
      }
    };

    /** 更新项目配置 */
    const apiUpdateProjectConfig = async (data: any) => {
      // 只有大纲生成完成且有项目ID时才更新配置
      console.log("configId", configId);
      if (!configId || !isOutlineStatus() || isReportStatus()) {
        return;
      }
      const ConfigData = {
        name: title, // 课题名称
        ...data,
      };

      try {
        await projectConfigApi.updateProjectConfig(configId, ConfigData);
        message.success("配置更新成功");
      } catch (error) {
        console.error("更新配置失败:", error);
      }
    };

    /** 生成项目大纲 */
    const generateOutline = async (configId: string) => {
      if (!configId) {
        console.error("configId 为空！！！");
        return false;
      }
      try {
        const res = await projectReportApi.generateOutline(configId);
        console.warn("generateOutline===========", res);
        return true;
      } catch (error) {
        console.log("生成项目大纲失败:", error);
        return false;
      }
    };

    /** 暴露方法给父组件 */
    useImperativeHandle(ref, () => ({
      exportConfigData: async () => {
        if (!validateStatus()) {
          return null;
        }
        const config = await getConfigData();
        const success = await generateOutline(config?.id || "");
        // 生成大纲请求成功，返回configId
        if (success) {
          return config;
        } else {
          return null;
        }
      },
    }));

    useEffect(() => {
      // 更新数据
      if (configId) {
        getProjectConfig();
      }
    }, [configId]);

    /** 获取项目配置 */
    const getProjectConfig = async () => {
      if (!configId) return;
      try {
        const res = await projectConfigApi.getProjectConfigById(configId);
        console.log("项目管理-项目配置:", res);
        updateProjectConfig(res);
      } catch (error) {
        console.error("获取项目配置失败:", error);
      }
    };
    /** 更新表单数据 */
    const updateProjectConfig = (config: ProjectConfig) => {
      setTitle(config?.name || "");
      // 设置额外信息补充
      setUserAddPrompt(config?.user_add_prompt || "");
      // 设置自定义模板
      setUserAddDemoId(config?.user_add_demo_id || "");
      // 加载自定义模板文件
      if (config?.user_add_demo_id) {
        loadUserDemoFile(config.user_add_demo_id);
      }
    };


    /** 处理课题名称变更 */
    const onTitleInput = (value: string) => {
      if (isConfigDisabled()) {
        showConfigDisabled(isReportStatus());
        return;
      }
      setTitle(value);
    };

    /** 配置面板收起 */
    const onConfigPanelCollapse = () => {
      const newCollapsed = !configPanelCollapsed;
      setConfigPanelCollapsed(newCollapsed);
      localStorage.setItem(CONFIG_PANEL_COLLAPSED, `${newCollapsed}`);
      // 通知父组件状态变化
      onCollapseChange?.(newCollapsed);
    };

    // 同步home传入的collapsed状态
    useEffect(() => {
      if (collapsed !== undefined && collapsed !== configPanelCollapsed) {
        setConfigPanelCollapsed(collapsed);
      }
    }, [collapsed]);

    const toIdeaGenHome = () => {
      // 是否有configId判断提示内容
      const hasConfigId = configId && configId.trim() !== "";

      // 判断是否正在生成过程中
      const isGenerating = () => {
        return (
          [
            ProjectStatus.OUTLINE_GENERATING, // 大纲生成中
            ProjectStatus.REPORT_GENERATING, // 正文生成中
          ].includes(status) || isLoading
        ); // isLoading
      };

      // 根据状态确定提示内容
      let content = "";
      if (isGenerating()) {
        content =
          "当前论文信息已保存，后台继续生成中，您可以在我的历史记录中进入查看。是否继续创建新论文？";
      } else if (hasConfigId) {
        content = "当前论文信息已保存，您可以在我的历史记录中进入查看。是否继续创建新论文？";
      } else {
        content = "当前页面未开始生成，所有填写信息将不会保存。是否继续创建新论文？";
      }

      // 弹窗提示用户
      Modal.confirm({
        title: "创建新论文",
        icon: <FileAddFilled style={{ color: theme.primaryColor }} />,
        content: content,
        okText: "继续新建",
        cancelText: "取消",
        okButtonProps: {
          style: { backgroundColor: theme.primaryColor, borderColor: theme.primaryColor },
        },
        onOk: () => {
          navigate(`/college/paper`);
          window.location.reload();
        },
        onCancel: () => {},
      });
    };


    /** 处理自定义模板上传 */
    const handleDemoFileUpload = (file: File) => {
      if (isConfigDisabled()) {
        showConfigDisabled(true);
        return false;
      }
      if (!title?.trim()) {
        message.error("请先输入课题名称");
        return false;
      }
      
      // 如果已有模板文件，需要先删除
      if (userDemoFile?.id) {
        message.warning("只能上传一个自定义模板文件，请先删除当前文件");
        return false;
      }

      try {
        setDemoFileUploading(true);
        const formData = new FormData();
        formData.append("file", file);
        console.log("准备上传自定义模板文件:", file.name);

        // 调用自定义模板上传接口
        customTemplateApi
          .uploadTemplate(formData)
          .then(response => {
            console.log("自定义模板上传成功响应:", response);
            const fileData = response;
            const newFile = {
              id: fileData.id,
              file_name: fileData.file_name,
              file_path: fileData.file_path || URL.createObjectURL(file),
              created_at: fileData.created_at
            };
            
            // 设置模板文件
            setUserDemoFile(newFile);
            // 设置模板ID
            setUserAddDemoId(fileData.id);
            
            // 更新配置
            if (isOutlineStatus() && !isReportStatus()) {
              apiUpdateProjectConfig({ user_add_demo_id: fileData.id });
            }
            
            message.success(`${file.name} 上传成功`);
            setDemoFileUploading(false);
          })
          .catch(error => {
            console.error("自定义模板上传失败:", error);
            setDemoFileUploading(false);
          });
      } catch (error) {
        console.error("自定义模板上传失败:", error);
        setDemoFileUploading(false);
      }
      return false;
    };
    
    /** 删除自定义模板 */
    const onRemoveDemoFile = () => {
      if (isConfigDisabled()) {
        showConfigDisabled(true);
        return;
      }
      
      if (!userDemoFile?.id) {
        message.error("没有可删除的模板文件");
        return;
      }

      try {
        // 直接清空模板，不调接口
        setUserDemoFile(null);
        setUserAddDemoId("");
        
        // 更新配置
        if (isOutlineStatus() && !isReportStatus()) {
          apiUpdateProjectConfig({ user_add_demo_id: null });
        }
        
        message.success("自定义模板删除成功");
      } catch (error) {
        console.error("删除自定义模板失败:", error);
      }
    };
    
    /** 加载自定义模板文件 */
    const loadUserDemoFile = async (fileId: string) => {
      if (!fileId) {
        return;
      }
      
      try {
        const templateFile = await customTemplateApi.getTemplateFile(fileId);
        console.log("加载模板文件成功:", templateFile);
        
        if (templateFile && templateFile.id) {
          setUserDemoFile({
            id: templateFile.id,
            file_name: templateFile.file_name || "未命名模板",
            file_path: templateFile.file_path || "#",
            created_at: templateFile.created_at || dayjs().format('YYYY-MM-DD HH:mm:ss')
          });
          setUserAddDemoId(templateFile.id);
        } else {
          console.log("未获取到有效模板文件数据");
          setUserDemoFile(null);
          setUserAddDemoId("");
        }
      } catch (error) {
        console.error("加载自定义模板失败:", error);
        setUserDemoFile(null);
      }
    };

    return (
      <div
        className={`report-config-panel ${configPanelCollapsed ? "collapsed" : ""}`}
        // style={{ width: configPanelCollapsed ? 60 : "100%" }}
      >
        <div className="panel-title">
          {!configPanelCollapsed && (
            <Title level={4}>
              论文信息
              {/* <Tooltip title="创建新论文">
                <Button
                  className="toHome"
                  type="text"
                  icon={<FileAddOutlined />}
                  onClick={() => toIdeaGenHome()}
                />
              </Tooltip> */}
            </Title>
          )}
          <Tooltip title="创建新论文">
            <Button
              className="toHome"
              type="text"
              icon={<FileAddOutlined />}
              onClick={() => toIdeaGenHome()}
            />
          </Tooltip>
          {/* <img
            className="panel-icon"
            style={{ rotate: configPanelCollapsed ? "180deg" : "0deg" }}
            src={side_img}
            onClick={onConfigPanelCollapse}
          /> */}
        </div>
        {configPanelCollapsed ? (
          <div className="panel-icon-container">
            <Tooltip title="研究项目基本信息" placement="right">
              <ContainerOutlined
                style={{
                  fontSize: 20,
                  color: theme.primaryColor,
                }}
              />
            </Tooltip>
          </div>
        ) : (
          <div className="panel-container">
            {/* 项目基本信息子模块 */}
            <div>
              <div style={{ display: "flex", alignItems: "center", marginBottom: 8 }}>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <ContainerOutlined
                    style={{
                      fontSize: 20,
                      color: theme.primaryColor,
                      marginRight: 8,
                    }}
                  />
                  <Title level={5} style={{ margin: 0 }}>
                    课题名称
                  </Title>
                </div>
              </div>
              <Form layout="vertical">
                <Item style={{ marginBottom: 0 }}>
                  <Tooltip
                    placement="right"
                    color="#fff"
                    className="Tooltip-style"
                    title={title ? <div className="Tooltip-content-style">{title}</div> : null}
                  >
                    <Input
                      placeholder="请输入课题名称（最多300字）"
                      maxLength={300}
                      style={{ borderRadius: theme.borderRadius }}
                      value={title}
                      onChange={e => onTitleInput(e.target.value)}
                      disabled={isConfigDisabled()}
                      // suffix={
                      //   <Tooltip title="AI优化课题名称">
                      //     <Button
                      //       style={{ color: theme.primaryColor }}
                      //       type="text"
                      //       size="small"
                      //       onClick={optimizeTitle}
                      //       loading={titleOptimizing}
                      //     >
                      //       AI
                      //     </Button>
                      //   </Tooltip>
                      // }
                    />
                  </Tooltip>
                </Item>
              </Form>
            </div>
            <Divider />

            {/* 额外信息补充 */}
            <div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: 8,
                }}
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <FileTextOutlined
                    style={{
                      fontSize: 20,
                      color: theme.primaryColor,
                      marginRight: 8,
                    }}
                  />
                  <Title level={5} style={{ margin: 0 }}>
                    额外信息补充
                  </Title>
                  <Tooltip title="建议详细描述您的需求、风格偏好或重点方向，以获得更理想的效果。">
                    <div
                      style={{
                        width: "14px",
                        height: "14px",
                        fontSize: "11px",
                        fontWeight: 500,
                        lineHeight: "14px",
                        textAlign: "center",
                        borderRadius: "50%",
                        backgroundColor: "#f5b46f",
                        color: "#fff",
                        marginLeft: "8px",
                      }}
                    >
                      ?
                    </div>
                  </Tooltip>
                </div>
              </div>
              <div>
                <Input.TextArea
                  placeholder="请输入提示词，作为大纲和正文的重要指引（最多500字）"
                  // rows={2}
                  value={userAddPrompt || ''}
                  onChange={e => {
                    setUserAddPrompt(e.target.value);
                  }}
                  disabled={isConfigDisabled()}
                  maxLength={500}
                />
              </div>
            </div>
            <Divider />

            {/* 自定义模版 */}
            <div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: 8,
                }}
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <FileWordOutlined
                    style={{
                      fontSize: 20,
                      color: theme.primaryColor,
                      marginRight: 8,
                    }}
                  />
                  <Title level={5} style={{ margin: 0 }}>
                    自定义模版
                  </Title>
                  <Tooltip title="请注意，您选择的模版将影响文档格式，建议选择有目录结构的模版。">
                    <div
                      style={{
                        width: "14px",
                        height: "14px",
                        fontSize: "11px",
                        fontWeight: 500,
                        lineHeight: "14px",
                        textAlign: "center",
                        borderRadius: "50%",
                        backgroundColor: "#f5b46f",
                        color: "#fff",
                        marginLeft: "8px",
                      }}
                    >
                      ?
                    </div>
                  </Tooltip>
                </div>
              </div>
              
              {!userDemoFile && (
                <Upload.Dragger
                  className="config-upload-dragger"
                  accept=".doc,.docx,.txt,.md,.markdown"
                  beforeUpload={(file) => {
                    return handleDemoFileUpload(file);
                  }}
                  showUploadList={false}
                  disabled={demoFileUploading || isConfigDisabled() || userDemoFile !== null}
                >
                  <div className="drag-text">
                    <CloudUploadOutlined className="drag-icon" />
                    拖到此处，或点击上传
                  </div>
                </Upload.Dragger>
              )}

              {demoFileUploading && (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 16,
                    marginBottom: 16,
                  }}
                >
                  <LoadingOutlined style={{ marginRight: 8 }} />
                  <Text type="secondary">正在上传模版文件，请稍候...</Text>
                </div>
              )}

              {/* 已上传的模版文件 */}
              {userDemoFile && (
                <List
                  style={{
                    marginBottom: 8,
                    padding: "0 12px",
                    backgroundColor: "#fff",
                    boxShadow: "rgba(0, 0, 0, 0.05) 0px 1px 2px",
                    borderRadius: 8,
                    width: "100%",
                  }}
                  dataSource={[userDemoFile]}
                  renderItem={file => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          file.file_name.includes("doc") || file.file_name.includes("docx") ? (
                            <FileWordOutlined style={{ fontSize: 20, color: "#1890ff" }} />
                          ) : file.file_name.includes("txt") ? (
                            <FileTextOutlined style={{ fontSize: 20, color: "#ff4d4f" }} />
                          ) : (
                            <FileTextOutlined style={{ fontSize: 20, color: "#1890ff" }} />
                          )
                        }
                        title={
                          <a
                            rel="noopener noreferrer"
                            style={{
                              wordBreak: "break-all",
                              overflowWrap: "break-word",
                            }}
                          >
                            {file.file_name}
                          </a>
                        }
                        description="大纲将按照该模版进行生成"
                      />
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                        }}
                      >
                        <Tooltip title="删除模版">
                          <Button
                            type="text"
                            danger
                            icon={<MinusCircleOutlined />}
                            size="small"
                            onClick={e => {
                              e.stopPropagation();
                              onRemoveDemoFile();
                            }}
                            disabled={isConfigDisabled()}
                          />
                        </Tooltip>
                      </div>
                    </List.Item>
                  )}
                />
              )}
            </div>

          </div>
        )}
      </div>
    );
  },
);

export default ReportConfigPanel;
