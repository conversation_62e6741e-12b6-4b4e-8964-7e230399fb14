import ICON_COMPLETE from "@/assets/complete.png";
import ICON_FAIL_CANCEL from "@/assets/fail_cancel.png";
import ICON_GENERATING from "@/assets/generating.png";
import ICON_INITIAL from "@/assets/initial.png";
import { theme } from "@/config/theme";
import { useAuth } from "@/contexts/AuthContext";
import "./Workflow.css";
import { ProjectConfig, ProjectStatus } from "@/types/ReportConfig";
import { projectConfigApi } from "@/utils/api_report_config";
import {
  ClockCircleOutlined,
} from "@ant-design/icons";
import { App, Typography } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";

const { Title, Text } = Typography;

export interface WorkflowMethods {
  refreshWorkflow: () => void;
}

export interface WorkflowProps {
  configId: string;
}

/** 工作流程步骤状态 */
enum StepStatus {
  WAIT = "wait", // 未开始
  PROCESS = "process", // 进行中
  FINISH = "finish", // 已完成
  ERROR = "error", // 错误
}

/** 工作流程步骤 */
interface WorkflowStep {
  title: string;
  subTitle: string;
  status: StepStatus;
  icon?: React.ReactNode;
  disabled?: boolean;
  version?: string;
}

/** 工作流阶段 */
export enum WorkflowStage {
  INITIAL = 0, // 初始配置
  OUTLINE = 1, // 大纲生成
  REPORT = 2, // 报告生成
}

/**
 * 工作流程面板
 * 展示项目进度的三个步骤：基础配置、生成大纲、生成论文
 */
const WorkflowPanel = forwardRef<WorkflowMethods, WorkflowProps>(
  (
    { configId },
    ref,
  ) => {
    const { isTrial } = useAuth();
    const { message } = App.useApp();
    const [loading, setLoading] = useState(false);
    const [projectConfig, setProjectConfig] = useState<ProjectConfig | null>(null);
    const [currentStep, setCurrentStep] = useState<number>(100);
    const [currentStage, setCurrentStage] = useState<WorkflowStage>(WorkflowStage.INITIAL);
    const [steps, setSteps] = useState<WorkflowStep[]>([
      {
        title: "基础配置",
        subTitle: "完善课题信息和研究要求",
        status: StepStatus.PROCESS, // 初始就高亮
        icon: <ClockCircleOutlined />,
      },
      {
        title: "生成大纲",
        subTitle: "创建全面的研究大纲",
        status: StepStatus.WAIT,
        icon: <ClockCircleOutlined />,
      },
      {
        title: "生成论文",
        subTitle: "根据大纲生成完整论文",
        status: StepStatus.WAIT,
        icon: <ClockCircleOutlined />,
      },
    ]);


    /** 组件初始化 */
    useEffect(() => {
      if (configId) {
        getProjectConfig();
      } else {
        // 没有configId时，保持初始状态（基础配置高亮）
        setCurrentStage(WorkflowStage.INITIAL);
        setCurrentStep(0);
      }
    }, [configId]);

    /** 暴露方法给父组件 */
    useImperativeHandle(ref, () => ({
      refreshWorkflow: () => {
        console.warn("更新工作流！！！", configId);
        getProjectConfig();
      },
    }));

    /** 获取项目配置 */
    const getProjectConfig = async () => {
      if (!configId) return;
      setLoading(true);
      try {
        const res = await projectConfigApi.getProjectConfigById(configId);
        console.log("工作流程-项目配置:", res);
        setProjectConfig(res);
        updateWorkflowSteps(res);
      } catch (error) {
        console.error("获取项目配置失败:", error);
      } finally {
        setLoading(false);
      }
    };

    /** 更新工作流步骤状态 */
    const updateWorkflowSteps = (config: ProjectConfig) => {
      // 创建步骤初始状态
      const workflowSteps: WorkflowStep[] = [
        {
          title: "基础配置",
          subTitle: "完善课题信息和研究要求",
          status: StepStatus.PROCESS, // 初始就高亮
        },
        {
          title: "生成大纲",
          subTitle: "创建全面的研究大纲",
          status: StepStatus.WAIT,
        },
        {
          title: "生成论文",
          subTitle: "根据大纲生成完整论文",
          status: StepStatus.WAIT,
        },
      ];

      // 根据项目状态更新步骤状态
      let currentStepIndex = 0;
      let stage = WorkflowStage.INITIAL;

      switch (config.status) {
        // 大纲相关状态
        case ProjectStatus.CONFIGURING:
          // 初始状态，基础配置高亮
          workflowSteps[0].status = StepStatus.PROCESS;
          stage = WorkflowStage.INITIAL;
          break;
        case ProjectStatus.OUTLINE_GENERATING: // 生成大纲中
          workflowSteps[0].status = StepStatus.FINISH;
          workflowSteps[1].status = StepStatus.PROCESS;
          currentStepIndex = 1;
          stage = WorkflowStage.OUTLINE;
          break;
        case ProjectStatus.OUTLINE_GENERATED: // 生成大纲完成
          workflowSteps[0].status = StepStatus.FINISH;
          workflowSteps[1].status = StepStatus.FINISH;
          currentStepIndex = 1;
          stage = WorkflowStage.OUTLINE;
          break;
        case ProjectStatus.OUTLINE_FAILED: // 生成大纲失败
          workflowSteps[0].status = StepStatus.FINISH;
          workflowSteps[1].status = StepStatus.ERROR;
          currentStepIndex = 1;
          stage = WorkflowStage.OUTLINE;
          break;
        case ProjectStatus.OUTLINE_CANCELED: // 生成大纲取消
          workflowSteps[0].status = StepStatus.FINISH;
          workflowSteps[1].status = StepStatus.ERROR;
          currentStepIndex = 1;
          stage = WorkflowStage.OUTLINE;
          break;

        // 报告相关状态
        case ProjectStatus.REPORT_GENERATING: // 生成报告中
          workflowSteps[0].status = StepStatus.FINISH;
          workflowSteps[1].status = StepStatus.FINISH;
          workflowSteps[2].status = StepStatus.PROCESS;
          currentStepIndex = 2;
          stage = WorkflowStage.REPORT;
          break;
        case ProjectStatus.REPORT_GENERATED: // 生成报告完成
          workflowSteps[0].status = StepStatus.FINISH;
          workflowSteps[1].status = StepStatus.FINISH;
          workflowSteps[2].status = StepStatus.FINISH;
          currentStepIndex = 2;
          stage = WorkflowStage.REPORT;
          break;
        case ProjectStatus.REPORT_FAILED: // 生成报告失败
          workflowSteps[0].status = StepStatus.FINISH;
          workflowSteps[1].status = StepStatus.FINISH;
          workflowSteps[2].status = StepStatus.ERROR;
          currentStepIndex = 2;
          stage = WorkflowStage.REPORT;
          break;
        case ProjectStatus.REPORT_CANCELED: // 生成报告取消
          workflowSteps[0].status = StepStatus.FINISH;
          workflowSteps[1].status = StepStatus.FINISH;
          workflowSteps[2].status = StepStatus.ERROR;
          currentStepIndex = 2;
          stage = WorkflowStage.REPORT;
          break;

        default:
          break;
      }

      setSteps(workflowSteps);
      setCurrentStep(currentStepIndex);
      setCurrentStage(stage);
    };



    // 渲染步骤项
    const renderStepItem = (step: WorkflowStep, index: number) => {
      const itemConfig = {
        [StepStatus.WAIT]: {
          text: "待开始",
          icon: ICON_INITIAL,
          color: "#999",
          borderColor: "#e8e8e8",
          background: "#fafafa",
        },
        [StepStatus.PROCESS]: {
          text: "进行中",
          icon: ICON_GENERATING,
          color: theme.primaryColor,
          borderColor: theme.primaryColor,
          background: "#f6f8ff",
        },
        [StepStatus.FINISH]: {
          text: "已完成",
          icon: ICON_COMPLETE,
          color: "#52c41a",
          borderColor: "#52c41a",
          background: "#f6ffed",
        },
        [StepStatus.ERROR]: {
          text: "已终止",
          icon: ICON_FAIL_CANCEL,
          color: "#ff4d4f",
          borderColor: "#ff4d4f",
          background: "#fff2f0",
        },
      };

      const item = itemConfig[step.status];
      const isLast = index === steps.length - 1;

      return (
        <div key={index} className="workflow-step-item">
          <div className="step-content">
            <div className="step-icon" style={{ borderColor: item.color }}>
              <img src={item.icon} alt={item.text} />
            </div>
            <div className="step-title" style={{ color: item.color }}>
              {step.title}
            </div>
          </div>
          {!isLast && (
            <div className="step-connector">
              <div className="connector-line"></div>
            </div>
          )}
        </div>
      );
    };

    return (
      <div className="workflow-panel">
        
        <div className="workflow-steps-container">
          {steps.map((step, index) => renderStepItem(step, index))}
        </div>
      </div>
    );
  },
);

export default WorkflowPanel;
