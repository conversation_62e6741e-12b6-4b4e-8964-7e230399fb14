.paper-page-container {
  width: 100%;
  height: 100vh;
  flex: 1;
  display: flex;
  flex-direction: row;
  padding: 16px;
  /* gap: 16px; */
  background-color: #EEF3FF;
  overflow: hidden;
}

.paper-container {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-right 0.3s ease-in-out;
}

/* 历史记录面板 */
.paper-history-panel {
  width: 25%;
  height: 100%;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  overflow: hidden;
  z-index: 10;
}

.paper-history-panel.show {
  opacity: 1;
  transform: translateX(0);
  margin-right: 16px;

  transition:
    width 0.3s ease,
    margin-right 0.2s ease,
    transform 0.3s ease,
    opacity 0.2s ease;
}

.paper-history-panel.hide {
  width: 0px;
  opacity: 0;
  transform: translateX(-100%);
  margin-right: 0px;

  transition:
    width 0.3s ease,
    margin-right 0.2s ease,
    transform 0.3s ease,
    opacity 0.2s ease;
}

.header-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .titleText {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 0 0 25%;
    max-width: 25%;
    font-size: 24px;
    font-weight: bold;
    color: #333;

    .historyButton {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid #e0e0e0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.action-bar {
  height: 50px;
  min-height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 16px;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 0;

  .title {
    margin: 0px;
  }

  .guide-btn {
    position: absolute;
    left: -1px;
    top: 8px;
    height: 32px !important;
    width: 30px !important;
    padding: 0px 8px 0 6px !important;
    border-radius: 0 50% 50% 0 !important;
    border: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
  }

  .ant-btn {
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    width: fit-content;
    min-width: fit-content;
    max-width: fit-content;
    border-radius: 8px;
    font-size: 14px;
    padding: 0px 12px;
    box-shadow: none;
  }

  .generate-btn {
    color: #fff;
    background: linear-gradient(34deg, #2a5caa 30%, #6e3bab 90%) !important;
  }

  .stop-btn {
    height: 32px;
    min-height: 32px;
  }
}

.empty-area {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 15%;
  background: #fff;
  border-radius: 0 0 12px 12px;

  .logo {
    height: 20px;
    max-height: 40px;
    margin-bottom: 16px;
    /* margin-top: 10%; */
  }

  .title {
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    color: #757575;
    margin-top: 8px;
    margin-bottom: 24px;
  }

  .start-btn-wrapper {
    position: relative;
    display: inline-block;
    width: 130px;
    /* 固定尺寸，避免响应式错位 */
    height: 130px;
  }

  .start-btn {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease-in-out;
    cursor: pointer;
    position: relative;
    z-index: 2;
  }

  /* 鼠标悬停时轻微放大 */
  .start-btn-wrapper:hover .start-btn {
    transform: scale(1.15);
  }

  /* 外圈发光旋转圈 */
  .start-btn-wrapper::before {
    content: "";
    position: absolute;
    top: -4px;
    left: -4px;
    width: calc(100% + 8px);
    height: calc(100% + 8px);
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      rgba(0, 255, 255, 0.5),
      rgba(255, 0, 255, 0.5),
      rgba(0, 255, 255, 0.5)
    );
    animation: rotateGlowCircle 3s linear infinite;
    /* 更快旋转 */
    z-index: 1;
    filter: blur(20px);
    /* 更紧凑的光晕 */
    opacity: 0.7;
  }
}

.nostart {
  filter: grayscale(80%) brightness(90%);
  opacity: 50%;
}

@keyframes rotateGlowCircle {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 卡片样式 */
.workflow-card {
  flex: 0 0 75%;
  max-width: 75%;
  background: #fff;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.main-content-area {
  flex: 1;
  height: calc(100vh - 320px);
  min-height: 500px;
}

.config-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  height: 100%;
  border: 1px solid #f0f0f0;
}

.content-card {
  height: 100%;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.content-card .action-bar {
  border-bottom: 1px solid #e8e8e8;
  border-radius: 0;
}
