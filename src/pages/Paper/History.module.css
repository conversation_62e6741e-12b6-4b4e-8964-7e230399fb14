.historyPanel {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.historyHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 8px;
}

.headerTitle {
  font-size: 18px;
  font-weight: 500;
}

.closeButton {
  color: #888;
}

.toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.searchInput {
  flex: 1;
}

.historyList {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
}

.historyCard {
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.historyCard:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.cardTitle {
  font-weight: 500;
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220px;
}

.deleteButton {
  color: #ff4d4f;
}

.cardSource {
  font-size: 13px;
  color: #888;
  margin-bottom: 10px;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardTimestamp {
  font-size: 12px;
  color: #aaa;
} 