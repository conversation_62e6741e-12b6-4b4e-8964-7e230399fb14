.report-config-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: none;
  transition: width 0.3s ease;
  /* transition: all 0.2s, background 0s; */

  .panel-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    min-height: 50px;
    padding: 10px 16px;
    border-bottom: 1px solid #f0f0f0;

    .ant-typography {
      display: flex;
      align-items: center;
      margin: 0px;
    }
    .toHome{
      font-size: 20px;
      color: #6c6c6c;
      margin-left: 6px;
    }
  }

  .panel-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: transform 0.3s ease;
  }

  /* 收起样式 */
  &.collapsed {
    .panel-title {
      padding: 10px 8px;
      justify-content: center;
      border-bottom: 1px solid #f0f0f0;
    }

    .panel-icon-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 14px 0px;
      flex: 1;
    }
  }

  .panel-container {
    overflow: auto;
    padding: 12px;
    
    /* 字数设置 */
    .words-container {
      /* margin-bottom: 16px; */

      .words-title {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }

      .tip {
        color: #757575;
      }

      .slider-container {
        padding: 0px 10px;
        display: flex;

        .word-count-slider {
          flex: 1;
          margin-bottom: 16px;

          .ant-slider-rail {
            background: linear-gradient(90deg, #ff9800 0%, #4caf50 50%, #f44336 100%);
            opacity: 0.5;
          }
          .ant-slider-track {
            background: #9c27b0;
          }
        }
      }
    }
  }

  .config-upload-dragger {
    .ant-upload .ant-upload-btn {
      padding: 8px;
      .drag-text {
        font-size: 14px;
        color: #999;

        .drag-icon {
          font-size: 20px;
          margin-right: 8px;
          color: #999;
        }
      }
      .drag-hint {
        font-size: 12px;
        color: #999;
        margin-bottom: 4px;
      }
    }
  }
}

/* Tooltip */
.ant-tooltip-inner {
  .Tooltip-style {
    max-width: 400px;
    background-color: #fff;
    color: #333;
    box-shadow:
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 9px 28px 8px rgba(0, 0, 0, 0.05);
  }
  .Tooltip-content-style {
    text-indent: 2em;
    text-align: justify;
    max-height: 500px;
    overflow-y: auto;
    padding: 8px;
    color: #333;
    white-space: pre-wrap;
    line-height: 1.5;
    font-size: 14px;
  }
}

/* divider分隔 */
.ant-divider.ant-divider-horizontal {
  margin: 8px 0;
}

/* 参考文献网址列表 */
.config-reference-url-list {
  .ant-list-item-meta-avatar {
    margin-top: 10px;
    margin-inline-end: 10px !important;

    span {
      font-size: 24px;
      color: #999;
    }
  }

  .ant-list-item-action {
    margin-inline-start: 16px !important;
  }
}