/* 模型管理样式 */
.management-container {
  padding: 24px;
  height: 100%;
}

.model-management-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border-radius: 8px;
}

.model-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-management-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-icon {
  font-size: 24px;
  color: #1890ff; /* 蓝色图标 */
}

.model-search-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.model-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 0;
}

/* 抽屉样式 */
.model-drawer .ant-drawer-body {
  padding: 24px;
}

/* 表格样式 */
.table-row-light {
  background-color: #fff;
}

.table-row-dark {
  background-color: #f9f9f9;
}

/* 操作按钮样式 */
.action-button {
  margin-right: 8px;
  min-width: 72px;
  text-align: center;
}

.view-button {
  background-color: #13c2c2 !important;
  border-color: #13c2c2 !important;
}

.edit-button {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.enable-button {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
}

.disable-button {
  background-color: #faad14 !important;
  border-color: #faad14 !important;
}

.delete-button {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #fff !important;
}

/* 抽屉底部按钮样式 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 10px 16px;
  background: #fff;
}

/* 响应式处理 */
@media screen and (max-width: 768px) {
  .model-management-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .model-search-bar {
    flex-direction: column;
    width: 100%;
  }
  
  .model-search-bar .ant-input {
    width: 100% !important;
  }
  
  .action-button {
    margin-bottom: 8px;
  }
}
