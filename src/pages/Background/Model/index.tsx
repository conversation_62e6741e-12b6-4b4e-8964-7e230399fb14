import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ModelConfig } from '@/types/ModelConfig';
import { modelApi, modelUserApi } from '@/utils/api';
import { PaginationState, processPaginatedResponse } from '@/types/Page';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Popconfirm,
  Result,
  Row,
  Space,
  Spin,
  Switch,
  Table,
  Tag,
  Typography
} from 'antd';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
  ApiOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import './index.css';
import ModelDrawer from './ModelDrawer';

const { Title, Text } = Typography;

/**
 * 模型管理页面组件
 */
const ModelManagement: React.FC = () => {
  const { isSuperAdmin } = useAuth(); // 检查是否超级管理员
  const [modelConfigs, setModelConfigs] = useState<ModelConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerType, setDrawerType] = useState<'create' | 'edit' | 'view'>('create');
  const [currentModel, setCurrentModel] = useState<ModelConfig | null>(null);
  const [searchText, setSearchText] = useState('');
  const [inputText, setInputText] = useState('');
  const [detailLoading, setDetailLoading] = useState(false);

  // 分页相关状态 - 为后续扩展预留
  const [pagination, setPagination] = useState<PaginationState>({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取所有模型
  const fetchModelConfigs = async (page = 1, size = 10, keyword = '') => {
    try {
      setLoading(true);
      const result = await modelApi.getAllModelsPaginated({
        page,
        size,
        keyword
      });
      
      // 使用通用处理函数处理分页数据
      const { data: configsArray, total } = processPaginatedResponse<ModelConfig>(result);
      
      setModelConfigs(configsArray);
      setPagination({
        ...pagination,
        current: page,
        pageSize: size,
        total
      });
      
      console.log('获取到模型管理:', configsArray, '总数:', total);
    } catch (error) {
      console.error('获取模型管理失败:', error);
      setModelConfigs([]);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取模型管理
  useEffect(() => {
    fetchModelConfigs(1, pagination.pageSize, '');
  }, []);

  // 打开创建模型抽屉
  const showCreateDrawer = () => {
    setCurrentModel(null);
    setDrawerType('create');
    setDrawerVisible(true);
  };

  // 打开编辑模型抽屉
  const showEditDrawer = (model: ModelConfig) => {
    setCurrentModel(model);
    setDrawerType('edit');
    setDrawerVisible(true);
  };

  // 显示模型详情
  const showModelDetail = async (model: ModelConfig) => {
    try {
      // 先打开抽屉
      setCurrentModel(null);
      setDrawerType('view');
      setDrawerVisible(true);
      setDetailLoading(true);
      
      // 模拟异步获取详情，实际上可以直接使用传入的model或者重新获取
      setTimeout(() => {
        setCurrentModel(model);
        setDetailLoading(false);
      }, 500);
    } catch (error) {
      console.error('获取模型详情失败:', error);
      setDetailLoading(false);
    }
  };

  // 关闭抽屉
  const closeDrawer = () => {
    setDrawerVisible(false);
    setCurrentModel(null);
  };

  // 删除模型
  const handleDelete = async (modelId: string) => {
    try {
      setLoading(true);
      await modelApi.deleteModel(modelId);
      message.success('模型删除成功');
      fetchModelConfigs();
    } catch (error) {
      console.error('删除模型失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 切换模型状态（启用/禁用）
  const handleToggleStatus = async (model: ModelConfig) => {
    try {
      setLoading(true);
      const newStatus = !model.is_active;
      await modelApi.updateModel(model.id, { is_active: newStatus });
      message.success(`模型已${newStatus ? '启用' : '禁用'}`);
      fetchModelConfigs();
    } catch (error) {
      console.error('更新模型状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setSearchText(inputText);
    // 重置到第一页，执行搜索
    fetchModelConfigs(1, pagination.pageSize, inputText);
  };

  // 处理重置搜索
  const handleReset = () => {
    setInputText('');
    setSearchText('');
    // 重置搜索后刷新数据
    fetchModelConfigs(1, pagination.pageSize, '');
  };

  // 处理按键事件，按回车触发搜索
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    // 更新分页状态并重新获取数据
    fetchModelConfigs(page, pageSize, searchText);
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '模型显示名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '模型名称（英文标识）',
      dataIndex: 'model_name',
      key: 'model_name',
    },
    {
      title: '最大上下文',
      dataIndex: 'max_context',
      key: 'max_context',
      width: 110,
    },
    {
      title: '最大输出',
      dataIndex: 'max_output',
      key: 'max_output',
      width: 90,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'status',
      width: 90,
      render: (isActive: boolean, record: ModelConfig) => (
        <Switch 
          checkedChildren="启用" 
          unCheckedChildren="禁用"
          defaultChecked={isActive}
          onClick={(e) => {
            handleToggleStatus(record);
          }}
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString('zh-CN')
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      render: (_: any, record: ModelConfig) => (
        <Space size="small">
          <Button 
            type="primary"
            size="small" 
            icon={<EyeOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              showModelDetail(record);
            }}
            className="action-button view-button"
          >
            查看
          </Button>
          <Button 
            type="primary"
            size="small" 
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              showEditDrawer(record);
            }}
            className="action-button edit-button"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此模型吗？"
            onConfirm={(e) => {
              e?.stopPropagation();
              handleDelete(record.id);
            }}
            okText="确定"
            cancelText="取消"
            onCancel={(e) => e?.stopPropagation()}
          >
            <Button 
              danger
              size="small" 
              icon={<DeleteOutlined />}
              onClick={(e) => e.stopPropagation()}
              className="action-button delete-button"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 非超级管理员不可访问此页面
  if (!isSuperAdmin) {
    return (
      <div style={{ padding: '24px' }}>
        <Card>
          <Result
            status="403"
            title="无权访问"
            subTitle="您没有权限访问模型管理页面"
          />
        </Card>
      </div>
    );
  }

  return (
    <div className="management-container">
      <Card
        title={
          <div className="model-management-header">
            <div className="model-management-title">
              <ApiOutlined className="model-icon" />
              <Title level={4} style={{ margin: 0 }}>
                模型管理
              </Title>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateDrawer}
            >
              新增模型
            </Button>
          </div>
        }
        className="model-management-card"
      >
        {/* 搜索栏 */}
        <div className="model-search-bar">
          <Input
            placeholder="搜索模型名称或标识"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            prefix={<SearchOutlined />}
            style={{ width: 300 }}
            allowClear
          />
          <Button 
            type="primary" 
            icon={<SearchOutlined />} 
            onClick={handleSearch}
          >
            搜索
          </Button>
          <Button 
            onClick={handleReset}
            style={{ marginLeft: 8 }}
          >
            重置
          </Button>
        </div>

        {/* 模型列表 */}
        {loading ? (
          <div className="model-loading">
            <Spin size="large" />
          </div>
        ) : (
          <Table
            rowKey="id"
            columns={columns}
            dataSource={modelConfigs}
            className="model-table"
            rowClassName={(record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark'}
            pagination={{ 
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              onChange: (page, pageSize) => handleTableChange(page, pageSize || 10)
            }}
          />
        )}
      </Card>

      {/* 模型管理抽屉 */}
      <ModelDrawer
        visible={drawerVisible}
        drawerType={drawerType}
        currentModel={currentModel}
        onClose={closeDrawer}
        onSuccess={(page, pageSize, keyword) => fetchModelConfigs(page, pageSize, keyword)}
        detailLoading={detailLoading}
      />
    </div>
  );
};

export default ModelManagement; 