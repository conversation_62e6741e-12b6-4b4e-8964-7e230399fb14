import React, { useEffect, useState } from 'react';
import { ModelConfig } from '@/types/ModelConfig';
import { modelApi } from '@/utils/api';
import {
  Button,
  Descriptions,
  Drawer,
  Empty,
  Form,
  Input,
  InputNumber,
  message,
  Space,
  Spin,
  Switch,
  Typography,
  Tag
} from 'antd';
import { ApiOutlined, ReloadOutlined, CheckCircleOutlined, StopOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;

export type DrawerType = 'create' | 'edit' | 'view';

interface ModelDrawerProps {
  visible: boolean;
  drawerType: DrawerType;
  currentModel: ModelConfig | null;
  onClose: () => void;
  onSuccess: (page: number, pageSize: number, searchText: string) => void;
  detailLoading?: boolean;
}

/**
 * 模型配置抽屉组件
 * 用于创建、编辑和查看模型配置
 */
// 格式化日期显示
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const ModelDrawer: React.FC<ModelDrawerProps> = ({ 
  visible, 
  drawerType, 
  currentModel, 
  onClose, 
  onSuccess,
  detailLoading = false
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const isView = drawerType === 'view';
  const isEdit = drawerType === 'edit';
  const isCreate = drawerType === 'create';

  // 设置表单初始值
  useEffect(() => {
    if (visible) {
      form.resetFields();
      if (currentModel && (isEdit || isView)) {
        form.setFieldsValue(currentModel);
      }
    }
  }, [visible, currentModel, drawerType, form]);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      if (isEdit && currentModel) {
        await modelApi.updateModel(currentModel.id, values);
        message.success('模型配置更新成功');
      } else if (isCreate) {
        await modelApi.addModel(values);
        message.success('模型配置添加成功');
      }
      
      // 关闭抽屉并通知父组件刷新数据
      onClose();
      // 调用父组件的成功回调，使用当前分页参数
      onSuccess(1, 10, ''); // 修改或创建后回到第一页
    } catch (error) {
      console.error('保存模型配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (isCreate) {
      form.resetFields();
    } else if (currentModel && isEdit) {
      form.setFieldsValue(currentModel);
    }
  };

  // 获取抽屉标题
  const getDrawerTitle = () => {
    switch (drawerType) {
      case 'create':
        return '新增模型配置';
      case 'edit':
        return '编辑模型配置';
      case 'view':
        return '模型配置详情';
      default:
        return '模型配置';
    }
  };

  // 渲染底部按钮
  const renderFooter = () => {
    if (isView) {
      return (
        <div className="drawer-footer">
          <Button type="primary" onClick={onClose}>
            关闭
          </Button>
        </div>
      );
    }
    
    return (
      <div className="drawer-footer">
        <Button onClick={onClose}>取消</Button>
        <Button icon={<ReloadOutlined />} onClick={handleReset}>重置</Button>
        <Button type="primary" onClick={() => form.submit()} loading={loading}>
          保存
        </Button>
      </div>
    );
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <ApiOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
          <Title level={4} style={{ margin: 0 }}>
            {getDrawerTitle()}
          </Title>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={600}
      className="model-drawer"
      footer={renderFooter()}
    >
      {isView ? (
        detailLoading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
            <Spin size="large" tip="加载模型详情..." />
          </div>
        ) : currentModel ? (
          <Descriptions bordered column={1} size="middle" labelStyle={{ width: "140px" }}>
            <Descriptions.Item label="模型名称">{currentModel.name}</Descriptions.Item>
            <Descriptions.Item label="模型标识">{currentModel.model_name}</Descriptions.Item>
            <Descriptions.Item label="状态">
              {currentModel.is_active ? 
                <Tag icon={<CheckCircleOutlined />} color="success">启用</Tag> : 
                <Tag icon={<StopOutlined />} color="error">禁用</Tag>
              }
            </Descriptions.Item>
            <Descriptions.Item label="API链接">{currentModel.api_url}</Descriptions.Item>
            <Descriptions.Item label="API密钥">{currentModel.api_key}</Descriptions.Item>
            <Descriptions.Item label="最大上下文长度">{currentModel.max_context}</Descriptions.Item>
            <Descriptions.Item label="最大输出上限">{currentModel.max_output}</Descriptions.Item>
            <Descriptions.Item label="描述">{currentModel.description || '-'}</Descriptions.Item>
            <Descriptions.Item label="创建时间">{formatDate(currentModel.created_at)}</Descriptions.Item>
            <Descriptions.Item label="更新时间">{formatDate(currentModel.updated_at)}</Descriptions.Item>
          </Descriptions>
        ) : (
          <Empty description="未找到模型详情" />
        )
      ) : loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>
          <Spin size="large" />
        </div>
      ) : (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={isView}
        >
          <Form.Item
            name="model_name"
            label="模型名称（英文标识）"
            rules={[
              { required: true, message: '请输入模型名称标识' },
              { max: 50, message: '模型名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入模型英文标识，如gpt-4-turbo" autoComplete="new-username" />
          </Form.Item>
          
          <Form.Item
            name="name"
            label="模型显示名称"
            rules={[
              { required: true, message: '请输入模型显示名称' },
              { max: 50, message: '模型显示名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入显示名称，如GPT-4 Turbo" autoComplete="new-username" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch 
              checkedChildren="启用" 
              unCheckedChildren="禁用"
              defaultChecked 
            />
          </Form.Item>
          
          <Form.Item
            name="api_key"
            label="API 密钥"
            rules={[
              { required: true, message: '请输入API密钥' },
              { max: 200, message: 'API密钥不能超过200个字符' }
            ]}
          >
            <Input.Password placeholder="请输入调用模型所需的API密钥" autoComplete="new-password" />
          </Form.Item>
          
          <Form.Item
            name="api_url"
            label="API 链接"
            rules={[
              { required: true, message: '请输入API链接' },
              {
                pattern: /^https?:\/\/[a-zA-Z0-9\-\.]+/,
                message: 'API链接必须是有效的URL格式'
              }
            ]}
          >
            <Input placeholder="请输入API链接，如https://api.openai.com/v1/chat/completions" />
          </Form.Item>
          
          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item
              name="max_context"
              label="最大上下文长度"
              rules={[{ required: true, message: '请输入最大上下文长度' }]}
              style={{ flex: "1" }}
            >
              <InputNumber 
                placeholder="请输入，如10000" 
                style={{ width: '100%' }}
                min={0}
              />
            </Form.Item>
            
            <Form.Item
              name="max_output"
              label="最大输出上限"
              rules={[{ required: true, message: '请输入最大输出上限' }]}
              style={{ flex: "1" }}
            >
              <InputNumber 
                placeholder="请输入，如5000" 
                style={{ width: '100%' }}
                min={0}
              />
            </Form.Item>
          </div>
          
          <Form.Item
            name="description"
            label="模型描述"
            extra="不超过500字"
            rules={[{ max: 500, message: '模型描述不能超过500个字符' }]}
          >
            <TextArea 
              placeholder="请输入模型的描述或用途"
              rows={3}
              maxLength={500} 
              showCount 
            />
          </Form.Item>
        </Form>
      )}
    </Drawer>
  );
};

export default ModelDrawer; 