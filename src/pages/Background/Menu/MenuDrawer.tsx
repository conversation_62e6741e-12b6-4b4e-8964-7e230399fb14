import React, { useEffect } from 'react';
import { 
  AppstoreOutlined, 
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import {
  Button,
  Descriptions,
  Drawer,
  Empty,
  Form,
  Input,
  InputNumber,
  Select,
  Space,
  Spin,
  Switch,
  TreeSelect,
  Typography
} from 'antd';
import { CreateMenuForm, Menu } from '@/types/Menu';
import * as AntdIcons from '@ant-design/icons';
import './index.css';

const { Title } = Typography;
const { Option } = Select;

// 获取所有Antd图标
const iconList = Object.keys(AntdIcons)
  .filter(key => key.endsWith('Outlined') || key.endsWith('Filled') || key.endsWith('TwoTone'))
  .map(key => ({
    label: key,
    value: key,
    icon: React.createElement((AntdIcons as any)[key])
  }));

// 格式化日期显示
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export type DrawerType = 'create' | 'edit' | 'view';

interface MenuDrawerProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (values: CreateMenuForm) => Promise<void>;
  loading: boolean;
  menu: Menu | null;
  type: DrawerType;
  treeData: any[];
  detailLoading?: boolean;
}

/**
 * 菜单管理抽屉组件
 */
const MenuDrawer: React.FC<MenuDrawerProps> = ({
  visible,
  onClose,
  onSubmit,
  loading,
  menu,
  type,
  treeData,
  detailLoading = false
}) => {
  const [form] = Form.useForm();
  const isView = type === 'view';
  const isCreate = type === 'create';
  const isEdit = type === 'edit';

  // 抽屉标题
  const getTitleText = () => {
    switch (type) {
      case 'create':
        return '新增菜单';
      case 'edit':
        return '编辑菜单';
      case 'view':
        return '菜单详情';
      default:
        return '菜单信息';
    }
  };

  // 组件挂载或visible/menu/type变化时重置表单
  useEffect(() => {
    if (visible) {
      if (isCreate) {
        form.resetFields();
        form.setFieldsValue({
          order: 0
        });
      } else if (menu && (isEdit || isView)) {
        form.setFieldsValue({
          name: menu.name,
          identifier: menu.identifier,
          order: menu.order,
          parent_id: menu.parent_id
        });
      }
    }
  }, [visible, menu, type, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    if (isView) {
      onClose();
      return;
    }

    try {
      const values = await form.validateFields();
      await onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (isCreate) {
      form.resetFields();
      form.setFieldsValue({
        order: 0
      });
    } else if (menu && isEdit) {
      form.setFieldsValue({
        name: menu.name,
        identifier: menu.identifier,
        order: menu.order,
        parent_id: menu.parent_id
      });
    }
  };

  // 渲染底部按钮
  const renderFooter = () => {
    if (isView) {
      return (
        <div className="drawer-footer">
          <Button type="primary" onClick={onClose}>
            关闭
          </Button>
        </div>
      );
    }
    
    return (
      <div className="drawer-footer">
        <Button onClick={onClose}>取消</Button>
        <Button icon={<ReloadOutlined />} onClick={handleReset}>重置</Button>
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          保存
        </Button>
      </div>
    );
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <AppstoreOutlined />
          <span>{getTitleText()}</span>
        </div>
      }
      width={600}
      placement="right"
      onClose={onClose}
      open={visible}
      className="menu-drawer"
      footer={renderFooter()}
    >
      {isView ? (
        detailLoading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
            <Spin size="large" tip="加载菜单详情..." />
          </div>
        ) : menu ? (
          <Descriptions bordered column={1} size="middle" labelStyle={{ width: "140px" }}>
            <Descriptions.Item label="菜单名称">{menu.name}</Descriptions.Item>
            <Descriptions.Item label="菜单标识符">{menu.identifier}</Descriptions.Item>
            {/* <Descriptions.Item label="路径">{menu.path}</Descriptions.Item>
            <Descriptions.Item label="图标">
              {(AntdIcons as any)[menu.icon] ? 
                React.createElement((AntdIcons as any)[menu.icon]) : '-'}
              {' '}{menu.icon}
            </Descriptions.Item> */}
            <Descriptions.Item label="排序">{menu.order}</Descriptions.Item>
            <Descriptions.Item label="父菜单ID">{menu.parent_id || '-'}</Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {formatDate(menu.created_at)}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {formatDate(menu.updated_at)}
            </Descriptions.Item>
          </Descriptions>
        ) : (
          <Empty description="未找到菜单详情" />
        )
      ) : (
        <Form
          form={form}
          layout="vertical"
          disabled={isView}
          onFinish={onSubmit}
          initialValues={{ order: 0 }}
          className="menu-drawer-form"
        >
          <Form.Item
            name="name"
            label="菜单名称"
            rules={[
              { required: true, message: '请输入菜单名称' },
              { max: 50, message: '菜单名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入菜单名称，如HI-InsightPlus" maxLength={50} />
          </Form.Item>
          
          <Form.Item
            name="parent_id"
            label="父菜单"
          >
            <TreeSelect
              allowClear
              treeData={treeData}
              placeholder="请选择父菜单（可选）"
              treeDefaultExpandAll
              showSearch
              filterTreeNode={(input, node) => 
                (node.title as string).toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </Form.Item>
          
          <Form.Item
            name="identifier"
            label="菜单标识符"
            rules={[
              { required: true, message: '请输入菜单标识符' },
              { max: 30, message: '菜单标识符不能超过30个字符' }
            ]}
          >
            <Input placeholder="请输入菜单标识符，如insight_plus" maxLength={30} />
          </Form.Item>

          <Form.Item
            name="order"
            label="排序"
            rules={[{ required: true, message: '请输入排序值' }]}
          >
            <InputNumber min={0} max={9999} style={{ width: '100%' }} />
          </Form.Item>
          
          {/* <Form.Item
            name="path"
            label="路径"
            rules={[
              { required: true, message: '请输入路径' },
              { pattern: /^\/[a-z0-9_\/]*$/, message: '路径必须以/开头，只能包含小写字母、数字、下划线和斜杠' }
            ]}
          >
            <Input placeholder="请输入路径，如/insight_plus" />
          </Form.Item> */}
          
          {/* <Form.Item
            name="icon"
            label="图标"
            rules={[{ required: true, message: '请选择图标' }]}
          >
            <Select
              placeholder="请选择图标"
              showSearch
              optionFilterProp="label"
              optionLabelProp="label"
            >
              {iconList.map(icon => (
                <Option key={icon.value} value={icon.value} label={icon.value}>
                  <Space>
                    {icon.icon}
                    <span>{icon.label}</span>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item> */}
        </Form>
      )}
    </Drawer>
  );
};

export default MenuDrawer; 