import { CreateMenuForm, Menu, MenuTreeNode } from '@/types/Menu';
import { menuApi } from '@/utils/api';
import { 
  AppstoreOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  EyeOutlined, 
  PlusOutlined, 
  SearchOutlined,
  MinusOutlined,
  PlusOutlined as PlusOutlinedIcon
} from '@ant-design/icons';
import {
  Button,
  Card,
  Descriptions,
  Empty,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Spin,
  Switch,
  Table,
  Tooltip,
  TreeSelect,
  Typography
} from 'antd';
import React, { useEffect, useState } from 'react';
import * as AntdIcons from '@ant-design/icons';
import './index.css';
import MenuDrawer, { DrawerType } from './MenuDrawer';

const { Title, Text } = Typography;
const { Option } = Select;

// 格式化日期显示
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取所有Antd图标
const iconList = Object.keys(AntdIcons)
  .filter(key => key.endsWith('Outlined') || key.endsWith('Filled') || key.endsWith('TwoTone'))
  .map(key => ({
    label: key,
    value: key,
    icon: React.createElement((AntdIcons as any)[key])
  }));

/**
 * 菜单管理页面组件
 */
const MenuManagement: React.FC = () => {
  const [menusTree, setMenusTree] = useState<MenuTreeNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerType, setDrawerType] = useState<DrawerType>('create');
  const [editingMenu, setEditingMenu] = useState<Menu | null>(null);
  const [searchText, setSearchText] = useState('');
  const [inputText, setInputText] = useState(''); // 用于存储输入框的临时值
  const [detailLoading, setDetailLoading] = useState(false);
  const [form] = Form.useForm();
  const [treeData, setTreeData] = useState<any[]>([]);

  // 获取所有模块
  const fetchModules = async () => {
    try {
      setLoading(true);
      const data = await menuApi.getMenuTree();
      const menusArray = Array.isArray(data) ? data : []; 
      setMenusTree(menusArray);
      console.log('获取到菜单列表:', menusArray);

      // 处理模块树结构用于选择父模块
      const tree = data.map((menu: MenuTreeNode) => ({
        title: menu.name,
        value: menu.id,
        key: menu.id,
        children: menu.children?.map((child: MenuTreeNode) => ({
          title: child.name,
          value: child.id,
          key: child.id,
        }))
      }));
      setTreeData(tree);
    } catch (error) {
      console.error('获取菜单列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: CreateMenuForm) => {
    try {
      setLoading(true);
      const menuData: CreateMenuForm = {
        name: values.name,
        identifier: values.identifier,
        order: values.order,
        parent_id: values.parent_id || undefined
      };

      let response;
      if (editingMenu && drawerType === 'edit') {
        // 更新现有菜单
        response = await menuApi.updateMenu(editingMenu.id, menuData);
        message.success('菜单已更新');
      } else {
        // 创建新菜单
        response = await menuApi.createMenu(menuData);
        message.success('菜单已创建');
      }

      console.log('菜单操作结果:', response);
      setDrawerVisible(false);
      fetchModules();
    } catch (error) {
      console.error('保存菜单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除菜单
  const handleDelete = async (menuId: string) => {
    try {
      setLoading(true);
      await menuApi.deleteMenu(menuId);
      message.success('菜单已删除');
      fetchModules();
    } catch (error) {
      console.error('删除菜单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 打开创建菜单抽屉
  const showCreateDrawer = () => {
    setEditingMenu(null);
    setDrawerType('create');
    setDrawerVisible(true);
  };

  // 打开编辑菜单抽屉
  const showEditDrawer = (menu: Menu) => {
    setEditingMenu(menu);
    setDrawerType('edit');
    setDrawerVisible(true);
  };

  // 显示菜单详情
  const showMenuDetail = async (menuId: string) => {
    try {
      // 先打开抽屉
      setEditingMenu(null);
      setDrawerType('view');
      setDrawerVisible(true);
      setDetailLoading(true);
      
      const menuDetail = await menuApi.getMenu(menuId);
      if (menuDetail) {
        setEditingMenu(menuDetail);
        console.log('获取菜单详情:', menuDetail);
      } else {
        message.error('未找到菜单详情');
      }
    } catch (error) {
      console.error('获取菜单详情失败:', error);
    } finally {
      setDetailLoading(false);
    }
  };

  // 关闭抽屉
  const closeDrawer = () => {
    setDrawerVisible(false);
  };

  // 组件挂载时获取模块列表
  useEffect(() => {
    fetchModules();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 100,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '菜单名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '菜单标识符',
      dataIndex: 'identifier',
      key: 'identifier',
    },
    {
      title: '排序',
      dataIndex: 'order',
      key: 'order',
      sorter: (a: Menu, b: Menu) => a.order - b.order,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => formatDate(text),
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      render: (_: any, record: MenuTreeNode) => (
        <Space size="small">
          <Button 
            type="primary"
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => showMenuDetail(record.id)}
            className="action-button view-button"
          >
            查看
          </Button>
          <Button 
            type="primary"
            size="small" 
            icon={<EditOutlined />}
            onClick={() => showEditDrawer(record)}
            className="action-button edit-button"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此菜单吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              danger
              size="small" 
              icon={<DeleteOutlined />}
              className="action-button delete-button"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="management-container">
      <Card
        title={
          <div className="menu-management-header">
            <div className="menu-management-title">
              <AppstoreOutlined className="menu-icon" />
              <Title level={4} style={{ margin: 0 }}>
                菜单管理
              </Title>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateDrawer}
            >
              新增菜单
            </Button>
          </div>
        }
        className="menu-management-card"
      >

        {/* 模块列表 */}
        {loading ? (
          <div className="menu-loading">
            <Spin size="large" />
          </div>
        ) : (
          <Table
            columns={columns}
            dataSource={menusTree}
            rowKey="id"
            rowClassName={(record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark'}
            pagination={false}
            expandable={{
              defaultExpandAllRows: false,
              expandIcon: ({ expanded, onExpand, record }) => 
                record.children && record.children.length > 0 ? (
                  <Button
                    className="ant-table-row-expand-icon ant-table-row-expand-icon-collapsed"
                    type="text"
                    size="small"
                    onClick={e => onExpand(record, e)}
                  >
                    {expanded ? <MinusOutlined /> : <PlusOutlinedIcon />}
                  </Button>
                ) : (
                  <div style={{ 
                    position: 'relative',
                    float: 'left',
                    display: 'block',
                    width: '24px',
                    height: '16px',
                  }} />
                )
            }}
          />
        )}
      </Card>

      {/* 菜单抽屉组件 */}
      <MenuDrawer
        visible={drawerVisible}
        onClose={closeDrawer}
        onSubmit={handleSubmit}
        loading={loading}
        menu={editingMenu}
        type={drawerType}
        treeData={treeData}
        detailLoading={detailLoading}
      />
    </div>
  );
};

export default MenuManagement; 