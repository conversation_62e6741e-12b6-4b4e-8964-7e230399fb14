import { Menu, MenuTreeNode } from '@/types/Menu';
import { CreateRoleForm, Role } from '@/types/Role';
import { PaginationState, processPaginatedResponse } from '@/types/Page';
import { menuApi, roleApi, orgAdminApi } from '@/utils/api';
import { useAuth } from '@/contexts/AuthContext';
import { 
  DeleteOutlined, 
  EditOutlined, 
  EyeOutlined, 
  LockOutlined, 
  PlusOutlined, 
  SearchOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons';
import {
  Button,
  Card,
  Descriptions,
  Empty,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Spin,
  Switch,
  Table,
  Tooltip,
  Typography,
  Tree,
  TreeSelect
} from 'antd';
import React, { useEffect, useState } from 'react';
import RoleDrawer, { DrawerType } from './RoleDrawer';
import './index.css';
import { userInfo } from 'os';

const { Title, Text } = Typography;
const { Option } = Select;
const { TreeNode } = Tree;

// 格式化日期显示
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * 角色管理页面组件
 */
const RoleManagement: React.FC = () => {
  const { isSuperAdmin } = useAuth(); // 超级管理员
  const { isOrgAdmin } = useAuth(); // 机构管理员
  const { userInfo } = useAuth(); // 当前登录用户信息
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerType, setDrawerType] = useState<DrawerType>('create');
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [searchText, setSearchText] = useState('');
  const [inputText, setInputText] = useState(''); // 用于存储输入框的临时值
  const [form] = Form.useForm();
  const [menuTree, setMenuTree] = useState<MenuTreeNode[]>([]);
  const [allMenus, setAllMenus] = useState<Menu[]>([]);
  const [menuLoading, setMenuLoading] = useState(false);

  // 分页相关状态
  const [pagination, setPagination] = useState<PaginationState>({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取所有角色
  const fetchRoles = async (page = 1, size = 10, keyword = '') => {
    try {
      setLoading(true);
      const result = await roleApi.getAllRolesPaginated({
        page,
        size,
        keyword
      });
      
      // 使用通用处理函数处理分页数据
      const { data: rolesArray, total } = processPaginatedResponse<Role>(result);
      
      setRoles(rolesArray);
      setPagination({
        ...pagination,
        current: page,
        pageSize: size,
        total
      });
      
      console.log('获取到角色列表:', rolesArray, '总数:', total);
    } catch (error) {
      console.error('获取角色列表失败:', error);
      setRoles([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: CreateRoleForm) => {
    try {
      setLoading(true);
      const roleData: CreateRoleForm = {
        name: values.name,
        identifier: values.identifier,
      };

      let response;
      if (editingRole && drawerType === 'edit') {
        // 更新现有角色
        response = await roleApi.updateRole(editingRole.id, roleData);
        message.success('角色已更新');
      } else {
        // 创建新角色
        response = await roleApi.createRole(roleData);
        message.success('角色已创建');
      }

      console.log('角色操作结果:', response);
      setDrawerVisible(false);
      fetchRoles();
    } catch (error) {
      console.error('保存角色失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除角色
  const handleDelete = async (roleId: string) => {
    try {
      setLoading(true);
      await roleApi.deleteRole(roleId);
      message.success('角色已删除');
      fetchRoles();
    } catch (error) {
      console.error('删除角色失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 打开创建角色抽屉
  const showCreateDrawer = () => {
    setEditingRole(null);
    setDrawerType('create');
    setDrawerVisible(true);
  };

  // 打开编辑角色抽屉
  const showEditDrawer = (role: Role) => {
    setEditingRole(role);
    setDrawerType('edit');
    setDrawerVisible(true);
  };

  // 打开权限配置抽屉
  const showPermissionDrawer = (role: Role) => {
    setEditingRole(role);
    setDrawerType('permission');
    setDrawerVisible(true);
  };

  // 关闭抽屉
  const closeDrawer = () => {
    setDrawerVisible(false);
  };

  // 显示角色详情
  const showRoleDetail = async (roleId: string) => {
    try {
      // setLoading(true);
      const roleDetail = await roleApi.getRole(roleId);
      if (roleDetail) {
        setEditingRole(roleDetail);
        setDrawerType('view');
        setDrawerVisible(true);
        console.log('获取角色详情:', roleDetail);
      } else {
        message.error('未找到角色详情');
      }
    } catch (error) {
      console.error('获取角色详情失败:', error);
    } finally {
      // setLoading(false);
    }
  };

  // 过滤角色列表
  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchText.toLowerCase()) ||
    role.identifier.toLowerCase().includes(searchText.toLowerCase())
  );

  // 获取菜单名称
  const getMenuName = (menuId: string): string => {
    const menu = allMenus.find(m => m.id === menuId);
    return menu ? menu.name : menuId;
  };

  // 处理搜索
  const handleSearch = () => {
    setSearchText(inputText);
    // 重置到第一页，执行搜索
    fetchRoles(1, pagination.pageSize, inputText);
  };

  // 处理重置
  const handleReset = () => {
    setInputText('');
    setSearchText('');
    // 重置搜索条件，恢复到第一页
    fetchRoles(1, pagination.pageSize, '');
  };

  // 处理按键事件，按回车触发搜索
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    // 更新分页状态并重新获取数据
    fetchRoles(page, pageSize, searchText);
  };

  // 组件挂载时获取角色列表和菜单列表
  useEffect(() => {
    fetchRoles(1, pagination.pageSize, '');
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => 
        (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色标识',
      dataIndex: 'identifier',
      key: 'identifier',
    },
    // {
    //   title: '所属机构',
    //   dataIndex: 'organization.name',
    //   key: 'organization.name',
    //   render: (_: any, record: Role) => {
    //     return record.organization?.name || '-';
    //   },
    // },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => formatDate(text),
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      render: (_: any, record: Role) => (
        <Space size="small">
          <Button 
            type="primary"
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => showRoleDetail(record.id)}
            className="action-button view-button"
          >
            查看
          </Button>
          {record.identifier !== "admin" && (
            <>
              <Button 
                type="primary"
                size="small" 
                icon={<EditOutlined />}
                onClick={() => showEditDrawer(record)}
                className="action-button edit-button"
              >
                编辑
              </Button>
              <Button 
                type="primary"
                size="small" 
                icon={<SafetyCertificateOutlined />}
                onClick={() => showPermissionDrawer(record)}
                className="action-button permission-button"
              >
                授权
              </Button>
            </>
          )}
          {/* <Popconfirm
            title="确定要删除此角色吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              danger
              size="small" 
              icon={<DeleteOutlined />}
              className="action-button delete-button"
            >
              删除
            </Button>
          </Popconfirm> */}
        </Space>
      ),
    },
  ];

  return (
    <div className="management-container">
      <Card
        title={
          <div className="role-management-header">
            <div className="role-management-title">
              <SafetyCertificateOutlined className="role-icon" />
              <Title level={4} style={{ margin: 0 }}>
                角色管理
              </Title>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateDrawer}
            >
              新增角色
            </Button>
          </div>
        }
        className="role-management-card"
      >
        {/* 搜索框 */}
        <div className="role-search-bar">
          <Input
            placeholder="搜索角色名称、标识"
            value={inputText}
            onChange={e => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            prefix={<SearchOutlined />}
            style={{ width: 300 }}
            allowClear
          />
          <Space>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              搜索
            </Button>
            <Button
              onClick={handleReset}
            >
              重置
            </Button>
          </Space>
        </div>

        {/* 角色列表 */}
        {loading ? (
          <div className="role-loading">
            <Spin size="large" />
          </div>
        ) : (
          <Table
            columns={columns}
            dataSource={roles}
            rowKey="id"
            rowClassName={(record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark'}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              onChange: (page, pageSize) => handleTableChange(page, pageSize || 10)
            }}
          />
        )}
      </Card>

      {/* 角色抽屉组件 */}
      <RoleDrawer
        visible={drawerVisible}
        onClose={closeDrawer}
        onSubmit={handleSubmit}
        loading={loading}
        role={editingRole}
        type={drawerType}
      />
    </div>
  );
};

export default RoleManagement; 