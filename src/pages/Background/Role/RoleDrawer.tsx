import React, { useEffect, useState } from 'react';
import { 
  SafetyCertificateOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import {
  Button,
  Descriptions,
  Drawer,
  Form,
  Input,
  Select,
  Space,
  Switch,
  Typography,
  Tree,
  Spin,
  message
} from 'antd';
import { CreateRoleForm, Role } from '@/types/Role';
import { MenuTreeNode } from '@/types/Menu';
import { menuApi, roleApi, orgAdminApi } from '@/utils/api';
import { useAuth } from '@/contexts/AuthContext';
import './index.css';

const { Title } = Typography;
const { TextArea } = Input;

// 格式化日期显示
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export type DrawerType = 'create' | 'edit' | 'view' | 'permission';

interface RoleDrawerProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (values: CreateRoleForm) => Promise<void>;
  loading: boolean;
  role: Role | null;
  type: DrawerType;
}

/**
 * 角色管理抽屉组件
 */
const RoleDrawer: React.FC<RoleDrawerProps> = ({
  visible,
  onClose,
  onSubmit,
  loading,
  role,
  type
}) => {
  const { isSuperAdmin } = useAuth(); // 超级管理员
  const { isOrgAdmin } = useAuth(); // 机构管理员
  const { userInfo } = useAuth(); // 当前登录用户信息
  const [form] = Form.useForm();
  const isView = type === 'view';
  const isCreate = type === 'create';
  const isEdit = type === 'edit';
  const isPermission = type === 'permission';

  // 菜单权限相关状态
  const [menuTree, setMenuTree] = useState<MenuTreeNode[]>([]);
  const [menuLoading, setMenuLoading] = useState(false);
  const [permissionLoading, setPermissionLoading] = useState(false);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  // 抽屉标题
  const getTitleText = () => {
    switch (type) {
      case 'create':
        return '新增角色';
      case 'edit':
        return '编辑角色';
      case 'view':
        return '角色详情';
      case 'permission':
        return `配置角色权限: ${role?.name || ''}`;
      default:
        return '角色信息';
    }
  };

  // 组件挂载或visible/role/type变化时重置表单
  useEffect(() => {
    if (visible) {
      if (isCreate) {
        form.resetFields();
      } else if (role && (isEdit || isView)) {
        form.setFieldsValue({
          name: role.name,
          identifier: role.identifier,
        });
      } else if (isPermission && role) {
        fetchMenuTree();
      }
    }
  }, [visible, role, type, form]);

  // 获取菜单树
  const fetchMenuTree = async () => {
    if (!visible || !role) return;
    
    try {
      setMenuLoading(true);
      
      let availableMenuTree: MenuTreeNode[] = [];
      if (isSuperAdmin) {
        // 超级管理员所有菜单
        const data = await menuApi.getMenuTree();
        availableMenuTree = Array.isArray(data) ? data : [];
      } else if (isOrgAdmin && userInfo) {
        // 机构管理员获取机构可用菜单
        const data = await orgAdminApi.getOrgMenuTree(userInfo?.organization?.id || '');
        availableMenuTree = Array.isArray(data) ? data : [];
      }
      
      setMenuTree(availableMenuTree);
      console.log('获取到菜单树:', availableMenuTree);
      
      const allKeys: React.Key[] = [];
      const extractKeys = (items: MenuTreeNode[]) => {
        items.forEach(item => {
          allKeys.push(item.id);
          if (item.children && item.children.length > 0) {
            extractKeys(item.children);
          }
        });
      };
      
      extractKeys(availableMenuTree);
      setExpandedKeys(allKeys);
      
      // 获取角色已分配的菜单id，并选中
      try {
        const roleMenus = await orgAdminApi.getRoleMenu(role.id);
        if (roleMenus && Array.isArray(roleMenus)) {
          const assignedMenuIds = roleMenus.map(menu => menu.menu_id).filter(id => id !== undefined);
          setCheckedKeys(assignedMenuIds);
          console.log('获取到角色已分配菜单:', assignedMenuIds);
        }
      } catch (roleMenuError) {
        console.error('获取角色菜单失败:', roleMenuError);
      }
    } catch (error) {
      console.error('获取菜单树失败:', error);
      setMenuTree([]);
    } finally {
      setMenuLoading(false);
    }
  };

  // 保存权限
  const handleSavePermission = async () => {
    if (!role) return;
    const organizationId = userInfo?.organization?.id || '';
    
    try {
      setPermissionLoading(true);
      
      await orgAdminApi.OrgRoleMenus({
        organizationId: organizationId,
        roleId: role.id,
        menu_ids: checkedKeys.map(key => key.toString())
      });
      
      message.success('角色权限已成功分配');
      onClose();
    } catch (error) {
      console.error('分配权限失败:', error);
    } finally {
      setPermissionLoading(false);
    }
  };

  // 重置权限选择
  const handleResetPermission = () => {
    setCheckedKeys([]);
  };

  // 全选
  const handleSelectAll = () => {
    // 所有菜单ID
    const allMenuIds: React.Key[] = [];
    const collectIds = (items: MenuTreeNode[]) => {
      items.forEach(item => {
        allMenuIds.push(item.id);
        if (item.children && item.children.length > 0) {
          collectIds(item.children);
        }
      });
    };
    
    collectIds(menuTree);
    setCheckedKeys(allMenuIds);
    console.log('全选菜单权限:', allMenuIds);
  };

  // 树节点选择变化
  const onCheck = (checked: any) => {
    console.log('选中的菜单:', checked);
    // 获取树选择组件返回的选中键值
    if (Array.isArray(checked)) {
      setCheckedKeys(checked);
    } else if (checked.checked) {
      setCheckedKeys(checked.checked);
    } else {
      setCheckedKeys([]);
    }
  };

  // 展开/收起节点
  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (isView) {
      onClose();
      return;
    }

    if (isPermission) {
      await handleSavePermission();
      return;
    }

    try {
      const values = await form.validateFields();
      await onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (isPermission) {
      handleResetPermission();
      return;
    }

    if (isCreate) {
      form.resetFields();
    } else if (role && isEdit) {
      form.setFieldsValue({
        name: role.name,
        identifier: role.identifier,
      });
    }
  };

  // 渲染底部按钮
  const renderFooter = () => {
    if (isView) {
      return (
        <div className="drawer-footer">
          <Button type="primary" onClick={onClose}>
            关闭
          </Button>
        </div>
      );
    }
    
    if (isPermission) {
      return (
        <div className="drawer-footer">
          <Button onClick={onClose}>取消</Button>
          <Button onClick={handleResetPermission}>重置</Button>
          <Button type="primary" onClick={handleSavePermission} loading={permissionLoading}>
            保存
          </Button>
        </div>
      );
    }
    
    return (
      <div className="drawer-footer">
        <Button onClick={onClose}>取消</Button>
        <Button icon={<ReloadOutlined />} onClick={handleReset}>重置</Button>
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          保存
        </Button>
      </div>
    );
  };

  // 准备树形数据
  const treeData = menuTree.map(menu => ({
    title: menu.name,
    key: menu.id,
    children: menu.children?.map(child => ({
      title: child.name,
      key: child.id,
    }))
  }));

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <SafetyCertificateOutlined />
          <span>{getTitleText()}</span>
        </div>
      }
      width={600}
      placement="right"
      onClose={onClose}
      open={visible}
      className="role-drawer"
      footer={renderFooter()}
    >
      {isPermission ? (
        <Spin spinning={menuLoading}>
          {menuTree.length > 0 ? (
            <div className="menu-tree-container">
              <Title level={5} style={{ marginBottom: '16px' }}>
                {isSuperAdmin ? 
                  '请选择该角色可访问的菜单' : 
                  '请从机构可用菜单中选择该角色可访问的菜单'}
              </Title>
              <div style={{ marginBottom: '16px', color: '#999', fontSize: '14px' }}>
                提示：勾选父菜单将自动选中所有子菜单
              </div>
              <div style={{ marginBottom: '16px' }}>
                <Space>
                  <Button size="small" type="primary" onClick={handleSelectAll}>全选</Button>
                  <Button size="small" onClick={handleResetPermission}>全不选</Button>
                </Space>
              </div>
              <Tree
                checkable
                onCheck={onCheck}
                checkedKeys={checkedKeys}
                treeData={treeData}
                expandedKeys={expandedKeys}
                onExpand={onExpand}
                defaultExpandAll
              />
            </div>
          ) : (
            <div className="no-menu-data" style={{ textAlign: 'center', padding: '50px 0' }}>
              暂无可配置的菜单数据
            </div>
          )}
        </Spin>
      ) : isView && role ? (
        <Descriptions bordered column={1} size="middle" labelStyle={{ width: "140px" }}>
          <Descriptions.Item label="角色名称">{role.name}</Descriptions.Item>
          <Descriptions.Item label="角色编码">{role.identifier}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {formatDate(role.created_at)}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {formatDate(role.updated_at)}
          </Descriptions.Item>
        </Descriptions>
      ) : (
        <Form
          form={form}
          layout="vertical"
          disabled={isView}
          onFinish={onSubmit}
          className="role-drawer-form"
        >
          <Form.Item
            name="name"
            label="角色名称"
            rules={[
              { required: true, message: '请输入角色名称' },
              { max: 50, message: '角色名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入角色名称" maxLength={50} />
          </Form.Item>
          
          <Form.Item
            name="identifier"
            label="角色标识"
            rules={[
              { required: true, message: '请输入角色标识' },
              { max: 30, message: '角色标识不能超过30个字符' }
            ]}
          >
            <Input placeholder="请输入角色标识" maxLength={30} />
          </Form.Item>
        </Form>
      )}
    </Drawer>
  );
};

export default RoleDrawer; 