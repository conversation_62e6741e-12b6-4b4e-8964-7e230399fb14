.role-management-container {
  width: 100%;
}

.role-management-card {
  width: 100%;
  overflow: auto;
}

.role-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.role-management-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.role-icon {
  font-size: 24px;
  color: #1890ff;
}

.role-search-bar {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.role-loading {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.menu-tree-container {
  padding: 8px 0;
}

.no-menu-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}

/* 表格隔行样式 */
.table-row-light {
  background-color: #ffffff;
}

.table-row-dark {
  background-color: #f5f7fa;
}

/* 操作按钮样式 */
.action-button {
  min-width: 70px;
  margin-right: 8px;
}

.view-button {
  background-color: #13c2c2;
  border-color: #13c2c2;
}

.view-button:hover {
  background-color: #36cfc9;
  border-color: #36cfc9;
}

.edit-button {
  background-color: #1677ff;
  border-color: #1677ff;
}

.edit-button:hover {
  background-color: #4096ff;
  border-color: #4096ff;
}

.permission-button {
  background-color: #722ed1;
  border-color: #722ed1;
}

.permission-button:hover {
  background-color: #9254de;
  border-color: #9254de;
}

.delete-button {
  min-width: 70px;
}

/* 抽屉样式 */
.role-drawer .ant-drawer-body {
  padding: 24px;
}

.role-drawer-form {
  max-width: 100%;
}

/* 抽屉底部按钮样式 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 10px 16px;
  background: #fff;
}

/* 表格样式 */ 