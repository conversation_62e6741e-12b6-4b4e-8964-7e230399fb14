import { Organization } from '@/types/Organization';
import { CreateUserForm, UserInfo, UserRole, UserRoleName } from '@/types/UserInfo';
import { PaginationState, processPaginatedResponse } from '@/types/Page';
import { useAuth } from '@/contexts/AuthContext';
import { authApi, organizationApi, roleApi } from '@/utils/api';
import { decrypt, encrypt } from '@/utils/crypto';
import { DeleteOutlined, EditOutlined, PlusOutlined, SearchOutlined, TeamOutlined, EyeOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Empty,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  Typography
} from 'antd';
import React, { useEffect, useState } from 'react';
import './index.css';
import { Role } from '@/types/Role';
import OrgUserDrawer, { DrawerType } from './OrgUserDrawer';

const { Title, Text } = Typography;
const { Option } = Select;

// 格式化日期显示
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * 用户管理页面组件
 */
const OrgUsersManagement: React.FC = () => {
  const { isSuperAdmin } = useAuth(); // 超级管理员
  const { isOrgAdmin } = useAuth(); // 机构管理员
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerType, setDrawerType] = useState<DrawerType>('create');
  const [editingUser, setEditingUser] = useState<UserInfo | null>(null);
  const [searchText, setSearchText] = useState('');
  const [inputText, setInputText] = useState(''); // 用于存储输入框的临时值
  const [usedCount, setUsedCount] = useState<number>(0);
  const [form] = Form.useForm();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [organizationsLoading, setOrganizationsLoading] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);

  // 分页相关状态
  const [pagination, setPagination] = useState<PaginationState>({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取所有用户
  const fetchUsers = async (page = 1, size = 10, keyword = '') => {
    try {
      setLoading(true);
      const result = await authApi.getAllUsers({
        page,
        size,
        keyword
      });

      // 使用通用处理函数处理分页数据
      const { data: usersArray, total } = processPaginatedResponse<UserInfo>(result);

      setUsers(usersArray);
      setPagination({
        ...pagination,
        current: page,
        pageSize: size,
        total
      });

      console.log('获取到用户列表:', usersArray, '总数:', total);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // 创建或更新用户
  const handleSubmit = async (values: CreateUserForm) => {
    try {
      setLoading(true);
      const { confirmPassword, maxAllowedCount, ...userData } = values;

      // 如果是修改用户且没有输入密码，则不加密密码
      let userDataToSubmit: any = { ...userData };

      // 如果有输入密码，则加密密码
      if (userData.password) {
        userDataToSubmit.password = encrypt(userData.password);
      } else if (editingUser) {
        // 如果是编辑模式且未输入密码，则删除密码字段
        delete userDataToSubmit.password;
      }

      let userId = '';
      if (editingUser && drawerType === 'edit') {
        // 更新用户
        await authApi.updateUser(editingUser.id, userDataToSubmit);
        userId = editingUser.id;
      } else {
        // 创建新用户
        const newUser = await authApi.createUser(userDataToSubmit);
        userId = newUser.id;
      }
      console.log('userId', userId);
      // 设置用户使用次数
      // 管理员不设置，但要调接口生成记录
      // if (userId) {
      //   try {
      //     if (maxAllowedCount && maxAllowedCount > 1000) {
      //       message.warning('最大使用次数不能超过1000');
      //       return;
      //     }
      //     await authApi.createUserMaxCount({ max_allowed_count: maxAllowedCount || 0, user_id: userId });
      //     console.log('最大使用次数:', maxAllowedCount);

      //     message.success('用户保存成功');
      //     // 重新获取用户列表
      //     fetchUsers();
      //     // 关闭抽屉并重置表单
      //     setDrawerVisible(false);
      //     form.resetFields();
      //     setEditingUser(null);

      //   } catch (error) {
      //     console.error('设置使用次数失败:', error);
      //   }
      // }

      message.success('用户保存成功');
      // 重新获取用户列表
      fetchUsers();
      // 关闭抽屉并重置表单
      setDrawerVisible(false);
      form.resetFields();
      setEditingUser(null);
    } catch (error) {
      console.error('操作失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除用户
  const handleDelete = async (userId: string) => {
    try {
      setLoading(true);
      await authApi.deleteUser(userId);
      message.success('用户已删除');
      fetchUsers();
    } catch (error) {
      console.error('删除用户失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 打开创建用户抽屉
  const showCreateDrawer = () => {
    setEditingUser(null);
    setDrawerType('create');
    setDrawerVisible(true);
  };

  // 打开编辑用户抽屉
  const showEditDrawer = (user: UserInfo) => {
    setEditingUser(user);
    setDrawerType('edit');
    setDrawerVisible(true);
  };

  // 显示用户详情
  const showUserDetail = (user: UserInfo) => {
    setEditingUser(user);
    setDrawerType('view');
    setDrawerVisible(true);
  };

  // 关闭抽屉
  const closeDrawer = () => {
    setDrawerVisible(false);
  };

  // 处理搜索
  const handleSearch = () => {
    setSearchText(inputText);
    // 重置到第一页，执行搜索
    fetchUsers(1, pagination.pageSize, inputText);
  };

  // 处理重置
  const handleReset = () => {
    setInputText('');
    setSearchText('');
    // 重置搜索条件，恢复到第一页
    fetchUsers(1, pagination.pageSize, '');
  };

  // 处理按键事件，按回车触发搜索
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    // 更新分页状态并重新获取数据
    fetchUsers(page, pageSize, searchText);
  };

  // 组件挂载时获取用户列表
  useEffect(() => {
    fetchUsers(1, pagination.pageSize, '');
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) =>
        (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '姓名',
      dataIndex: 'realname',
      key: 'realname',
      render: (text: string) => text || '-',
    },
    // {
    //   title: '单位',
    //   dataIndex: 'company',
    //   key: 'company',
    //   render: (text: string) => text || '-',
    // },
    // {
    //   title: '职称',
    //   dataIndex: 'position',
    //   key: 'position',
    //   render: (text: string) => text || '-',
    // },
    {
      title: '所属机构',
      dataIndex: 'organization.name',
      key: 'organization.name',
      render: (_: any, record: UserInfo) => {
        return record.organization?.name || '-';
      },
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      key: 'mobile',
      render: (_: any, record: UserInfo) => {
        return decrypt(record.mobile) || '-';
      },
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (_: any, record: UserInfo) => record.role?.name || '-',
    },
    {
      title: '使用次数',
      key: 'used_count',
      width: 100,
      render: (_: any, record: UserInfo) => {
        const usedCount = record.used_count || 0;
        const maxCount = record.max_allowed_count === null ? '无限次' : (record.max_allowed_count || 0);
        return `${usedCount} / ${maxCount}`;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => text ? formatDate(text) : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: UserInfo) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => showUserDetail(record)}
            className="action-button view-button"
          >
            查看
          </Button>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditDrawer(record)}
            className="action-button edit-button"
          >
            编辑
          </Button>
          {/* <Popconfirm
            title="确定要删除此用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              className="action-button delete-button"
            >
              删除
            </Button>
          </Popconfirm> */}
        </Space>
      ),
    },
  ];

  return (
    <div className="management-container">
      <Card
        title={
          <div className="user-management-header">
            <div className="user-management-title">
              <TeamOutlined className="user-icon" />
              <Title level={4} style={{ margin: 0 }}>
                用户管理
              </Title>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateDrawer}
            >
              创建用户
            </Button>
          </div>
        }
        className="user-management-card"
      >
        {/* 搜索框 */}
        <div className="user-search-bar">
          <Input
            placeholder="搜索用户名、姓名、单位、所属机构"
            value={inputText}
            onChange={e => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            prefix={<SearchOutlined />}
            style={{ width: 300 }}
            allowClear
          />
          <Space>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              搜索
            </Button>
            <Button
              onClick={handleReset}
            >
              重置
            </Button>
          </Space>
        </div>

        {/* 用户列表 */}
        {loading ? (
          <div className="user-loading">
            <Spin size="large" />
          </div>
        ) : (
          <Table
            columns={columns}
            dataSource={users}
            rowKey="id"
            rowClassName={(record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark'}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              onChange: (page, pageSize) => handleTableChange(page, pageSize || 10)
            }}
          />
        )}
      </Card>

      {/* 用户抽屉组件 */}
      <OrgUserDrawer
        visible={drawerVisible}
        onClose={closeDrawer}
        onSubmit={handleSubmit}
        loading={loading}
        user={editingUser}
        type={drawerType}
      />
    </div>
  );
};

export default OrgUsersManagement; 