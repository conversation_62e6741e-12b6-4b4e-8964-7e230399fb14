import React, { useEffect, useState } from 'react';
import { 
  UserOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import {
  Button,
  Descriptions,
  Drawer,
  Form,
  Input,
  Select,
  InputNumber,
  Space,
  Switch,
  Typography,
  Spin,
  Empty
} from 'antd';
import { CreateUserForm, UserInfo, UserRole, UserRoleName } from '@/types/UserInfo';
import { Organization } from '@/types/Organization';
import { Role } from '@/types/Role';
import { authApi, organizationApi, roleApi } from '@/utils/api';
import { useAuth } from '@/contexts/AuthContext';
import './index.css';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// 格式化日期显示
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export type DrawerType = 'create' | 'edit' | 'view';

interface OrgUserDrawerProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (values: CreateUserForm) => Promise<void>;
  loading: boolean;
  user: UserInfo | null;
  type: DrawerType;
}

/**
 * 用户管理抽屉组件
 */
const OrgUserDrawer: React.FC<OrgUserDrawerProps> = ({
  visible,
  onClose,
  onSubmit,
  loading,
  user,
  type
}) => {
  const { isSuperAdmin } = useAuth(); // 超级管理员
  const { isOrgAdmin } = useAuth(); // 机构管理员
  const { userInfo } = useAuth(); // 当前登录用户信息
  const [form] = Form.useForm();
  const isView = type === 'view';
  const isCreate = type === 'create';
  const isEdit = type === 'edit';
  const [usedCount, setUsedCount] = useState<number>(0);
  const [maxAllowedCount, setMaxAllowedCount] = useState<number | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [organizationsLoading, setOrganizationsLoading] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);
  const [selectedOrgId, setSelectedOrgId] = useState<string>('');
  const [isSuperAdminUser, setIsSuperAdminUser] = useState<boolean>(false);
  const [isOrgAdminUser, setIsOrgAdminUser] = useState<boolean>(false);

  // 获取所有机构
  const fetchOrganizations = async () => {
    try {
      setOrganizationsLoading(true);
      const data = await organizationApi.getAllOrganizations();
      const organizationsArray = Array.isArray(data) ? data : []; 
      setOrganizations(organizationsArray);
      console.log('获取到机构列表:', organizationsArray);
    } catch (error) {
      console.error('获取机构列表失败:', error);
      setOrganizations([]);
    } finally {
      setOrganizationsLoading(false);
    }
  };

  // 获取角色列表
  const fetchRoles = async (organizationId?: string) => {
    try {
      setRolesLoading(true);
      let data;
      
      if (isSuperAdmin && organizationId) {
        // 超级管理员需要传递机构ID
        data = await roleApi.getAllRoles(organizationId);
      } 
      else if (isOrgAdmin && userInfo?.organization?.id) {
        // 机构管理员不需要传递参数
        data = await roleApi.getOrgAllRoles();
      } else {
        // 其他情况或超管未选择机构时
        setRoles([]);
        setRolesLoading(false);
        return;
      }
      // 机构只能一个管理员 不可以选择 管理员角色
      const rolesArray = Array.isArray(data) ? data.filter(role => role.identifier !== "admin") : [];
      setRoles(rolesArray);
      console.log('获取到角色列表:', rolesArray);
    } catch (error) {
      console.error('获取角色列表失败:', error);
      setRoles([]);
    } finally {
      setRolesLoading(false);
    }
  };

  // 获取用户使用次数
  // const fetchUserMaxCount = async (userId: string) => {
  //   try {
  //     const data = await authApi.getUserMaxCount(userId);
  //     console.log('获取用户使用次数:', data);
  //     // 更新表单中的最大使用次数字段
  //     form.setFieldsValue({
  //       maxAllowedCount: data.max_allowed_count === null ? null : (data.max_allowed_count || 0)
  //     });
  //     // 保存已使用次数
  //     setUsedCount(data.used_count || 0);
  //     setMaxAllowedCount(data.max_allowed_count === null ? null : (data.max_allowed_count || 0));
  //   } catch (error) {
  //     console.error('获取用户最大使用次数失败:', error);
  //     // 设置默认值为0
  //     form.setFieldsValue({
  //       maxAllowedCount: 0
  //     });
  //     setUsedCount(0);
  //     setMaxAllowedCount(0);
  //   }
  // };

  // 处理机构选择变化
  const handleOrganizationChange = (orgId: string) => {
    setSelectedOrgId(orgId);
    
    // 清空角色选择
    form.setFieldValue('role_id', undefined);
    
    // 根据选择的机构获取角色列表
    if (orgId) {
      fetchRoles(orgId);
    } else {
      setRoles([]);
    }
  };

  // 抽屉标题
  const getTitleText = () => {
    switch (type) {
      case 'create':
        return '新增用户';
      case 'edit':
        return '编辑用户';
      case 'view':
        return '用户详情';
      default:
        return '用户信息';
    }
  };

  // 组件挂载或visible/user/type变化时重置表单和获取数据
  useEffect(() => {
    if (visible) {
      if (isSuperAdmin) {
        fetchOrganizations();
      }
      
      if (isCreate) {
        form.resetFields();
        setSelectedOrgId('');
        setIsSuperAdminUser(false);
        setIsOrgAdminUser(false);
        setMaxAllowedCount(0);
      } else if (user && (isEdit || isView)) {
        const orgId = user.organization?.id;
        setSelectedOrgId(orgId || '');
        
        console.log('user', user);
        console.log('user.role.identifier', user.role?.identifier);
        
        // 是否超级管理员用户
        const isSuperAdmin = user.role?.identifier === 'super_admin';
        const isOrgAdmin = user.role?.identifier === 'admin';
        setIsSuperAdminUser(isSuperAdmin);
        setIsOrgAdminUser(isOrgAdmin);

        // 直接从用户对象中获取使用次数信息
        const usedCount = user.used_count || 0;
        const maxCount = user.max_allowed_count === null ? 0 : (user.max_allowed_count || 0);
        
        setUsedCount(usedCount);
        setMaxAllowedCount(maxCount);
        
        form.setFieldsValue({
          username: user.username,
          realname: user.realname,
          company: user.company,
          position: user.position,
          achievement: user.achievement,
          role_id: user.role?.id,
          organization_id: orgId,
          used_count: usedCount,
          max_allowed_count: maxCount,
          // 清空密码
          password: '',
          confirmPassword: ''
        });
        
        // 获取角色列表
        if (orgId) {
          console.log('orgId', orgId);
          fetchRoles(orgId);
        }
        
        // 获取用户使用次数
        // if (user.id) {
        //   fetchUserMaxCount(user.id);
        // }
      }
    }
  }, [visible, user, type, form, isSuperAdmin, isOrgAdmin, userInfo]);
  
  // 如果是机构管理员，组件挂载时获取角色列表
  useEffect(() => {
    if (visible && isOrgAdmin) {
      fetchRoles('');
    }
  }, [visible, isOrgAdmin]);

  // 处理表单提交
  const handleSubmit = async () => {
    if (isView) {
      handleCloseDrawer();
      return;
    }

    try {
      const values = await form.validateFields();
      
      // 最大使用次数，超级管理员不填为null，其他角色不填为0
      // if (values.max_allowed_count === undefined || values.max_allowed_count === '') {
      //   values.max_allowed_count = isSuperAdminUser ? null : 0;
      // }
      
      await onSubmit(values);
      
      // 表单提交，清空密码
      form.setFieldsValue({
        password: '',
        confirmPassword: ''
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (isCreate) {
      form.resetFields();
    } else if (user && isEdit) {
      form.setFieldsValue({
        username: user.username,
        realname: user.realname,
        company: user.company,
        position: user.position,
        achievement: user.achievement,
        role_id: user.role?.id,
        organization_id: user.organization?.id,
        max_allowed_count: maxAllowedCount
      });
    }
  };

  // 抽屉关闭再次重置
  const handleCloseDrawer = () => {
    // 重置 清空密码
    form.resetFields();
    form.setFieldsValue({
      password: '',
      confirmPassword: ''
    });
    
    onClose();
  };

  // 渲染底部按钮
  const renderFooter = () => {
    if (isView) {
      return (
        <div className="drawer-footer">
          <Button type="primary" onClick={handleCloseDrawer}>
            关闭
          </Button>
        </div>
      );
    }
    
    return (
      <div className="drawer-footer">
        <Button onClick={handleCloseDrawer}>取消</Button>
        <Button icon={<ReloadOutlined />} onClick={handleReset}>重置</Button>
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          保存
        </Button>
      </div>
    );
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <UserOutlined />
          <span>{getTitleText()}</span>
        </div>
      }
      width={600}
      placement="right"
      onClose={handleCloseDrawer}
      open={visible}
      className="user-drawer"
      footer={renderFooter()}
    >
      {isView && user ? (
        <Descriptions bordered column={1} size="middle" labelStyle={{ width: "140px" }}>
          <Descriptions.Item label="用户名">{user.username}</Descriptions.Item>
          <Descriptions.Item label="姓名">{user.realname || '-'}</Descriptions.Item>
          <Descriptions.Item label="单位">{user.company || '-'}</Descriptions.Item>
          <Descriptions.Item label="职称">{user.position || '-'}</Descriptions.Item>
          <Descriptions.Item label="角色">
            {user.role?.name ? user.role.name : '-'}
          </Descriptions.Item>
          {isSuperAdmin && (
            <Descriptions.Item label="所属机构">
              {user.organization?.name || '-'}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="使用次数">
            {usedCount} / {maxAllowedCount === null ? 0 : (maxAllowedCount || 0)}
          </Descriptions.Item>
          <Descriptions.Item label="成就">{user.achievement || '-'}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {formatDate(user.created_at)}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {formatDate(user.updated_at)}
          </Descriptions.Item>
        </Descriptions>
      ) : (
        <Form
          form={form}
          layout="vertical"
          disabled={isView}
          onFinish={onSubmit}
          className="user-drawer-form"
        >
          {isSuperAdmin && !isSuperAdminUser && (
            <Form.Item
              name="organization_id"
              label="所属机构"
              tooltip="选择用户所属的机构"
              rules={[
                { required: true, message: '请选择机构' }
              ]}
            >
              <Select
                placeholder="请选择机构"
                disabled={!!isEdit}
                allowClear
                showSearch
                loading={organizationsLoading}
                filterOption={(input, option) =>
                  (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase())
                }
                options={organizations.map((org: Organization) => ({
                  label: `${org.name} (${org.code})`,
                  value: org.id
                }))}
                notFoundContent={organizationsLoading ? <Spin size="small" /> : <Empty description="暂无机构" />}
                value={selectedOrgId}
                onChange={handleOrganizationChange}
              />
            </Form.Item>
          )}
          
          {isOrgAdmin && (
            <Form.Item
              name="organization_id"
              hidden={true}
            >
              <Input type="hidden" />
            </Form.Item>
          )}

          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 4, message: '用户名不能少于4个字符' },
              { max: 50, message: '用户名不能超过50个字符' }
            ]}
          >
            <Input
              placeholder="请输入用户名"
              disabled={!!isEdit}
              autoComplete="new-username"
              maxLength={50}
            />
          </Form.Item>

          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: !isEdit, message: '请输入密码' },
                { min: 6, message: '密码不能少于6个字符' },
                { max: 50, message: '密码不能超过50个字符' },
                {
                  pattern: /^(?=.*[a-z])(?=.*[A-Z0-9@#$%^&+=]).+$/,
                  message: '密码需包含小写字母和大写字母/数字/特殊字符'
                }
              ]}
              style={{ flex: "50%" }}
            >
              <Input.Password placeholder={isEdit ? '不修改请留空' : '请输入密码'} autoComplete="new-password" />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认密码"
              dependencies={['password']}
              style={{ flex: "50%" }}
              rules={[
                {
                  required: !isEdit,
                  message: '请确认密码'
                },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const password = getFieldValue('password');

                    // 编辑模式下，如果未输入密码，则确认密码可以为空
                    if (isEdit && !password && !value) {
                      return Promise.resolve();
                    }
                    // 编辑模式下，如果输入了密码，则确认密码必填
                    if (isEdit && password && !value) {
                      return Promise.reject(new Error('请确认密码'));
                    }

                    // 两次密码必须一致
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password placeholder={isEdit ? '不修改请留空' : '请确认密码'} />
            </Form.Item>
          </div>

          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item
              name="realname"
              label="姓名"
              rules={[
                { required: true, message: '请输入姓名' },
                { max: 50, message: '姓名不能超过50个字符' }
              ]}
              style={{ flex: "50%" }}
            >
              <Input placeholder="请输入姓名" maxLength={50} />
            </Form.Item>

            {isSuperAdminUser ? (
              <Form.Item
                label="角色"
                style={{ flex: "50%" }}
              >
                <Input value="超级管理员" disabled={true} />
                <Form.Item name="role_id" hidden={true}>
                  <Input type="hidden" />
                </Form.Item>
              </Form.Item>
            ): isOrgAdminUser ? (
              <Form.Item
                label="角色"
                style={{ flex: "50%" }}
              >
                <Input value="管理员" disabled={true} />
                <Form.Item name="role_id" hidden={true}>
                  <Input type="hidden" />
                </Form.Item>
              </Form.Item>
            ): (
              <Form.Item
                name="role_id"
                label="角色"
                rules={[{ required: true, message: '请选择角色' }]}
                style={{ flex: "50%" }}
              >
                <Select 
                  placeholder="请选择角色"
                  loading={rolesLoading}
                  // disabled={isEdit && user?.role?.identifier === "admin"}
                >
                  {roles.map(role => (
                    <Option key={role.id} value={role.id}>
                      {role.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          </div>

          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item
              name="max_allowed_count"
              label="最大允许使用次数"
              style={{ flex: isEdit ? "50%" : "100%" }}
              rules={[{ required: true, message: '请输入最大允许使用次数' }]}
            >
              <InputNumber 
                min={0}
                max={9999999999} // 限制最大值为10位数字
                maxLength={10}
                placeholder="请输入最大允许使用次数" 
                style={{ width: '100%' }}
                // onChange={(e) => {
                //   const value = e.target.value;
                //   // 最多允许输入10位数字
                //   const limitedValue = value.length > 10 ? value.slice(0, 10) : value;
                //   // 当输入框为空时，设置为 0
                //   form.setFieldsValue({
                //     max_allowed_count: limitedValue === '' ? 0 : Number(limitedValue)
                //   });
                // }}
              />
            </Form.Item>

            {/* 编辑模式下显示已使用次数 */}
            {isEdit && (
              <Form.Item
                label="已使用次数"
                tooltip="用户已经使用的次数"
                style={{ flex: "50%" }}
              >
                <span>{usedCount}</span>
              </Form.Item>
            )}
          </div>

          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item
              name="company"
              label="单位"
              rules={[{ max: 50, message: '单位不能超过50个字符' }]}
              style={{ flex: "50%" }}
            >
              <Input placeholder="请输入单位" maxLength={50} />
            </Form.Item>

            <Form.Item
              name="position"
              label="职称"
              rules={[{ max: 50, message: '职称不能超过50个字符' }]}
              style={{ flex: "50%" }}
            >
              <Input placeholder="请输入职称" maxLength={50} />
            </Form.Item>
          </div>

          <Form.Item
            name="achievement"
            label="成就"
            extra="不超过500字"
            rules={[{ max: 500, message: '成就不能超过500个字符' }]}
          >
            <TextArea
              placeholder="请输入成就"
              rows={3}
              maxLength={500}
              showCount
            />
          </Form.Item>

        </Form>
      )}
    </Drawer>
  );
};

export default OrgUserDrawer; 