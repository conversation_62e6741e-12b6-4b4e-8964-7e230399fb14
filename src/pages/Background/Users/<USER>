.user-management-container {
  width: 100%;
}

.user-management-card {
  width: 100%;
  overflow: auto;
}

.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-management-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-icon {
  font-size: 24px;
  color: #1890ff;
}

.user-search-bar {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-loading {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

/* 表格隔行样式 */
.table-row-light {
  background-color: #ffffff;
}

.table-row-dark {
  background-color: #f5f7fa;
}

/* 操作按钮样式 */
.action-button {
  min-width: 70px;
  margin-right: 8px;
}

.view-button {
  background-color: #13c2c2;
  border-color: #13c2c2;
}

.view-button:hover {
  background-color: #36cfc9;
  border-color: #36cfc9;
}

.edit-button {
  background-color: #1677ff;
  border-color: #1677ff;
}

.edit-button:hover {
  background-color: #4096ff;
  border-color: #4096ff;
}

.delete-button {
  min-width: 70px;
}

/* 抽屉样式 */
.user-drawer .ant-drawer-body {
  padding: 24px;
}

.user-drawer-form {
  max-width: 100%;
}

/* 抽屉底部按钮样式 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 10px 16px;
  background: #fff;
}

/* 表格样式 */ 