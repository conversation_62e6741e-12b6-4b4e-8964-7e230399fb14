import { Organization, CreateOrganizationForm } from '@/types/Organization';
import { PaginationState, processPaginatedResponse } from '@/types/Page';
import { organizationApi, modelOrgApi } from '@/utils/api';
import {
  BankOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
  SafetyCertificateOutlined,
  CopyOutlined
} from '@ant-design/icons';
import {
  Button,
  Card,
  Descriptions,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Space,
  Spin,
  Switch,
  Table,
  Tooltip,
  Typography,
  Input as AntInput
} from 'antd';
import React, { useEffect, useState } from 'react';
import OrganizationDrawer, { DrawerType } from './OrganizationDrawer';
import './index.css';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

/**
 * 机构管理页面组件
 */
const OrganizationManagement: React.FC = () => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerType, setDrawerType] = useState<DrawerType>('create');
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null);
  const [searchText, setSearchText] = useState('');
  const [inputText, setInputText] = useState(''); // 用于存储输入框的临时值
  const [form] = Form.useForm();
  
  // 分页相关状态
  const [pagination, setPagination] = useState<PaginationState>({
    current: 1,
    pageSize: 10,
    total: 0
  });
  
  // 管理员账号信息弹窗状态
  const [adminInfoVisible, setAdminInfoVisible] = useState(false);
  const [adminInfo, setAdminInfo] = useState<{username: string, password: string} | null>(null);

  // 获取所有机构
  const fetchOrganizations = async (page = 1, size = 10, keyword = '') => {
    try {
      setLoading(true);
      const result = await organizationApi.getAllOrganizationsPaginated({
        page,
        size,
        keyword
      });
      
      // 使用通用处理函数处理分页数据
      const { data: organizationArray, total } = processPaginatedResponse<Organization>(result);
      
      setOrganizations(organizationArray);
      setPagination({
        ...pagination,
        current: page,
        pageSize: size,
        total: total
      });
      
      console.log('获取到机构列表:', organizationArray, '总数:', total);
    } catch (error) {
      console.error('获取机构列表失败:', error);
      setOrganizations([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: CreateOrganizationForm & { model_ids?: string[] }) => {
    try {
      setLoading(true);
      // 保存模型ID数组，以便创建后分配模型
      const modelIds = values.model_ids || [];
      
      // 构建机构基础数据，不包括model_ids
      const orgData: CreateOrganizationForm = {
        name: values.name,
        code: values.code,
        type: values.type,
        contact_person: values.contact_person || '',
        contact_phone: values.contact_phone || '',
        contact_email: values.contact_email || '',
        description: values.description,
        remarks: values.remarks,
        is_active: values.is_active !== undefined ? values.is_active : true,
        is_trial: values.is_trial !== undefined ? values.is_trial : false,
        limit_count: values.limit_count
      };

      if (editingOrganization && drawerType === 'edit') {
        // 更新现有机构
        await organizationApi.updateOrganization(editingOrganization.id, orgData);
        
        // 更新机构模型配置
        try {
          await modelOrgApi.arrangeOrgModels({
            organization_id: editingOrganization.id,
            list_model: modelIds
          });
          console.log('更新机构模型成功');
        } catch (error) {
          console.error('更新机构模型失败:', error);
          message.error('更新机构模型失败，请稍后重试');
        }
        
        message.success('机构已更新');
      } else {
        // 创建新机构
        const response = await organizationApi.createOrganization(orgData);
        
        if (response) {
          // 新创建的机构ID
          const newOrgId = response.id;
          
          // 如果有机构ID和模型选择，配置机构模型
          if (newOrgId && modelIds.length > 0) {
            try {
              await modelOrgApi.arrangeOrgModels({
                organization_id: newOrgId,
                list_model: modelIds
              });
              console.log('设置新机构模型成功');
            } catch (error) {
              console.error('设置新机构模型失败:', error);
              message.error('设置新机构模型失败，请稍后在编辑机构中重试');
            }
          }
          
          // 显示机构管理员账号信息
          setAdminInfo({
            username: response.username || '',
            password: response.password || ''
          });
          setAdminInfoVisible(true);
          
          message.success('机构已创建');
        }
      }
      
      // 关闭抽屉并刷新数据
      setDrawerVisible(false);
      fetchOrganizations();
    } catch (error) {
      console.error('保存机构失败:', error);
      message.error('保存机构失败，请检查表单并重试');
    } finally {
      setLoading(false);
    }
  };

  // 复制文本到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        message.success('复制成功');
      },
      () => {
        message.error('复制失败，请手动复制');
      }
    );
  };

  // 关闭管理员信息弹窗
  const handleAdminInfoClose = () => {
    setAdminInfoVisible(false);
    setAdminInfo(null);
  };

  // 删除机构
  const handleDelete = async (orgId: string) => {
    try {
      setLoading(true);
      console.log('删除机构ID:', orgId);
      const result = await organizationApi.deleteOrganization(orgId);
      message.success('机构已删除');
      fetchOrganizations();
    } catch (error) {
      console.error('删除机构失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 打开创建机构抽屉
  const showCreateDrawer = () => {
    setEditingOrganization(null);
    setDrawerType('create');
    setDrawerVisible(true);
  };

  // 打开编辑机构抽屉
  const showEditDrawer = (organization: Organization) => {
    setEditingOrganization(organization);
    setDrawerType('edit');
    setDrawerVisible(true);
  };

  // 打开权限配置抽屉
  const showPermissionDrawer = (organization: Organization) => {
    setEditingOrganization(organization);
    setDrawerType('permission');
    setDrawerVisible(true);
  };

  // 关闭抽屉
  const closeDrawer = () => {
    setDrawerVisible(false);
  };

  // 显示机构详情
  const showOrganizationDetail = async (orgId: string) => {
    try {
      // setLoading(true);
      const organizationDetail = await organizationApi.getOrganization(orgId);
      if (organizationDetail) {
        setEditingOrganization(organizationDetail);
        setDrawerType('view');
        setDrawerVisible(true);
        console.log('获取机构详情:', organizationDetail);
      } else {
        message.error('未找到机构详情');
      }
    } catch (error) {
      console.error('获取机构详情失败:', error);
    } finally {
      // setLoading(false);
    }
  };

  // 组件挂载时获取机构列表
  useEffect(() => {
    fetchOrganizations(1, pagination.pageSize, '');
  }, []);

  // 格式化时间
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 处理搜索
  const handleSearch = () => {
    setSearchText(inputText);
    // 重置到第一页，执行搜索
    fetchOrganizations(1, pagination.pageSize, inputText);
  };

  // 处理重置
  const handleReset = () => {
    setInputText('');
    setSearchText('');
    // 重置搜索条件，恢复到第一页
    fetchOrganizations(1, pagination.pageSize, '');
  };

  // 处理按键事件，按回车触发搜索
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    // 更新分页状态并重新获取数据
    fetchOrganizations(page, pageSize, searchText);
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => 
        (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '机构名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '机构编码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '机构类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '体验机构',
      dataIndex: 'is_trial',
      key: 'is_trial',
      width: 100,
      render: (isTrial: boolean) => (
        isTrial ? <Text type="success">是</Text> : <Text type="danger">否</Text>),
    },
    {
      title: '联系人',
      dataIndex: 'contact_person',
      key: 'contact_person',
    },
    {
      title: '最大使用次数',
      dataIndex: 'limit_count',
      key: 'limit_count',
      render: (limit_count?: number) => limit_count ? limit_count : 0,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        isActive ? <Text type="success">启用</Text> : <Text type="danger">禁用</Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => formatDate(text),
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      render: (_: any, record: Organization) => (
        <Space size="small">
          <Button 
            type="primary"
            size="small" 
            icon={<BankOutlined />}
            onClick={() => showOrganizationDetail(record.id)}
            className="action-button view-button"
          >
            查看
          </Button>
          <Button 
            type="primary"
            size="small" 
            icon={<EditOutlined />}
            onClick={() => showEditDrawer(record)}
            className="action-button edit-button"
          >
            编辑
          </Button>
          <Button 
            type="primary"
            size="small" 
            icon={<SafetyCertificateOutlined />}
            onClick={() => showPermissionDrawer(record)}
            className="action-button permission-button"
          >
            授权
          </Button>
          {/* <Popconfirm
            title="确定要删除此机构吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              danger
              size="small" 
              icon={<DeleteOutlined />}
              className="action-button delete-button"
            >
              删除
            </Button>
          </Popconfirm> */}
        </Space>
      ),
    },
  ];

  return (
    <div className="management-container">
      <Card
        title={
          <div className="org-management-header">
            <div className="org-management-title">
              <BankOutlined className="org-icon" />
              <Title level={4} style={{ margin: 0 }}>
                机构管理
              </Title>
            </div>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={showCreateDrawer}
            >
              新增机构
            </Button>
          </div>
        }
        className="org-management-card"
      >
        {/* 搜索框 */}
        <div className="org-search-bar">
          <Input
            placeholder="搜索机构名称、类型、编码、联系人"
            prefix={<SearchOutlined />}
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            style={{ width: 300 }}
            allowClear
          />
          <Space>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              搜索
            </Button>
            <Button
              onClick={handleReset}
            >
              重置
            </Button>
          </Space>
        </div>
        
        {/* 机构列表 */}
        {loading ? (
          <div className="org-loading">
            <Spin size="large" />
          </div>
        ) : (
          <Table
            dataSource={organizations}
            columns={columns}
            rowKey="id"
            rowClassName={(record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark'}
            pagination={{ 
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              onChange: (page, pageSize) => handleTableChange(page, pageSize || 10)
            }}
          />
        )}
      </Card>

      {/* 机构抽屉组件 */}
      <OrganizationDrawer
        visible={drawerVisible}
        onClose={closeDrawer}
        onSubmit={handleSubmit}
        loading={loading}
        organization={editingOrganization}
        type={drawerType}
      />

      {/* 管理员账号信息弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <SafetyCertificateOutlined />
            <span>机构管理员账号信息</span>
          </div>
        }
        open={adminInfoVisible}
        onCancel={handleAdminInfoClose}
        footer={[
          <Button key="close" type="primary" onClick={handleAdminInfoClose}>
            关闭
          </Button>
        ]}
        width={400}
      >
        <div className="admin-info-container">
          <p className="admin-info-tip">请妥善保存以下账号信息，此信息仅显示一次</p>
          
          <div className="admin-info-item">
            <Descriptions bordered column={1} size="small">
              <Descriptions.Item label="用户名">
                <div className="copy-field">
                  <span>{adminInfo?.username}</span>
                  <Button 
                    type="link" 
                    icon={<CopyOutlined />} 
                    onClick={() => copyToClipboard(adminInfo?.username || '')}
                  />
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="密码">
                <div className="copy-field">
                  <span>{adminInfo?.password}</span>
                  <Button 
                    type="link" 
                    icon={<CopyOutlined />} 
                    onClick={() => copyToClipboard(adminInfo?.password || '')}
                  />
                </div>
              </Descriptions.Item>
            </Descriptions>
          </div>
          
          <div className="admin-info-item">
            <Button 
              type="primary" 
              block
              onClick={() => copyToClipboard(`用户名：${adminInfo?.username}\n密码：${adminInfo?.password}`)}
            >
              <CopyOutlined /> 复制全部信息
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default OrganizationManagement; 