.org-management-container {
  width: 100%;
}

.org-management-card {
  width: 100%;
  overflow: auto;
}

.org-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.org-management-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.org-icon {
  font-size: 24px;
  color: #1890ff;
}

.org-search-bar {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.org-loading {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.menu-tree-container {
  padding: 8px 0;
}

.no-menu-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}

/* 表格隔行样式 */
.table-row-light {
  background-color: #ffffff;
}

.table-row-dark {
  background-color: #f5f7fa;
}

/* 操作按钮样式 */
.action-button {
  min-width: 70px;
  margin-right: 8px;
}

.view-button {
  background-color: #13c2c2;
  border-color: #13c2c2;
}

.view-button:hover {
  background-color: #36cfc9;
  border-color: #36cfc9;
}

.edit-button {
  background-color: #1677ff;
  border-color: #1677ff;
}

.edit-button:hover {
  background-color: #4096ff;
  border-color: #4096ff;
}

.permission-button {
  background-color: #722ed1;
  border-color: #722ed1;
}

.permission-button:hover {
  background-color: #9254de;
  border-color: #9254de;
}

.delete-button {
  min-width: 70px;
}

/* 抽屉样式 */
.organization-drawer .ant-drawer-body {
  padding: 24px;
}

.organization-drawer-form {
  max-width: 100%;
}

/* 抽屉底部按钮样式 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 10px 16px;
  background: #fff;
}

/* 管理员账号信息弹窗样式 */
.admin-info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.admin-info-tip {
  color: #ff4d4f;
  font-weight: 500;
  text-align: center;
  margin-bottom: 8px;
}

.admin-info-item {
  margin-bottom: 8px;
}

.copy-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copy-field .ant-btn {
  margin-left: 8px;
  padding: 0;
}

/* 表格样式 */ 