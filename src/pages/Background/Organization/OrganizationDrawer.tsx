import React, { useEffect, useState } from 'react';
import { 
  BankOutlined,
  ReloadOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons';
import {
  Button,
  Descriptions,
  Drawer,
  Form,
  Input,
  InputNumber,
  Select,
  Space,
  Switch,
  Typography,
  Tabs,
  Tree,
  Spin,
  message
} from 'antd';
import { CreateOrganizationForm, Organization } from '@/types/Organization';
import { MenuTreeNode } from '@/types/Menu';
import { menuApi, orgAdminApi, modelApi, modelOrgApi } from '@/utils/api';
import { ModelConfig } from '@/types/ModelConfig';
import { processPaginatedResponse } from '@/types/Page';
import './index.css';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

// 格式化日期显示
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export type DrawerType = 'create' | 'edit' | 'view' | 'permission';

interface OrganizationDrawerProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (values: CreateOrganizationForm & { model_ids?: string[] }) => Promise<void>;
  loading: boolean;
  organization: Organization | null;
  type: DrawerType;
}

export interface CreateOrganizationFormExtended extends CreateOrganizationForm {
  model_ids?: string[]; // 新增模型ID数组字段
}

/**
 * 机构管理抽屉组件
 */
const OrganizationDrawer: React.FC<OrganizationDrawerProps> = ({
  visible,
  onClose,
  onSubmit,
  loading,
  organization,
  type
}) => {
  const [form] = Form.useForm();
  const isView = type === 'view';
  const isCreate = type === 'create';
  const isEdit = type === 'edit';
  const isPermission = type === 'permission';
  const [activeTab, setActiveTab] = useState<string>('basic');

  // 菜单权限相关状态
  const [menuTree, setMenuTree] = useState<MenuTreeNode[]>([]);
  const [menuLoading, setMenuLoading] = useState(false);
  const [permissionLoading, setPermissionLoading] = useState(false);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  // 模型数据
  const [allModels, setAllModels] = useState<ModelConfig[]>([]);
  const [orgModels, setOrgModels] = useState<string[]>([]);
  const [modelsLoading, setModelsLoading] = useState(false);

  // 抽屉标题
  const getTitleText = () => {
    switch (type) {
      case 'create':
        return '新增机构';
      case 'edit':
        return '编辑机构';
      case 'view':
        return '机构详情';
      case 'permission':
        return `分配权限: ${organization?.name || ''}`;
      default:
        return '机构信息';
    }
  };

  // 组件挂载或visible/organization/type变化时重置表单
  useEffect(() => {
    if (visible) {
      if (isCreate) {
        form.resetFields();
        form.setFieldsValue({
          is_active: true,
          is_trial: false,
        });
        setActiveTab('basic');
        fetchAllModels(); // 获取所有模型
      } else if (organization && (isEdit || isView)) {
        form.setFieldsValue({
          name: organization.name,
          code: organization.code,
          type: organization.type,
          contact_person: organization.contact_person,
          contact_phone: organization.contact_phone,
          contact_email: organization.contact_email,
          description: organization.description,
          remarks: organization.remarks,
          is_active: organization.is_active,
          is_trial: organization.is_trial,
          limit_count: organization.limit_count || 0
        });
        setActiveTab('basic');
        fetchAllModels(); // 获取所有模型并加载机构模型
      } else if (isPermission && organization) {
        setActiveTab('permission');
        fetchMenuTree();
      }
    }
  }, [visible, organization, type, form]);

  // 获取菜单树
  const fetchMenuTree = async () => {
    if (!visible || !organization) return;
    
    try {
      setMenuLoading(true);
      
      // 获取机构菜单数据
      const orgMenus = await orgAdminApi.getOrgMenu(organization.id);
      const data = await menuApi.getMenuTree();
      const menuTreeArray = Array.isArray(data) ? data : [];
      setMenuTree(menuTreeArray);
      
      // 自动展开所有节点
      const allKeys: React.Key[] = [];
      const checkedMenuIds: React.Key[] = [];
      
      // 提取所有菜单ID和已分配的菜单ID
      const extractKeys = (items: MenuTreeNode[]) => {
        items.forEach(item => {
          allKeys.push(item.id);
          if (item.children && item.children.length > 0) {
            extractKeys(item.children);
          }
        });
      };
      
      extractKeys(menuTreeArray);
      setExpandedKeys(allKeys);
      
      // 设置已分配的菜单ID
      if (orgMenus && Array.isArray(orgMenus)) {
        const assignedMenuIds = orgMenus.map(menu => menu.menu_id).filter(id => id !== undefined);
        setCheckedKeys(assignedMenuIds);
      }
      
      console.log('获取到菜单树:', menuTreeArray);
      console.log('获取到机构菜单:', orgMenus);
    } catch (error) {
      console.error('获取菜单树失败:', error);
      setMenuTree([]);
    } finally {
      setMenuLoading(false);
    }
  };

  // 保存权限
  const handleSavePermission = async () => {
    if (!organization) return;
    
    try {
      setPermissionLoading(true);
      
      await orgAdminApi.SuperRoleMenus({
        organizationId: organization.id,
        menu_ids: checkedKeys.map(key => key.toString())
      });
      
      message.success('机构权限已分配');
      onClose();
    } catch (error) {
      console.error('分配权限失败:', error);
    } finally {
      setPermissionLoading(false);
    }
  };

  // 重置权限选择
  const handleResetPermission = () => {
    setCheckedKeys([]);
  };

  // 全选
  const handleSelectAll = () => {
    // 所有菜单ID
    const allMenuIds: React.Key[] = [];
    const collectIds = (items: MenuTreeNode[]) => {
      items.forEach(item => {
        allMenuIds.push(item.id);
        if (item.children && item.children.length > 0) {
          collectIds(item.children);
        }
      });
    };
    
    collectIds(menuTree);
    setCheckedKeys(allMenuIds);
    console.log('全选菜单权限:', allMenuIds);
  };

  // 树节点选择变化
  const onCheck = (checked: any) => {
    // console.log('选中的菜单:', checked);
    setCheckedKeys(checked);
  };

  // 展开/收起节点
  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  // 获取所有模型
  const fetchAllModels = async () => {
    try {
      setModelsLoading(true);
      const result = await modelApi.getAllModels();
      setAllModels(result || []);
      console.log('获取到所有模型:', result);
      
      // 如果是编辑或查看，获取机构的模型
      if ((isEdit || isView) && organization) {
        fetchOrgModels(organization.id);
      }
    } catch (error) {
      console.error('获取模型失败:', error);
      setAllModels([]);
    } finally {
      setModelsLoading(false);
    }
  };

  // 获取机构的模型
  const fetchOrgModels = async (organizationId: string) => {
    try {
      setModelsLoading(true);
      const response = await modelOrgApi.getOrgModels(organizationId);
      const modelIds = Array.isArray(response) 
        ? response.map(item => item.model?.id).filter(Boolean)
        : [];
      
      setOrgModels(modelIds);
      
      // 设置表单值
      form.setFieldValue('model_ids', modelIds);
      
      console.log('获取到机构模型:', response);
      console.log('提取的模型ID:', modelIds);
    } catch (error) {
      console.error('获取机构模型失败:', error);
      setOrgModels([]);
    } finally {
      setModelsLoading(false);
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (isView) {
      onClose();
      return;
    }

    if (isPermission) {
      await handleSavePermission();
      return;
    }

    try {
      const values = await form.validateFields();
      await onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (isPermission) {
      handleResetPermission();
      return;
    }

    if (isCreate) {
      form.resetFields();
      form.setFieldsValue({
        is_active: true,
        is_trial: false,
      });
      // 重置模型选择
      form.setFieldValue('model_ids', []);
    } else if (organization && isEdit) {
      form.setFieldsValue({
        name: organization.name,
        code: organization.code,
        type: organization.type,
        contact_person: organization.contact_person,
        contact_phone: organization.contact_phone,
        contact_email: organization.contact_email,
        description: organization.description,
        remarks: organization.remarks,
        is_active: organization.is_active,
        is_trial: organization.is_trial,
        limit_count: organization.limit_count || 0
      });
      
      // 重新获取机构模型配置
      if (organization.id) {
        fetchOrgModels(organization.id);
      }
    }
  };

  // 渲染底部按钮
  const renderFooter = () => {
    if (isView) {
      return (
        <div className="drawer-footer">
          <Button type="primary" onClick={onClose}>
            关闭
          </Button>
        </div>
      );
    }
    
    if (isPermission) {
      return (
        <div className="drawer-footer">
          <Button onClick={onClose}>取消</Button>
          <Button onClick={handleResetPermission}>重置</Button>
          <Button type="primary" onClick={handleSavePermission} loading={permissionLoading}>
            保存
          </Button>
        </div>
      );
    }
    
    return (
      <div className="drawer-footer">
        <Button onClick={onClose}>取消</Button>
        <Button icon={<ReloadOutlined />} onClick={handleReset}>重置</Button>
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          保存
        </Button>
      </div>
    );
  };

  // 准备树形数据
  const treeData = menuTree.map(menu => ({
    title: menu.name,
    key: menu.id,
    children: menu.children?.map(child => ({
      title: child.name,
      key: child.id,
    }))
  }));

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {isPermission ? <SafetyCertificateOutlined /> : <BankOutlined />}
          <span>{getTitleText()}</span>
        </div>
      }
      width={600}
      placement="right"
      onClose={onClose}
      open={visible}
      className="organization-drawer"
      footer={renderFooter()}
    >
      {isPermission ? (
        <Spin spinning={menuLoading}>
          {menuTree.length > 0 ? (
            <div className="menu-tree-container">
              <Title level={5} style={{ marginBottom: '16px' }}>请选择该机构管理员可访问的菜单</Title>
              <div style={{ marginBottom: '16px' }}>
                <Space>
                  <Button size="small" type="primary" onClick={handleSelectAll}>全选</Button>
                  <Button size="small" onClick={handleResetPermission}>全不选</Button>
                </Space>
              </div>
              <Tree
                checkable
                onCheck={onCheck}
                checkedKeys={checkedKeys}
                treeData={treeData}
                expandedKeys={expandedKeys}
                onExpand={onExpand}
                defaultExpandAll
              />
            </div>
          ) : (
            <div className="no-menu-data">
              暂无菜单数据
            </div>
          )}
        </Spin>
      ) : isView && organization ? (
        <Descriptions bordered column={1} size="middle" labelStyle={{ width: "140px" }}>
          <Descriptions.Item label="机构名称">{organization.name}</Descriptions.Item>
          <Descriptions.Item label="机构编码">{organization.code}</Descriptions.Item>
          <Descriptions.Item label="机构类型">{organization.type}</Descriptions.Item>
          <Descriptions.Item label="体验机构">
            {organization.is_trial ? '是' : '否'}
          </Descriptions.Item>
          <Descriptions.Item label="机构模型配置">
            {modelsLoading ? (
              <Spin size="small" />
            ) : orgModels.length > 0 ? (
              <div>
                {allModels
                  .filter(model => orgModels.includes(model.id))
                  .map(model => (
                    <div key={model.id} style={{ marginBottom: '4px' }}>
                      {model.name}
                    </div>
                  ))}
              </div>
            ) : (
              '未配置模型'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="联系人">{organization.contact_person || '-'}</Descriptions.Item>
          <Descriptions.Item label="联系电话">{organization.contact_phone || '-'}</Descriptions.Item>
          <Descriptions.Item label="联系邮箱">{organization.contact_email || '-'}</Descriptions.Item>
          <Descriptions.Item label="状态">
            {organization.is_active ? '启用' : '禁用'}
          </Descriptions.Item>
          <Descriptions.Item label="最大使用次数">
            {organization.limit_count ? organization.limit_count : 0}
          </Descriptions.Item>
          <Descriptions.Item label="机构描述">{organization.description || '-'}</Descriptions.Item>
          <Descriptions.Item label="备注">{organization.remarks || '-'}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {formatDate(organization.created_at)}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {formatDate(organization.updated_at)}
          </Descriptions.Item>
        </Descriptions>
      ) : (
        <Form
          form={form}
          layout="vertical"
          disabled={isView}
          onFinish={onSubmit}
          initialValues={{ is_active: true, is_trial: false }}
          className="organization-drawer-form"
        >
          <Form.Item
            name="name"
            label="机构名称"
            rules={[
              { required: true, message: '请输入机构名称' },
              { max: 100, message: '机构名称不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入机构名称" maxLength={100} />
          </Form.Item>
          
          <Form.Item
            name="code"
            label="机构编码"
            rules={[
              { required: true, message: '请输入机构编码' },
              { max: 50, message: '机构编码不能超过50个字符' },
              { pattern: /^[a-zA-Z0-9_]+$/, message: '机构编码只能包含字母、数字和下划线' }
            ]}
          >
            <Input placeholder="请输入机构编码" maxLength={50} />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="机构类型"
            rules={[
              { required: true, message: '请选择机构类型' },
              { max: 50, message: '机构类型不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入机构类型" maxLength={50} />
          </Form.Item>
          
          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item
              name="limit_count"
              label="最大使用次数"
              tooltip="设置该机构的最大使用次数限制"
              style={{ flex: "50%" }}
              rules={[
                { required: true, message: '请输入最大使用次数' },
                { type: 'number', message: '请输入有效的数字' },
              ]}
            >
              <InputNumber 
                min={0} 
                max={9999999999} // 限制最大值为10位数字
                maxLength={10}
                placeholder="请输入最大使用次数" 
                style={{ width: '100%' }}
              />
            </Form.Item> 

            <Form.Item
              name="is_trial"
              label="体验机构"
              valuePropName="checked"
              style={{ flex: "50%" }}
            >
              <Switch />
            </Form.Item>
          </div>

          <Form.Item
            name="model_ids"
            label="机构模型配置"
            tooltip="选择该机构可使用的模型"
          >
            <Select
              mode="multiple"
              placeholder="请选择机构可用模型"
              loading={modelsLoading}
              style={{ width: '100%' }}
              optionFilterProp="label"
            >
              {allModels.map(model => (
                <Select.Option key={model.id} value={model.id} label={model.name}>
                  {model.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item
              name="contact_person"
              label="联系人"
              rules={[
                { required: true, message: '请输入联系人' },
                { max: 50, message: '联系人不能超过50个字符' }
              ]}
              style={{ flex: "50%" }}
            >
              <Input placeholder="请输入联系人" maxLength={50} />
            </Form.Item>
            
            <Form.Item
              name="contact_phone"
              label="联系电话"
              rules={[
                { required: true, message: '请输入联系电话' },
                { max: 20, message: '联系电话不能超过20个字符' },
                { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码格式' }
              ]}
              style={{ flex: "50%" }}
            >
              <Input placeholder="请输入联系电话" maxLength={20} />
            </Form.Item>
          </div>
          
          <div style={{ display: "flex", gap: "16px" }}>
            <Form.Item
              name="contact_email"
              label="联系邮箱"
              rules={[
                // { required: true, message: '请输入联系邮箱' },
                { max: 100, message: '联系邮箱不能超过100个字符' },
                { type: 'email', message: '请输入正确的邮箱格式' }
              ]}
              style={{ flex: "50%" }}
            >
              <Input placeholder="请输入联系邮箱" maxLength={100} />
            </Form.Item>
            
            <Form.Item
              name="is_active"
              label="是否启用"
              valuePropName="checked"
              style={{ flex: "50%" }}
            >
              <Switch />
            </Form.Item>
          </div>
          
          <Form.Item
            name="description"
            label="机构描述"
            rules={[{ max: 500, message: '机构描述不能超过500个字符' }]}
          >
            <TextArea placeholder="请输入机构描述" rows={4} maxLength={500} showCount />
          </Form.Item>
          
          <Form.Item
            name="remarks"
            label="备注"
            rules={[{ max: 200, message: '备注不能超过200个字符' }]}
          >
            <TextArea placeholder="请输入备注信息" rows={2} maxLength={200} showCount />
          </Form.Item>
        </Form>
      )}
    </Drawer>
  );
};

export default OrganizationDrawer; 