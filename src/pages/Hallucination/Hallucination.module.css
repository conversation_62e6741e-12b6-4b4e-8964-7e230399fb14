/* 幻觉审查页面 */
.hallucinationPageContainer {
  height: 100vh;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  padding: 16px;
  background-color: #F0F4FF;
  overflow: hidden;
}

/* 历史记录面板 */
.HistoryPanel {
  width: 25%;
  height: 100%;
  z-index: 10;
  transform: translateX(-100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease-in-out;
  overflow: hidden;
}

.HistoryPanel.show {
  opacity: 1;
  transform: translateX(0);
  margin-right: 16px;
  transition: width 0.3s ease, margin-right 0.2s ease, transform 0.3s ease, opacity 0.2s ease;
}

.HistoryPanel.hide {
  width: 0px;
  opacity: 0;
  transform: translateX(-100%);
  margin-right: 0px;
  transition: width 0.3s ease, margin-right 0.2s ease, transform 0.3s ease, opacity 0.2s ease;
}

.hallucinationContainer {
  height: 100%;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 顶部标题 */
.hallucinationTitle {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  min-height: fit-content;
  margin-bottom: 16px;
}

.historyButton {
  width: 40px !important;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.titleLeft {
  display: flex;
  flex-direction: column;
  min-height: fit-content;
}

.titleText {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.titleDesc {
  font-size: 16px;
  color: #555;
}

/* 内容区域 */
.hallucinationContent {
  display: flex;
  flex-direction: row;
  gap: 16px;
  width: 100%;
  height: calc(100vh - 215px);
}

.hallucinationLeft {
  width: 25%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.hallucinationMain {
  width: 75%;
  height: 100%;
}

/* 左侧上传区域 */
.uploaderContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: fit-content;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.uploaderInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  min-height: fit-content;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.uploaderTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.uploaderDesc {
  font-size: 14px;
  color: #555;
}

.uploadDragger {
  margin-bottom: 16px;
}

.dragInner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 16px 0;
}

.dragText {
  font-size: 16px;
  /* color: #999; */
}

.dragIcon {
  font-size: 32px;
  font-weight: bold;
  padding: 10px;
  border-radius: 16px;
  color: #fff;
  background-color: #7C3AED;
}

/* 上传状态 */
.uploadingInfo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 16px 0px;
}

.uploadingText {
  margin-left: 8px;
  font-size: 14px;
  color: #999;
}

.uploadingTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

/* 已上传文件信息 */
.uploadedFileContainer {
  margin-top: 16px;
}

.uploadedFileItem {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 16px;
  border-radius: 8px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
}

.uploadedFileIcon {
  width: 40px;
  min-width: 40px;
  height: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #E0E7FF;
  font-size: 20px;
  font-weight: bold;
  color: #7C3AED;
}

.uploadedFileDetails {
  flex: 1;
  margin-left: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow: hidden;
}

.uploadedFileName {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

.uploadedFileInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.uploadedFileInfo span {
  font-size: 12px;
  color: #666;
  word-wrap: break-word;
}

.uploadTime {
  font-size: 12px;
  color: #999;
  word-wrap: break-word;
}

/* 右侧主内容区域 */
.mainContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.mainHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: fit-content;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 16px;
  margin-bottom: 20px;
}

.mainHeaderInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mainHeaderTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.mainHeaderDesc {
  font-size: 14px;
  color: #666;
}

.refreshButton {
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
}

.mainContent {
  flex: 1;
  overflow-y: auto;
}

/* 空状态 */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #A3A3A3;
  /* background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9; */
}

.emptyIcon {
  font-size: 48px;
}

.emptyTitle {
  font-size: 16px;
  font-weight: 600;
  /* color: #333; */
  margin-bottom: 8px;
}

.emptyDesc {
  font-size: 14px;
  color: #999;
}

/* 对比内容区域 */
.comparisonContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  height: 100%;
}

.comparisonCard {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e8e8e8;
  margin-bottom: 20px;
}

.comparisonCard .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.comparisonCard .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.comparisonContent {
  display: flex;
  flex-direction: row;
  gap: 16px;
  height: 100%;
}

.modifiedSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.contentBox {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow-y: auto;
}

.modifiedSectionActions {
  display: flex;
  justify-content: flex-end;
  height: 24px;
}

.downloadButton {
  background: #7C3AED;
  border-color: #7C3AED;
  color: white;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
}

.downloadButton:hover {
  background: #8B5CF6;
  border-color: #8B5CF6;
  color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hallucinationContent {
    flex-direction: column;
  }
  
  .hallucinationLeft,
  .hallucinationMain {
    width: 100%;
  }
  
  .hallucinationLeft {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .hallucinationContainer {
    padding: 16px;
  }
  
  .titleText {
    font-size: 24px;
  }
  
  .titleDesc {
    font-size: 14px;
  }
  
  .uploaderContainer,
  .mainContainer {
    padding: 16px;
  }
  
  .comparisonContent {
    flex-direction: column;
    gap: 16px;
  }
  
  .modifiedSection {
    flex: none;
  }
  
  .contentBox {
    padding: 12px;
  }
} 