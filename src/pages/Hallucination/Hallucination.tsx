import React, { useState, useEffect } from "react";
import { Button, message, Upload, Popconfirm, Space, Tooltip } from "antd";
import {
  FileTextOutlined,
  LoadingOutlined,
  DownloadOutlined,
  SearchOutlined,
  HistoryOutlined,
} from "@ant-design/icons";
import { hallucinationApi } from "@/utils/api_trace_hallucination";
import { 
  HallucinationUploadResponse,
  HallucinationVerifyResponse,
  UploadedHallucinationFile,
  CreateVerifyRequest,
} from "@/types/Hallucination";
import ToastMarkdownEditor from "@/components/MarkdownEditor";
import History from "./History";
import styles from "./Hallucination.module.css";

const Hallucination: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedHallucinationFile | null>(null);
  const [isDownloadingWord, setIsDownloadingWord] = useState(false);
  const [isDownloadingPdf, setIsDownloadingPdf] = useState(false);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  // 历史记录
  const [showHistory, setShowHistory] = useState(false);

  // 组件卸载时清理轮询
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  /** 历史记录选择 */
  const handleHistorySelect = async (selectedId: string) => {
    try {
      const result: HallucinationVerifyResponse = await hallucinationApi.getVerify(selectedId);
      console.log("选择的历史记录:", result);
      
      // 更新当前显示的文件信息
      const historyFileData: UploadedHallucinationFile = {
        verify_id: result.id,
        file_id: result.file.id,
        file_name: result.file.file_name,
        status: result.status,
        ai_report: result.ai_report || '',
        upload_time: new Date(result.created_at).toLocaleString(),
      };
      setUploadedFile(historyFileData);
      
      // 关闭历史
      setShowHistory(false);
      
    } catch (error) {
      console.error("获取历史记录详情失败:", error);
    }
  };

  /** 轮询获取幻觉审查结果 */
  const pollVerifyResult = (verifyId: string) => {
    console.log("开始轮询验证结果:", verifyId);
    
    const interval = setInterval(async () => {
      try {
        console.log("轮询检查状态:", verifyId);
        const result: HallucinationVerifyResponse = await hallucinationApi.getVerify(verifyId);
        console.log("轮询结果:", result);
        
        if (result.status === 'SUCCESS') {
          console.log("幻觉审查完成，停止轮询");
          clearInterval(interval);
          setPollingInterval(null);
          setIsUploading(false);
          
          // 更新上传文件状态
          setUploadedFile(prev => prev ? {
            ...prev,
            status: result.status,
            ai_report: result.ai_report,
          } : null);
          
          message.success("幻觉审查完成");
        } else if (result.status === 'FAILED' || result.status === 'CANCELLED') {
          console.log("幻觉审查失败或取消，停止轮询");
          clearInterval(interval);
          setPollingInterval(null);
          setIsUploading(false);
          
          setUploadedFile(prev => prev ? {
            ...prev,
            status: result.status,
            ai_report: result.ai_report || '审查失败',
          } : null);
          
          message.error(`幻觉审查${result.status === 'FAILED' ? '失败' : '已取消'}`);
        } else if (result.status === 'ONGOING') {
          console.log("幻觉审查进行中，继续轮询");
        }
      } catch (error) {
        console.error("轮询获取结果失败:", error);
        clearInterval(interval);
        setPollingInterval(null);
        setIsUploading(false);
      }
    }, 5000); // 每5秒轮询一次
    
    setPollingInterval(interval);
  };

  /** 处理幻觉审查文件上传 */
  const handleFileUpload = async (file: File, fileList: File[]) => {
    if (isUploading) {
      message.warning("文件上传中，请稍后再试");
      return;
    }

    if (file.name.length > 100) {
      message.error("文件名过长，请修改后重新上传（100字符以内）");
      return;
    }

    try {
      setIsUploading(true);
      setUploadedFile(null);
      
      // 清除之前的轮询
      if (pollingInterval) {
        clearInterval(pollingInterval);
        setPollingInterval(null);
      }
      
      const formData = new FormData();
      formData.append("file", file);
      console.log("准备上传幻觉审查文件:", file.name);

      // 1、上传
      const uploadRes: HallucinationUploadResponse = await hallucinationApi.uploadPaper(formData);
      console.log("文件上传成功响应:", uploadRes);
      
      // 2、创建记录
      const createVerifyData: CreateVerifyRequest = {
        file_id: uploadRes.id
      };
      const verifyRes: HallucinationVerifyResponse = await hallucinationApi.createVerify(createVerifyData);
      console.log("创建验证记录成功:", verifyRes);
      
      // 上传文件信息
      const uploadedFileData: UploadedHallucinationFile = {
        verify_id: verifyRes.id,
        file_id: uploadRes.id,
        file_name: uploadRes.file_name,
        status: verifyRes.status,
        ai_report: verifyRes.ai_report || '',
        upload_time: new Date().toLocaleString(),
      };
      setUploadedFile(uploadedFileData);
      
      // 3、轮询结果
      if (verifyRes.status === 'ONGOING' || verifyRes.status === 'NO-START') {
        pollVerifyResult(verifyRes.id);
      } else if (verifyRes.status === 'SUCCESS') {
        setIsUploading(false);
        message.success("幻觉审查完成");
      } else {
        setIsUploading(false);
        message.error("幻觉审查失败");
      }
      
    } catch (error: any) {
      console.error("幻觉审查处理失败:", error);
      setIsUploading(false);
      const errorMessage = error?.response?.data?.error || error?.message || '上传失败，请稍后重试';
      message.error(errorMessage);
    }
    
    return false; // 阻止默认上传行为
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 下载审查后的内容
  const handleDownload = async () => {
    if (!uploadedFile || !uploadedFile.verify_id) {
      message.warning("暂无可下载的内容");
      return;
    }
    if (isDownloadingWord) {
      message.warning("正在下载中，请稍后");
      return;
    }

    setIsDownloadingWord(true);
    try {
      message.loading({ content: '正在准备Word下载...', key: 'download' });
      // 过滤掉原文件名后缀
      const originalFilename = uploadedFile.file_name;
      const lastDotIndex = originalFilename.lastIndexOf('.');
      const baseName = lastDotIndex !== -1 ? originalFilename.substring(0, lastDotIndex) : originalFilename;
      const filename = `${baseName}_幻觉审查.docx`;
      await hallucinationApi.downloadVerify(uploadedFile.verify_id, 'doc', filename);
      message.success({ content: '下载成功', key: 'download' });
    } catch (error) {
      console.error('下载失败:', error);
      const errorMessage = error instanceof Error ? error.message : '下载失败，请稍后重试';
      message.error({ content: errorMessage, key: 'download' });
    } finally {
      setIsDownloadingWord(false);
    }
  };

  // 下载审查后的PDF内容
  const handleDownloadPdf = async () => {
    if (!uploadedFile || !uploadedFile.verify_id) {
      message.warning("暂无可下载的PDF内容");
      return;
    }
    if (isDownloadingPdf) {
      message.warning("正在下载中，请稍后");
      return;
    }

    setIsDownloadingPdf(true);
    try {
      message.loading({ content: '正在准备PDF下载...', key: 'downloadPdf' });
      // 过滤掉原文件名后缀
      const originalFilename = uploadedFile.file_name;
      const lastDotIndex = originalFilename.lastIndexOf('.');
      const baseName = lastDotIndex !== -1 ? originalFilename.substring(0, lastDotIndex) : originalFilename;
      const filename = `${baseName}_幻觉审查.pdf`;
      await hallucinationApi.downloadVerify(uploadedFile.verify_id, 'pdf', filename);
      message.success({ content: 'PDF下载成功', key: 'downloadPdf' });
    } catch (error) {
      console.error('PDF下载失败:', error);
      const errorMessage = error instanceof Error ? error.message : 'PDF下载失败，请稍后重试';
      message.error({ content: errorMessage, key: 'downloadPdf' });
    } finally {
      setIsDownloadingPdf(false);
    }
  };

  // 渲染审查结果内容
  const renderHallucinationResult = () => {
    if (isUploading) {
      return (
        <div className={styles.emptyContainer}>
          <LoadingOutlined className={styles.emptyIcon} style={{ fontSize: '48px' }}/>
          <div className={styles.emptyTitle}>正在进行幻觉审查...</div>
          <div className={styles.emptyDesc}>请稍候，这可能需要一些时间</div>
        </div>
      );
    }

    if (!uploadedFile) {
      return (
        <div className={styles.emptyContainer}>
          <div className={styles.emptyIcon}>
            <SearchOutlined />
          </div>
          <div className={styles.emptyTitle}>暂未上传文档</div>
          <div className={styles.emptyDesc}>请先上传文件进行幻觉内容审查</div>
        </div>
      );
    }

    if (uploadedFile.status === 'ONGOING' || uploadedFile.status === 'NO-START') {
      return (
        <div className={styles.emptyContainer}>
          <LoadingOutlined className={styles.emptyIcon} style={{ fontSize: '48px' }}/>
          <div className={styles.emptyTitle}>幻觉审查进行中...</div>
          <div className={styles.emptyDesc}>请稍候，正在分析文档内容</div>
        </div>
      );
    }

    if (uploadedFile.status === 'FAILED' || uploadedFile.status === 'CANCELLED') {
      return (
        <div className={styles.emptyContainer}>
          <div className={styles.emptyIcon}>
            <SearchOutlined />
          </div>
          <div className={styles.emptyTitle}>审查失败</div>
          <div className={styles.emptyDesc}>{uploadedFile.ai_report || '幻觉审查处理失败，请重新上传'}</div>
        </div>
      );
    }

    if (uploadedFile.status === 'SUCCESS' && uploadedFile.ai_report) {
      return (
        <div className={styles.comparisonContainer}>
          <div className={styles.comparisonContent}>
            <div className={styles.modifiedSection}>
              <div className={styles.contentBox}>
                <ToastMarkdownEditor
                  projectId={uploadedFile.verify_id || ""}
                  content={uploadedFile.ai_report}
                  isEditMode={false}
                  isStreaming={false}
                  // 源码编辑：流式显示时也传递编辑器模式（虽然是预览模式，但保持一致性）
                  editorType={'wysiwyg'}
                />
                {/* {uploadedFile.ai_report.split('\n').map((line: string, lineIndex: number) => {
                  return (
                    <p key={lineIndex}>{line}</p>
                  ); */}
                {/* })} */}
              </div>
              <div className={styles.modifiedSectionActions}>
                <Popconfirm
                  title="选择下载格式"
                  description={
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button 
                        icon={<DownloadOutlined />} 
                        onClick={handleDownload} 
                        style={{ width: '100%' }}
                        loading={isDownloadingWord}
                      >
                        下载Word文档
                      </Button>
                      <Button 
                        icon={<DownloadOutlined />} 
                        onClick={handleDownloadPdf}
                        style={{ width: '100%' }}
                        loading={isDownloadingPdf}
                      >
                        下载PDF文档
                      </Button>
                    </Space>
                  }
                  okButtonProps={{ style: { display: 'none' } }}
                  cancelButtonProps={{ style: { display: 'none' } }}
                  placement="topRight"
                >
                  <Button
                    className={styles.downloadButton}
                    icon={<DownloadOutlined />}
                    size="small"
                    disabled={isUploading}
                  >
                    下载审查结果
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.emptyContainer}>
        <div className={styles.emptyIcon}>
          <SearchOutlined />
        </div>
        <div className={styles.emptyTitle}>暂无审查结果</div>
        <div className={styles.emptyDesc}>请稍后再试</div>
      </div>
    );
  };

  return (
    <div className={styles.hallucinationPageContainer}>
      {/* 历史记录面板 */}
      <div className={`${styles.HistoryPanel} ${showHistory ? styles.show : styles.hide}`}>
        <History 
          onClose={() => setShowHistory(false)}
          shouldRefresh={showHistory}
          onSelect={handleHistorySelect}
        />
      </div>

      <div className={styles.hallucinationContainer}>
        {/* 页面标题 */}
        <div className={styles.hallucinationTitle}>
          <Tooltip title="查看历史记录">
            <Button
              type="default"
              className={styles.historyButton}
              icon={<HistoryOutlined />}
              onClick={() => setShowHistory(!showHistory)}
            />
          </Tooltip>
          <div className={styles.titleLeft}>
            <div className={styles.titleText}>幻觉审查工具</div>
            <div className={styles.titleDesc}>智能检测和修正文档中的幻觉内容</div>
          </div>
        </div>
        {/* 主内容 */}
        <div className={styles.hallucinationContent}>
          {/* 左侧：文件上传 */}
          <div className={styles.hallucinationLeft}>
            {/* 文件上传区域 */}
            <div className={styles.uploaderContainer}>
              <div className={styles.uploaderInfo}>
                <div className={styles.uploaderTitle}>上传文档</div>
                <div className={styles.uploaderDesc}>
                  支持PDF、Word文档、TXT、Markdown文件，最多1.5万字
                </div>
              </div>
              <div className={styles.uploadDragger}>
                <Upload.Dragger
                  accept=".pdf,.doc,.docx,.txt,.md,.markdown"
                  beforeUpload={(file, fileList) => {
                    console.log("幻觉审查文件=================", file);
                    console.log("文件列表=================", fileList);
                    return handleFileUpload(file, fileList);
                  }}
                  showUploadList={false}
                  multiple={false}
                  disabled={isUploading}
                >
                  <div className={styles.dragInner}>
                    <SearchOutlined className={styles.dragIcon} />
                    <div className={styles.dragText}>拖拽文件到此处或点击上传</div>
                  </div>
                </Upload.Dragger>
              </div>

              {/* 上传状态显示 */}
              {isUploading && (
                <div className={styles.uploadingInfo}>
                  <LoadingOutlined />
                  <div className={styles.uploadingText}>正在进行幻觉审查，请稍候...</div>
                </div>
              )}

              {/* 已上传文件信息 */}
              {uploadedFile && (
                <div className={styles.uploadedFileContainer}>
                  <div className={styles.uploadingTitle}>
                    文件信息
                  </div>
                  <div className={styles.uploadedFileItem}>
                    <div className={styles.uploadedFileIcon}>
                      <FileTextOutlined />
                    </div>
                    <div className={styles.uploadedFileDetails}>
                      <div className={styles.uploadedFileName}>
                        {uploadedFile.file_name}
                      </div>
                      <div className={styles.uploadedFileInfo}>
                        <span>状态: {uploadedFile.status === 'SUCCESS' ? '已完成' 
                                      : uploadedFile.status === 'ONGOING' ? '处理中' 
                                      : uploadedFile.status === 'FAILED' ? '失败' 
                                      : '等待中'}</span>
                      </div>
                      <div className={styles.uploadTime}>上传时间: {uploadedFile.upload_time}</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：审查结果*/}
          <div className={styles.hallucinationMain}>
            <div className={styles.mainContainer}>
              <div className={styles.mainHeader}>
                <div className={styles.mainHeaderInfo}>
                  <div className={styles.mainHeaderTitle}>审查结果</div>
                  <div className={styles.mainHeaderDesc}>查看幻觉审查结果</div>
                </div>
                {/* <Button
                    className={styles.refreshButton}
                    variant="filled"
                    color="primary"
                    icon={<SyncOutlined />}
                >
                    刷新
                </Button> */}
              </div>
              
              <div className={styles.mainContent}>
                {renderHallucinationResult()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hallucination; 