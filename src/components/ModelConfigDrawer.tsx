import { ModelConfig } from '@/types/ModelConfig';
import { modelUserApi } from '@/utils/api';
import { eventBus, EVENTS } from '@/utils/index'; // 引入事件总线
import { theme } from '@/config/theme';
import {
  ApiOutlined,
  EditOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  RobotOutlined,
  SmileOutlined,
  <PERSON>boltOutlined,
  BulbOutlined
} from '@ant-design/icons';
import {
  Button,
  Drawer,
  Form,
  Input,
  message,
  Spin,
  Typography,
  Select,
  Tabs,
  Card,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

// 模型类型枚举
enum ModelSceneType {
  PROJECT_CONFIG_NEED = "PROJECT_CONFIG_NEED", // AI课题名称优化、AI介绍、AI总结服务
  PAPER_GENERATE = "PAPER_GENERATE", // 生成大纲、生成报告的默认模型
  HANDLE_TEXT = "HANDLE_TEXT", // 扩写、续写、缩写、润色
  INSIGHT_MIND_MAP = "INSIGHT_MIND_MAP", // 脑图、重点服务的默认模型
  INSIGHT_GENERATE = "INSIGHT_GENERATE", // 生成灵感使用的模型
  INSIGHT_HANDLE_TEXT = "INSIGHT_HANDLE_TEXT", // 灵感里面的扩写、续写、缩写、润色、翻译服务的默认模型
}

// 图标映射
const ModelSceneIcons = {
  [ModelSceneType.PROJECT_CONFIG_NEED]: <FileTextOutlined />,
  [ModelSceneType.PAPER_GENERATE]: <EditOutlined />,
  [ModelSceneType.HANDLE_TEXT]: <ThunderboltOutlined />,
  [ModelSceneType.INSIGHT_MIND_MAP]: <ApiOutlined />,
  [ModelSceneType.INSIGHT_GENERATE]: <ExperimentOutlined />,
  [ModelSceneType.INSIGHT_HANDLE_TEXT]: <ThunderboltOutlined />
};

interface ModelConfigDrawerProps {
  visible: boolean;
  onClose: () => void;
  onModelsChange?: (models: ModelConfig[]) => void;
}

const ModelConfigDrawer: React.FC<ModelConfigDrawerProps> = ({ visible, onClose, onModelsChange }) => {
  const [modelConfigs, setModelConfigs] = useState<ModelConfig[]>([]);
  const [availableModels, setAvailableModels] = useState<ModelConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("HI-DocGen");
  const [saveLoading, setSaveLoading] = useState(false);
  
  // 用户默认模型设置
  const [userDefaultModels, setUserDefaultModels] = useState<{[key: string]: string}>({});
  
  // 获取用户可用的模型
  const fetchAvailableModels = async () => {
    try {
      const response = await modelUserApi.getUserAllModels();
      const models = Array.isArray(response) ? response : [];
      setAvailableModels(models);
      // console.log('获取到用户可用模型列表:', models);
    } catch (error) {
      console.error('获取用户可用模型失败:', error);
      setAvailableModels([]);
    }
  };
  
  // 获取所有模型配置
  const fetchModelConfigs = async () => {
    try {
      setLoading(true);
      const data = await modelUserApi.getUserDefaultModel();
      const configsArray = Array.isArray(data) ? data : [];
      setModelConfigs(configsArray);
      
      // 提取用户的默认模型配置
      const defaultSettings: {[key: string]: string} = {};
      configsArray.forEach((item) => {
        if (item.default_way && item.model?.id) {
          defaultSettings[item.default_way] = item.model.id;
        }
      });
      
      setUserDefaultModels(defaultSettings);
      // console.log('获取到用户模型配置:', defaultSettings);
      
      // 通知父组件模型列表更新
      onModelsChange?.(configsArray);
      // 事件总线通知其他组件
      eventBus.emit(EVENTS.MODEL_CONFIGS_UPDATED, configsArray);
    } catch (error) {
      console.error('获取模型配置失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // 处理模型选择变化
  const handleModelChange = async (modelId: string, sceneType: string) => {
    try {
      setUserDefaultModels(prev => ({
        ...prev,
        [sceneType]: modelId
      }));
      
      setSaveLoading(true);
      
      // 格式：当前选择的模型类型，当前选择的模型ID
      const dataToSave = {
        list_model: [
          {
            model_id: modelId,
            default_way: sceneType
          }
        ]
      };
      
      // 修改用户的默认模型
      await modelUserApi.updateUserDefaultModel(dataToSave);
      message.success('模型配置已更新');
      
      // 找到选择的模型完整信息
      const selectedModel = availableModels.find(m => m.id === modelId);
      
      if (selectedModel) {
        // 通过事件总线通知其他组件模型已更新
        eventBus.emit(EVENTS.MODEL_CONFIGS_UPDATED, {
          type: sceneType,
          modelId: modelId,
          modelName: selectedModel.name,
          modelInfo: {
            id: selectedModel.id,
            name: selectedModel.name
          }
        });
      }
    } catch (error) {
      console.error('更新模型配置失败:', error);
      // 失败时恢复之前的选择
      fetchModelConfigs();
    } finally {
      setSaveLoading(false);
    }
  };
  
  // Tab切换
  const handleTabChange = (activeKey: string) => {
    setActiveTab(activeKey);
  };
  
  useEffect(() => {
    if (visible) {
      setLoading(true);
      // 先获取可用模型列表，再获取用户默认模型配置
      fetchAvailableModels().then(() => {
        fetchModelConfigs();
      });
    }
  }, [visible]);
  
  // 关闭抽屉时，确保当前选择的模型被应用
  const handleClose = () => {
    // 在关闭前，确保PAPER_GENERATE类型的模型设置已经应用
    const paperGenerateModelId = userDefaultModels[ModelSceneType.PAPER_GENERATE];
    if (paperGenerateModelId) {
      const selectedModel = availableModels.find(m => m.id === paperGenerateModelId);
      if (selectedModel) {
        // 再次发送事件，确保模型选择被应用
        eventBus.emit(EVENTS.MODEL_CONFIGS_UPDATED, {
          type: ModelSceneType.PAPER_GENERATE,
          modelId: selectedModel.id,
          modelName: selectedModel.name,
          modelInfo: {
            id: selectedModel.id,
            name: selectedModel.name
          }
        });
      }
    }
    
    // 调用关闭回调
    onClose();
  };

  // 渲染模型选择卡片
  const renderModelCard = (title: string, description: string, sceneType: ModelSceneType) => {
    
    return (
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div style={{ 
              color: theme.primaryColor, 
              fontSize: '18px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: `${theme.primaryColor}15`,
            }}>
              {ModelSceneIcons[sceneType] || <RobotOutlined />}
            </div>
            <span>{title}</span>
          </div>
        }
        style={{ 
          marginBottom: 20, 
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
          border: '1px solid #f0f0f0'
        }}
        bodyStyle={{ padding: '16px 24px' }}
        hoverable
      >
        <div>          
          <Select
            style={{ 
              width: '100%',
              marginBottom: '8px'
            }}
            placeholder="请选择模型"
            value={userDefaultModels[sceneType]}
            onChange={(value) => handleModelChange(value, sceneType)}
            loading={saveLoading}
            dropdownStyle={{ borderRadius: '8px' }}
            suffixIcon={<SmileOutlined style={{ color: theme.primaryColor }} />}
          >
            {availableModels.map(model => (
              <Select.Option key={model.id} value={model.id}>
                {model.name}
              </Select.Option>
            ))}
          </Select>
          
          <Paragraph type="secondary" style={{ 
            fontSize: '13px',
            margin: '4px 0 0 0',
            color: 'rgba(0, 0, 0, 0.45)'
          }}>
            {description}
          </Paragraph>
        </div>
      </Card>
    );
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Button type="primary" shape="circle" icon={<ApiOutlined />} />
          <Title level={4} style={{ margin: 0 }}>
            模型配置
          </Title>
        </div>
      }
      placement="right"
      onClose={handleClose}
      open={visible}
      width={500}
    >
      {loading ? (
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center', 
          alignItems: 'center',
          padding: '80px 0',
          gap: '16px'
        }}>
          <Spin size="large" />
          <Text type="secondary">正在加载模型配置...</Text>
        </div>
      ) : (
        <>
          <div style={{ marginBottom: '24px' }}>
            <Paragraph style={{ 
              background: `${theme.primaryColor}10`,
              padding: '12px 16px',
              borderRadius: '8px',
              borderLeft: `4px solid ${theme.primaryColor}`,
              margin: 0
            }}>
              <Text strong>提示：</Text>
              <Text>您可以为不同的功能场景选择合适的默认模型，配置完成后将自动应用到相应功能。</Text>
            </Paragraph>
          </div>
          
          <Tabs 
            activeKey={activeTab} 
            onChange={handleTabChange} 
            centered 
            size="large"
            type="card"
            tabBarStyle={{
              marginBottom: '24px',
              borderBottom: 'none'
            }}
            tabBarGutter={16}
          >
            <TabPane 
              tab={
                <span style={{ padding: '0 12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <BulbOutlined />
                  Co-mpanion
                </span>
              } 
              key="Co-mpanion"
            >
              <div style={{ padding: '0 8px' }}>
                {renderModelCard(
                  "研究项目基本信息默认模型",
                  "AI课题名称优化、AI介绍、AI总结服务的默认模型",
                  ModelSceneType.PROJECT_CONFIG_NEED
                )}
                {renderModelCard(
                  "材料生成默认模型",
                  "大纲生成、正文生成、幻觉审查、AI去痕的默认模型",
                  ModelSceneType.PAPER_GENERATE
                )}
                {renderModelCard(
                  "文本工具默认模型",
                  "扩写、续写、缩写、润色服务的默认模型",
                  ModelSceneType.HANDLE_TEXT
                )}
              </div>
            </TabPane>
            <TabPane 
              tab={
                <span style={{ padding: '0 12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <ExperimentOutlined />
                  HI-InsightPlus
                </span>
              } 
              key="HI-Insight"
            >
              <div style={{ padding: '0 8px' }}>
                {renderModelCard(
                  "灵感卡片默认模型",
                  "简介、脑图、重点服务的默认模型",
                  ModelSceneType.INSIGHT_MIND_MAP
                )}
                {renderModelCard(
                  "灵感发现默认模型",
                  "生成灵感使用的模型",
                  ModelSceneType.INSIGHT_GENERATE
                )}
                {renderModelCard(
                  "文本工具默认模型",
                  "扩写、续写、缩写、润色、翻译服务的默认模型",
                  ModelSceneType.INSIGHT_HANDLE_TEXT
                )}
              </div>
            </TabPane>
          </Tabs>
        </>
      )}
    </Drawer>
  );
};

export default ModelConfigDrawer; 