import { ExclamationCircleFilled } from "@ant-design/icons";
import { Modal } from "antd";

const { confirm } = Modal;
const showLimitedModal = (
  type: "export" | "copy" | "words" | "usage" | "edit" | "save" | "flow",
  title?: "提示",
) => {
  let message = "";
  if (type === "export") message = "您当前使用的是体验账户，如需下载功能或获取完整权限，请联系管理员进行账户升级。感谢您的理解！";
  if (type === "copy") message = "内容复制功能仅对付费用户开放，升级账户即可获得完整权限，或联系管理员获取完整权限";
  if (type === "words") message = "字数已达上限，请联系管理员升级账户即可获得完整报告，或联系管理员获取完整权限";
  if (type === "usage") message = "生成次数已达上限，升级账户即可获得完整权限，或联系管理员获取完整权限";
  if (type === "edit") message = "编辑功能仅对付费用户开放，升级账户即可使用此功能，或联系管理员获取完整权限";
  if (type === "save") message = "保存功能仅对付费用户开放，升级账户即可使用此功能，或联系管理员获取完整权限";
  if (type === "flow") message = "您当前使用的是体验账户，如需幻觉审查或AI去痕功能，请联系管理员进行账户升级。感谢您的理解！";

  confirm({
    width: 500,
    title: title,
    icon: <ExclamationCircleFilled />,
    content: message,
    centered: true,
    // okText: "立即升级",
    cancelText: "关闭提示",
    onOk() {
      console.log("showLimitedModal ok");
    },
    onCancel() {
      console.log("showLimitedModal cancel");
    },
  });
};

export default showLimitedModal;
