import { theme } from '@/config/theme';
import { Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Text } = Typography;

interface LoadingDotsProps {
  text?: string;
  color?: string;
}

const LoadingDots: React.FC<LoadingDotsProps> = ({ 
  text = '加载中', 
  color = theme.primaryColor 
}) => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <Text style={{ color, fontSize: 16, fontWeight: 500 }}>
      {text}<span>{dots}</span>
    </Text>
  );
};

export default LoadingDots; 