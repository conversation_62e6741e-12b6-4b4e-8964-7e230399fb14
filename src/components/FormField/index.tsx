import React, { ReactNode } from 'react';
import { Form, Input, Select } from 'antd';
import { FormItemProps } from 'antd/es/form';
import './style.css';

const { Item } = Form;

export interface FormFieldProps extends FormItemProps {
  type?: 'input' | 'select' | 'textarea';
  placeholder?: string;
  maxLength?: number;
  rows?: number;
  children?: ReactNode;
  options?: { value: string; label: string }[];
  value?: string;
  onChange?: (value: string) => void;
}

/**
 * 自定义表单字段组件
 * 样式：输入框组有圆角边框，输入框没有边框，label在输入框边框上方
 */
const FormField: React.FC<FormFieldProps> = ({
  type = 'input',
  placeholder,
  maxLength,
  rows = 4,
  options,
  children,
  value,
  onChange,
  ...formItemProps
}) => {
  // 根据类型渲染不同的控件
  const renderControl = () => {
    if (children) {
      return children;
    }

    // 针对不同的表单控件类型，渲染不同的控件
    switch (type) {
      case 'select':
        return (
          <Select 
            placeholder={placeholder} 
            className="custom-form-field-control"
            options={options}
          />
        );
      case 'textarea':
        return (
          <Input.TextArea
            placeholder={placeholder}
            maxLength={maxLength}
            rows={rows}
            className="custom-form-field-control"
          />
        );
      case 'input':
      default:
        return (
          <Input 
            placeholder={placeholder} 
            maxLength={maxLength}
            className="custom-form-field-control"
          />
        );
    }
  };

  return (
    <Item className="custom-form-field" {...formItemProps}>
      <div className="custom-form-field-container">
        {renderControl()}
      </div>
    </Item>
  );
};

export default FormField; 