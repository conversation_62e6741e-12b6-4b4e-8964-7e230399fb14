/* 自定义表单字段容器样式 */
.custom-form-field .ant-form-item-label {
  position: relative;
  z-index: 2;
  padding: 0 8px !important;
}

.custom-form-field .ant-form-item-label > label {
  margin-left: 6px !important;
  color: #a5a5a5;
  background-color: #fff;
  padding: 0 4px;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
}

.custom-form-field .ant-form-item-label > label.ant-form-item-required::before {
  margin-right: 2px;
}

/* 输入框容器样式 */
.custom-form-field-container {
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  padding: 12px 16px 8px;
  position: relative;
  margin-top: -10px;
  background-color: #fff;
}

/* 输入控件样式 */
.custom-form-field-control {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin-top: 0;
  width: 100%;
  height: 32px;
}
.custom-form-field-control-text {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin-top: 0;
  width: 100%;
  height: auto;
}

.custom-form-field-control:focus,
.custom-form-field-control:hover {
  box-shadow: none !important;
}

/* Select 组件样式 */
.custom-form-field-container .ant-select .ant-select-selector {
  border: none !important;
  box-shadow: none !important;
  padding-left: 0 !important;
}

/* 文本域样式 */
.custom-form-field-container .ant-input-textarea .ant-input {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  resize: none;
}
.custom-form-field-container .ant-input-textarea-show-count .ant-input-data-count {
  bottom: -30px;
}

/* 错误状态样式 */
.custom-form-field.ant-form-item-has-error .custom-form-field-container {
  border-color: #ff4d4f;
}

/* 下拉箭头位置调整 */
.custom-form-field-container .ant-select-arrow {
  right: 0;
} 