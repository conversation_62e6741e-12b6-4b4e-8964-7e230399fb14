import { theme } from '@/config/theme';
import { Card, Typography } from 'antd';
import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

const { Title } = Typography;

interface MarkdownViewerProps {
  title: string;
  content: string;
  createdAt?: string;
}

const MarkdownViewer: React.FC<MarkdownViewerProps> = ({ title, content }) => {
  const markdownBodyRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (markdownBodyRef.current) {
      markdownBodyRef.current.scrollTop = markdownBodyRef.current.scrollHeight;
    }
  }, [content]); // 依赖 content prop，content 变化时 effect 会执行

  // 导出MD文件
  // const handleExportMd = () => {
  //   const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
  //   saveAs(blob, `${title.replace(/\s+/g, '-').toLowerCase()}.md`);
  // };

  // 复制内容到剪贴板
  // const handleCopy = () => {
  //   navigator.clipboard.writeText(content)
  //     .then(() => {
  //       message.success('内容已复制到剪贴板');
  //     })
  //     .catch(err => {
  //       console.error('复制失败:', err);
  //       message.error('复制失败，请手动选择内容后复制');
  //     });
  // };

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 24
      }}>
        <Title level={4} style={{ margin: 0 }}>{title}</Title>

        {/* <Space>
          <Button 
            icon={<DownloadOutlined />} 
            onClick={handleExportMd}
            type="primary"
            ghost
          >
            导出MD
          </Button>
          <Button 
            icon={<CopyOutlined />} 
            onClick={handleCopy}
          >
            复制内容
          </Button>
        </Space> */}
      </div>

      <Card
        variant="borderless"
        style={{
          borderRadius: theme.borderRadius,
          boxShadow: theme.boxShadow,
          background: '#fff'
        }}
      >
        <div className="markdown-body" style={{ maxHeight: '800px', overflowY: 'auto' }} ref={markdownBodyRef}>
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
          >
            {content}
          </ReactMarkdown>
        </div>
      </Card>
    </div>
  );
};

export default MarkdownViewer; 