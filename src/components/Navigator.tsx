import title_img from "@/assets/HI-DocGen.svg";
import side_img from "@/assets/side-icon.png";
import { theme } from "@/config/theme";
import { useAuth } from "@/contexts/AuthContext";
import { usePermission } from "@/contexts/PermissionContext"; // 权限上下文
import { routes } from "@/routes";
import "@/styles/Navigator.css";
import { ModelConfig } from "@/types/ModelConfig";
import { eventBus, EVENTS } from "@/utils/index"; // 引入事件总线
import { ApiOutlined, LogoutOutlined, UserOutlined, SettingOutlined, SearchOutlined, HomeOutlined } from "@ant-design/icons";
import { App, Button, Dropdown, Layout, Menu, Input, Breadcrumb, Typography } from "antd";
import Sider from "antd/es/layout/Sider";
import { Content, Footer } from "antd/es/layout/layout";
import React, { useCallback, useEffect, useState } from "react";
import { useLocation, useNavigate, useRoutes } from "react-router-dom";
import ModelConfigDrawer from "./ModelConfigDrawer";
import UserInfoDrawer from "./UserInfoDrawer";

const { Header } = Layout;
/** 侧边栏展开 */
const SIDE_BAR_COLLAPSED = "SIDE_BAR_COLLAPSED";
const side_bar_collapsed = JSON.parse(localStorage.getItem(SIDE_BAR_COLLAPSED) || "false");

const Navigator: React.FC = () => {
  const { message } = App.useApp();
  const location = useLocation();
  const element = useRoutes(routes);
  const navigate = useNavigate();
  // 判断是否在登录页
  const isLoginPage = location.pathname === "/login";
  const {
    isAuthenticated,
    logout,
    isTrial,
    isSuperAdmin,
    isOrgAdmin,
    userInfo,
    loading: authLoading,
  } = useAuth();
  const { authorizedMenus, loading: permissionLoading } = usePermission(); // 使用权限上下文中的菜单数据
  // 新增用户信息抽屉状态
  const [userDrawerVisible, setUserDrawerVisible] = useState(false);
  // 模型配置抽屉状态
  const [modelConfigDrawerVisible, setModelConfigDrawerVisible] = useState(false);
  const [firstItem, setFirstItem] = useState("");
  const [secondItem, setSecondItem] = useState("");
  // 侧边菜单展开
  const [sideBarCollapsed, setSideBarCollapsed] = useState(side_bar_collapsed);

  // 使用权限过滤后的菜单配置
  const menuConfig =
    !permissionLoading && authorizedMenus && authorizedMenus.length > 0 ? authorizedMenus : [];

  /**
   * 根据当前路径设置菜单状态
   * 处理特殊路由
   */
  useEffect(() => {
    // console.warn("useEffect location ===", location)
    console.warn("useEffect location.pathname ===", location.pathname);
    let path = location.pathname;

    // 特殊处理授权中间页，即使未登录也允许访问
    const pathKeys = path.split("/").filter(Boolean);
    let firstKey = pathKeys.length > 0 ? pathKeys[0] : "";
    let secondKey = pathKeys.length > 1 ? pathKeys[1] : "";
    console.log("pathKeys===", firstKey, secondKey);

    if (firstKey === "auth" && secondKey === "redirect") {
      console.log("授权中间页，允许未登录访问");
      return;
    }

    // 未登录
    if (!isAuthenticated) {
      navigate("/login");
      return;
    }

    // 根目录或login，跳转到college的默认菜单
    if ((path === "/" || path === "/college") || (path === "/login" && menuConfig.length > 0)) {
    // if (path === "/") {
      path = getDefaultMenuPath("college"); // 获取college菜单的默认路由
      console.log("set college path ===", path);
      navigate(path);
      return;
    }

    // 404
    if (!isValidPath(firstKey, secondKey)) {
      console.log("没有匹配的路由");
      navigate("/404");
      return;
    }
    // 路径无误，更新选中菜单项
    console.log("set item path ===", firstKey, secondKey);
    setFirstItem(firstKey);
    setSecondItem(secondKey);
  }, [location.pathname, isAuthenticated]);

  const isValidPath = (firstKey: string, secondKey: string) => {
    let isValid = false;

    // 如果菜单配置为空或加载中，对所有路径放行
    if (!menuConfig || menuConfig.length === 0) {
      return true;
    }

    // 特殊处理卡片详情页路由
    if (firstKey === "insight_plus" && secondKey === "card") {
      return true;
    }

    // 特殊处理未授权页面和错误页面
    if (firstKey === "403" || firstKey === "404" || firstKey === "error") {
      return true;
    }

    // 特殊处理授权中间页
    if (firstKey === "auth" && secondKey === "redirect") {
      return true;
    }

    const first_menu_index = menuConfig.findIndex(f => f.key === firstKey);
    if (first_menu_index === -1) {
      return false; // firstKey匹配失败
    }

    isValid = true;

    // secondKey不为空，需要检查
    if (secondKey) {
      const first_menu_item = menuConfig[first_menu_index];

      // 确保一级菜单有子菜单
      if (!first_menu_item.children || first_menu_item.children.length === 0) {
        return false;
      }

      const second_menu_index = first_menu_item.children.findIndex(f => f.key === secondKey);

      if (second_menu_index === -1) {
        return false; // secondKey匹配失败
      }
    }
    console.log("isValidPath ===", isValid);
    return isValid;
  };

  const onMenuSelect = (selectKey: string, isFirstLevel: boolean) => {
    const fromPath = location.pathname;
    let toPath = "";
    if (isFirstLevel) {
      toPath = getDefaultMenuPath(selectKey);
    } else {
      toPath = getDefaultMenuPath(firstItem, selectKey);
    }
    console.log(`点击菜单: 当前路径 ${fromPath}, 目标路径 ${toPath}`);
    navigate(toPath);
  };

  // 处理菜单跳转，防止匹配失败
  const getDefaultMenuPath = (firstKey = "", secondKey = "") => {
    const first_menu_index = menuConfig.findIndex(f => f.key === firstKey);
    // 无匹配菜单，返回第一个
    const first_menu_item = first_menu_index !== -1 ? menuConfig[first_menu_index] : menuConfig[0];
    console.log("first menu =====", first_menu_item);
    // 无子目录，直接返回一级路径
    if (!Array.isArray(first_menu_item.children) || first_menu_item.children.length == 0) {
      return first_menu_item.path;
    }
    const second_menu_index = first_menu_item.children.findIndex(f => f.key === secondKey);
    // 无匹配菜单，返回第一个
    const second_menu_item =
      second_menu_index !== -1
        ? first_menu_item.children[second_menu_index]
        : first_menu_item.children[0];
    // 返回完整路径
    return first_menu_item.path + second_menu_item.path;
  };

  // 处理登出
  const handleLogout = () => {
    logout();
    message.success("已退出登录");
    // 登出后立即导航到登录页面，并使用replace模式避免返回
    navigate("/login", { replace: true });
  };

  // 显示用户信息抽屉
  const showUserDrawer = () => {
    setUserDrawerVisible(true);
  };

  // 显示模型配置抽屉
  const showModelConfigDrawer = () => {
    setModelConfigDrawerVisible(true);
  };

  // 获取当前模块的名称
  const getCurrentModuleName = () => {
    // 查找当前选中的一级菜单
    const currentFirstMenu = menuConfig.find(f => f.key === firstItem);
    if (!currentFirstMenu) return "";

    // 如果有二级菜单，查找当前选中的二级菜单
    if (secondItem && currentFirstMenu.children) {
      const currentSecondMenu = currentFirstMenu.children.find(s => s.key === secondItem);
      if (currentSecondMenu) return currentSecondMenu.title as string;
    }
    
    // 如果没有二级菜单或未找到当前二级菜单，返回一级菜单的标题
    return currentFirstMenu.title as string;
  };

  // 用户菜单项
  const userMenuItems = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: "用户信息",
      onClick: showUserDrawer,
    },
    // {
    //   key: "modelConfig",
    //   icon: <ApiOutlined />,
    //   label: "模型配置",
    //   onClick: showModelConfigDrawer,
    // },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "退出登录",
      onClick: handleLogout,
    },
  ];

  const renderNavBar = useCallback(() => {
    const first_menu = menuConfig.map(item => {
      return {
        key: item.key,
        label: item.title,
        icon: item.icon,
      };
    });
    return first_menu.length > 0 ? (
      <Menu
        className="nav-menu"
        mode="horizontal"
        selectedKeys={[firstItem]}
        items={first_menu}
        onSelect={item => {
          console.log("firstItem select========", item);
          onMenuSelect(item.key, true);
        }}
      />
    ) : null;
  }, [menuConfig, firstItem]);

  const onCollapsed = () => {
    setSideBarCollapsed(!sideBarCollapsed);
    localStorage.setItem(SIDE_BAR_COLLAPSED, `${!sideBarCollapsed}`);
  };

  const renderSideBar = useCallback(() => {
    // 查找当前选中的一级菜单
    const currentFirstMenu = menuConfig.find(f => f.key === firstItem);

    // 如果没有找到对应的一级菜单，或者一级菜单没有子菜单，则不显示侧边栏
    if (!currentFirstMenu || !currentFirstMenu.children || currentFirstMenu.children.length === 0) {
      return null;
    }

    // 提取子菜单
    const children = currentFirstMenu.children;

    // 转换为Antd Menu需要的格式
    const second_menu = children.map(item => {
      return {
        key: item.key,
        label: item.label || item.title,
        icon: item.icon,
      };
    });

    return second_menu.length > 0 ? (
      <Sider width={sideBarCollapsed ? 60 : 200} className="side-container">
        <div className="title">
          <div style={{ cursor: 'pointer', color: '#A3A3A3', opacity: sideBarCollapsed ? 0 : 1 }} onClick={() => navigate('/college/home')}>
            高校AI智跑助手<br/>
            <small>AI智伴，伴您每一步</small>
          </div>
          {/* <img className="logo" src={title_img} style={{ opacity: sideBarCollapsed ? 0 : 1 }} /> */}
          <img
            className="icon"
            style={{ rotate: sideBarCollapsed ? "180deg" : "0deg" }}
            src={side_img}
            onClick={onCollapsed}
          />
        </div>
        <Menu
          className="side-menu"
          mode="inline"
          inlineCollapsed={sideBarCollapsed}
          selectedKeys={[secondItem]}
          items={second_menu}
          onSelect={item => {
            console.log("secondItem select========", item);
            onMenuSelect(item.key, false);
          }}
        />
      </Sider>
    ) : null;
  }, [menuConfig, firstItem, secondItem, sideBarCollapsed]);

  return (
    <Layout className="layout-container"
    >
      {!isLoginPage && (
        <Header className="header-container" >
          {/* 左侧Logo */}
          <div 
            className="app-logo" 
            style={{ color: theme.primaryColor, cursor: 'pointer' }} 
            onClick={() => navigate('/college/home')}
          >
            高校AI助手
          </div>

          {/* 中间导航菜单 */}
          {/* {renderNavBar()} */}

          {/* 模块名称、面包屑、搜索 */}
          <div className="header-middle">
            <div className="module-navigation">
              <Typography.Title level={4} style={{ margin: 0 }}>
                {getCurrentModuleName()}
              </Typography.Title>
              <Breadcrumb 
                items={[
                  {
                    title: <><HomeOutlined /> 首页</>,
                    href: '/college/home',
                    onClick: (e) => {
                      e.preventDefault();
                      navigate('/college/home');
                    }
                  },
                  {
                    title: getCurrentModuleName()
                  }
                ]}
              />
            </div>
            
            {/* 全局搜索 */}
            {/* <div className="global-search">
              <Input 
                placeholder="搜索功能、聊天记录、作业..." 
                prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
                style={{ width: 400, borderRadius: 20 }}
                onPressEnter={(e) => {
                  const value = (e.target as HTMLInputElement).value;
                  if (value.trim()) {
                    message.info(`搜索: ${value}`);
                  }
                }}
              />
            </div> */}
          </div>

          {/*用户菜单 */}
          <div className="user-area">
            {/* 使用次数 */}
            {userInfo && userInfo.used_count !== undefined && userInfo.max_allowed_count !== undefined && (
              <div className="usage-counter">
                使用次数<br/>
                <span className={userInfo.used_count >= userInfo.max_allowed_count ? 'usage-count-exceeded' : 'usage-count-normal'}>
                  { userInfo.max_allowed_count === null ? `${userInfo.used_count} / 无限次` : `${userInfo.used_count} / ${userInfo.max_allowed_count}` }
                </span>
              </div>
            )}
            {/* 后台设置 */}
            {(isSuperAdmin || isOrgAdmin) && (
              <Button 
                shape="circle" 
                icon={<SettingOutlined />} 
                onClick={() => {
                  const defaultBackgroundPath = getDefaultMenuPath("background");
                  navigate(defaultBackgroundPath);
                }} 
                title="后台管理"
              />
            )}
            {/* 个人中心 */}
            {isAuthenticated && (
              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <Button type="primary" shape="circle" icon={<UserOutlined />} />
              </Dropdown>
            )}
          </div>

          {/* 用户信息抽屉 */}
          <UserInfoDrawer visible={userDrawerVisible} onClose={() => setUserDrawerVisible(false)} />

          {/* 模型配置抽屉 */}
          <ModelConfigDrawer
            visible={modelConfigDrawerVisible}
            onClose={() => setModelConfigDrawerVisible(false)}
            onModelsChange={(models: ModelConfig[]) => {
              // 事件总线模型配置更新
              // console.log("导航栏模型配置更新:", models);
              eventBus.emit(EVENTS.MODEL_CONFIGS_UPDATED, models);
            }}
          />
        </Header>
      )}
      <Layout>
        {!isLoginPage && renderSideBar()}
        <Layout>
          <Content className={`main-content ${isLoginPage ? 'main-content-login' : ''}`} >
            {element}
          </Content>
        </Layout>
      </Layout>
      {!isLoginPage && (
        <Footer className="app-footer" >
          College-Agent ©{new Date().getFullYear()} 高校AI助手
        </Footer>
      )}
    </Layout>
  );
};

export default Navigator;
