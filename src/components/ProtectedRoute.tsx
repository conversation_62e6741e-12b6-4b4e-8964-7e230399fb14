import { useAuth } from '@/contexts/AuthContext';
import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { usePermission } from '@/contexts/PermissionContext';
import { message } from 'antd';
import LoadingDots from '@/components/LoadingDots';
import ComingSoon from '@/pages/ComingSoon';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  requiredPermission?: string;
  menuPath?: string; // 添加菜单路径属性用于权限检查
}

// 受保护的路由组件，用于验证用户是否已登录和权限
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireAdmin = false,
  requiredPermission,
  menuPath
}) => {
  const { isAuthenticated, userInfo, isSuperAdmin, updateUserInfo } = useAuth();
  const { hasMenuPermission, loading: permissionLoading } = usePermission();
  const location = useLocation();

  // 路由进入时确保获取用户信息
  useEffect(() => {
    if (isAuthenticated && !userInfo) {
      updateUserInfo();
    }
  }, [isAuthenticated, userInfo]);

  // 如果权限组件仍在加载中
  if (permissionLoading || (isAuthenticated && !userInfo)) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <LoadingDots />
      </div>
    );
  }

  // 未登录时重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 需要特定权限但用户没有该权限时
  if (requiredPermission && userInfo?.permissions && 
      !userInfo.permissions.includes(requiredPermission)) {
    // 未授权访问页面
    return <ComingSoon 
      title="未授权访问"
      subTitle="很抱歉，您没有权限访问此页面" 
      status="403"
    />;
  }

  // 如果不是超级管理员且指定了menuPath，检查是否有该菜单权限
  if (!isSuperAdmin && menuPath && !hasMenuPermission(menuPath)) {
    // 未授权访问页面
    return <ComingSoon 
      title="未授权访问"
      subTitle="很抱歉，您没有权限访问此页面" 
      status="403"
    />;
  }

  // 已登录且符合权限要求，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute; 