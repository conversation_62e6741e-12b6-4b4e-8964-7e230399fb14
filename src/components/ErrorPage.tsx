import React from 'react';
import { Button, Result } from 'antd';
import { useNavigate } from 'react-router-dom';

interface ErrorPageProps {
  status?: '400' | '403' | '404' | '500' | 'error';
  title?: string;
  subTitle?: string;
}

/**
 * 错误页面组件
 * 用于显示HTTP错误状态或应用错误
 */
const ErrorPage: React.FC<ErrorPageProps> = ({
  status = 'error',
  title,
  subTitle,
}) => {
  const navigate = useNavigate();
  
  // 根据状态码设置默认标题和描述
  const getDefaultText = () => {
    switch (status) {
      case '400':
        return {
          title: '请求错误',
          subTitle: '请求参数错误，请检查后重试',
        };
      case '403':
        return {
          title: '禁止访问',
          subTitle: '您没有权限访问此页面',
        };
      case '404':
        return {
          title: '页面不存在',
          subTitle: '您访问的页面不存在',
        };
      case '500':
        return {
          title: '服务器错误',
          subTitle: '服务器内部错误，请稍后重试',
        };
      default:
        return {
          title: '发生错误',
          subTitle: '应用程序遇到了问题',
        };
    }
  };

  const defaultText = getDefaultText();
  const finalTitle = title || defaultText.title;
  const finalSubTitle = subTitle || defaultText.subTitle;

  return (
    <div style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <Result
        status={status as any}
        title={finalTitle}
        subTitle={finalSubTitle}
        extra={[
          <Button type="primary" key="home" onClick={() => navigate('/')}>
            返回首页
          </Button>,
          <Button key="refresh" onClick={() => window.location.reload()}>
            刷新页面
          </Button>
        ]}
      />
    </div>
  );
};

export default ErrorPage; 