import { DeleteOutlined, FileTextOutlined, LoadingOutlined, SafetyOutlined } from '@ant-design/icons';
import { Modal, Upload } from 'antd';
import React, { ReactElement, useState } from 'react';
import styles from './index.module.css';

interface FileUploadDraggerProps {
  /** 上传区域标题 */
  title: string;
  /** 上传区域描述 */
  description: string | React.ReactNode;
  /** 接受的文件类型 */
  accept: string;
  /** 文件上传处理函数 */
  onUpload: (file: File, fileList: File[]) => boolean | void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否正在上传 */
  isUploading?: boolean;
  /** 上传中的提示文字 */
  uploadingText?: string;
  /** 是否支持多文件上传 */
  multiple?: boolean;
  /** 是否显示已上传的文件信息 */
  showUploadedFile?: boolean;
  /** 已上传的文件信息 */
  uploadedFile?: {name: string, size: number} | null;
  /** 文件删除回调 */
  onFileDelete?: () => void;
  /** 是否显示文件替换确认对话框 */
  showReplaceConfirm?: boolean;
  /** 单文件上传提示文字 */
  singleFileHint?: string;
  /** 是否禁用删除功能 */
  disableDelete?: boolean;
  children?: ReactElement;
}

/**
 * 统一的文件上传拖拽组件
 * 基于"降低AI"页面的视觉设计，提供统一的上传体验
 */
const FileUploadDragger: React.FC<FileUploadDraggerProps> = ({
  title,
  description,
  accept,
  onUpload,
  disabled = false,
  isUploading = false,
  uploadingText = "正在上传文件，请稍候...",
  multiple = false,
  showUploadedFile = false,
  uploadedFile = null,
  onFileDelete,
  showReplaceConfirm = false,
  singleFileHint,
  disableDelete = false,
  children = null,
}) => {
  const [showReplaceModal, setShowReplaceModal] = useState(false);
  const [pendingFile, setPendingFile] = useState<File | null>(null);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理文件上传前的逻辑
  const handleBeforeUpload = (file: File, fileList: File[]) => {
    // 如果已有文件且需要显示替换确认对话框
    if (showReplaceConfirm && uploadedFile && !multiple) {
      setPendingFile(file);
      setShowReplaceModal(true);
      return false; // 阻止上传，等待用户确认
    }

    // 直接调用原始的上传处理函数
    return onUpload(file, fileList);
  };

  // 确认替换文件
  const handleConfirmReplace = () => {
    if (pendingFile) {
      onUpload(pendingFile, [pendingFile]);
      setPendingFile(null);
    }
    setShowReplaceModal(false);
  };

  // 取消替换文件
  const handleCancelReplace = () => {
    setPendingFile(null);
    setShowReplaceModal(false);
  };

  // 处理文件删除
  const handleDeleteFile = () => {
    if (disableDelete) {
      return; // 禁用状态下不执行任何操作
    }

    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个文件吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        onFileDelete?.();
      },
    });
  };
  return (
    <div className={styles.uploaderContainer}>
      {/* 上传区域信息 */}
      <div className={styles.uploaderInfo}>
        <div className={styles.uploaderTitle}>{title}</div>
        <div className={styles.uploaderDesc}>
          {description}
          {singleFileHint && (
            <>
              <br />
              <span className={styles.singleFileHint}>{singleFileHint}</span>
            </>
          )}
        </div>
      </div>

      {/* 文件拖拽上传区域 */}
      <div className={styles.uploadDragger}>
        <Upload.Dragger
          accept={accept}
          beforeUpload={handleBeforeUpload}
          showUploadList={false}
          multiple={multiple}
          disabled={disabled || isUploading}
        >
          <div className={styles.dragInner}>
            <SafetyOutlined className={styles.dragIcon} />
            <div className={styles.dragText}>拖拽文件到此处或点击上传</div>
          </div>
        </Upload.Dragger>
      </div>

      {/* 上传状态显示 */}
      {isUploading && (
        <div className={styles.uploadingInfo}>
          <LoadingOutlined />
          <div className={styles.uploadingText}>{uploadingText}</div>
        </div>
      )}

      {/* 已上传文件显示 */}
      {showUploadedFile && uploadedFile && (
        <div className={styles.uploadedFileContainer}>
          <div className={styles.uploadedFileItem}>
            <div className={styles.uploadedFileIcon}>
              <FileTextOutlined />
            </div>
            <div className={styles.uploadedFileDetails}>
              <div className={styles.uploadedFileName}>{uploadedFile.name}</div>
              <div className={styles.uploadedFileSize}>
                {formatFileSize(uploadedFile.size)}
              </div>
            </div>
            {onFileDelete && (
              <div className={styles.uploadedFileActions}>
                <DeleteOutlined
                  className={`${styles.deleteIcon} ${disableDelete ? styles.deleteIconDisabled : ''}`}
                  onClick={handleDeleteFile}
                  title={disableDelete ? "生成过程中无法删除文件" : "删除文件"}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* 文件替换确认对话框 */}
      <Modal
        title="确认替换文件"
        open={showReplaceModal}
        onOk={handleConfirmReplace}
        onCancel={handleCancelReplace}
        okText="确定替换"
        cancelText="取消"
      >
        <p>您已经上传了一个文件，新文件将替换当前文件。确定要继续吗？</p>
        {pendingFile && (
          <p>新文件：<strong>{pendingFile.name}</strong></p>
        )}
      </Modal>
      {children}
    </div>
  );
};

export default FileUploadDragger;
