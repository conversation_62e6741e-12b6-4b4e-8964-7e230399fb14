/**
 * LaTeX公式插件
 * 基于 KaTeX 实现数学公式渲染
 */
import katex from 'katex';
import 'katex/dist/katex.min.css';

// LaTeX公式：简化的数学公式插件，先去掉工具栏按钮避免冲突
const mathPlugin = () => {
  console.log('LaTeX公式：数学公式插件已加载');

  // LaTeX公式：HTML渲染器
  const toHTMLRenderers = {
    // 行内公式渲染 $...$
    mathInline: (node: any) => {
      const formula = node.literal;
      try {
        const rendered = katex.renderToString(formula, {
          displayMode: false,
          throwOnError: false
        });
        return {
          type: 'openTag',
          tagName: 'span',
          classNames: ['math-inline'],
          outerNewLine: false,
          innerHTML: rendered
        };
      } catch (error) {
        console.warn('LaTeX公式：行内公式渲染错误', error);
        return {
          type: 'text',
          content: `$${formula}$`
        };
      }
    },

    // 块级公式渲染 $$...$$
    mathBlock: (node: any) => {
      const formula = node.literal;
      try {
        const rendered = katex.renderToString(formula, {
          displayMode: true,
          throwOnError: false
        });
        return {
          type: 'openTag',
          tagName: 'div',
          classNames: ['math-block'],
          outerNewLine: true,
          innerHTML: rendered
        };
      } catch (error) {
        console.warn('LaTeX公式：块级公式渲染错误', error);
        return {
          type: 'text',
          content: `$$${formula}$$`
        };
      }
    }
  };

  return {
    toHTMLRenderers
  };
};

export default mathPlugin; 