/**
 * 处理预览模式下的HTML，将p标签内的br标签转换为独立的p标签
 * 使预览模式的换行表现与编辑模式一致
 * 
 * 特殊处理：
 * - 保持原有的data-nodeid属性一致性
 * - 拆分后的第一个<p>保留原data-nodeid
 * - 后续<p>在原data-nodeid基础上添加小编号（如：1000 -> 1000, 1000-1, 1000-2）
 * - 复制所有其他data-*属性到新段落
 */
export const splitBrIntoP = (container: Element, debug: boolean = false) => {
    try {
        const paragraphs = container.querySelectorAll('p');

        // 使用Array.from转换为数组，避免在遍历时修改DOM导致的问题
        Array.from(paragraphs).forEach(p => {
            // 检查段落是否包含br标签
            const brElements = p.querySelectorAll('br');
            if (brElements.length === 0) return;

            // 获取段落的所有子节点
            const childNodes = Array.from(p.childNodes);

            // 如果只有一个br且是最后一个节点，可能是编辑器自动添加的，跳过处理
            if (brElements.length === 1 &&
                childNodes.length > 0 &&
                childNodes[childNodes.length - 1] === brElements[0]) {
                // 检查br前是否有实际内容
                const hasContentBeforeBr = childNodes.slice(0, -1).some(node =>
                    node.nodeType === Node.TEXT_NODE ? node.textContent?.trim() : true
                );
                if (!hasContentBeforeBr) {
                    return;
                }
            }

            // 按br标签分割内容
            const segments: Node[][] = [];
            let currentSegment: Node[] = [];

            childNodes.forEach(node => {
                if (node.nodeName === 'BR') {
                    // 遇到br标签，结束当前段落
                    if (currentSegment.length > 0) {
                        segments.push([...currentSegment]);
                        currentSegment = [];
                    }
                    // 如果当前没有内容但这不是第一个br，添加空段落
                    else if (segments.length > 0) {
                        segments.push([]);
                    }
                } else {
                    currentSegment.push(node);
                }
            });

            // 添加最后一个段落（如果有内容）
            if (currentSegment.length > 0) {
                segments.push(currentSegment);
            }

            // 如果分割出多个段落，替换原段落
            if (segments.length > 1) {
                const parent = p.parentNode;
                if (!parent) return;

                // 获取原段落的data-nodeid属性
                const originalNodeId = p.getAttribute('data-nodeid');

                // 调试日志：记录拆分信息
                if (originalNodeId && debug) {
                    console.log(`[splitBrIntoP] 拆分段落 data-nodeid="${originalNodeId}"，将拆分为 ${segments.length} 个段落`);
                }

                // 创建新的p标签替换原来的
                const newParagraphs = segments.map((segment, index) => {
                    const newP = document.createElement('p');

                    // 复制原段落的类名和样式
                    newP.className = p.className;
                    if (p.getAttribute('style')) {
                        newP.setAttribute('style', p.getAttribute('style')!);
                    }

                    // 设置data-nodeid属性
                    if (originalNodeId) {
                        const newNodeId = index === 0 ? originalNodeId : `${originalNodeId}-${index}`;
                        newP.setAttribute('data-nodeid', newNodeId);

                        // 调试日志：记录新段落的data-nodeid
                        if (debug) {
                            console.log(`[splitBrIntoP] 新段落 ${index + 1}: data-nodeid="${newNodeId}"`);
                        }
                    }

                    // 复制其他可能存在的data-*属性（除了data-nodeid）
                    Array.from(p.attributes).forEach(attr => {
                        if (attr.name.startsWith('data-') && attr.name !== 'data-nodeid') {
                            newP.setAttribute(attr.name, attr.value);
                        }
                    });

                    // 添加段落内容
                    if (segment.length > 0) {
                        segment.forEach(node => {
                            // 克隆节点以避免移动原节点
                            const clonedNode = node.cloneNode(true);
                            newP.appendChild(clonedNode);
                        });
                    }

                    // 如果段落为空或只包含空白字符，添加一个空格以保持段落结构
                    if (!newP.textContent?.trim()) {
                        newP.innerHTML = '&nbsp;';
                    }

                    return newP;
                });

                // 在原段落位置插入新段落
                newParagraphs.forEach(newP => {
                    parent.insertBefore(newP, p);
                });

                // 移除原段落
                parent.removeChild(p);
            }
        });
    } catch (error) {
        console.warn('[splitBrIntoP] 处理预览HTML时出错:', error);
    }
};