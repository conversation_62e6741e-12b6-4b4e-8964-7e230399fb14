.toast-markdown-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;

  .editor-ref-container {
    position: relative;
    height: 100%;
    overflow: hidden;

    .viewer-container {
      width: 100%;
      overflow-y: auto;
      border: 1px solid #dadde6;
      border-radius: 4px;
      padding: 16px 24px;
      /* background: #fafafa; */

      /* 不能直接改，参考文献内容不要缩进 */
      /* p {
        text-indent: 2em;
        text-align: justify;
      } */
    }

    .toastui-editor-contents {
      font-size: 17px;
      line-height: 1.5;
      font-family:
        -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans",
        sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      line-height: 1.5;

      /* 覆盖代码块字体 */
      pre,
      code,
      blockquote {
        font-family: inherit;
      }

      /* 大标题居中 */
      h1 {
        text-align: center;
      }
      /* 标题重叠 */
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        line-height: 1.5;
      }

      /* 取消斜体 */
      em {
        font-style: initial;
      }

      /* 有序、无序列表增加缩进 */
      ol,
      ul {
        margin-left: 2em;
      }

      /* 添加平滑过渡，避免样式应用时的闪动 */
      * {
        transition: var(--style-transition, none);
      }
    }

    /* 只处理第一层P元素 */
    .toastui-editor-contents > p {
      text-indent: 2em;
      text-align: justify;
    }
  }

  .trial-mask {
    z-index: 99;
    width: 100%;
    height: 25%;
    min-height: 200px;
    position: absolute;
    bottom: 0px;
    background: linear-gradient(0deg, #fff, 80%, transparent);
    /* background: red; */

    display: flex;
    align-items: end;
    justify-content: center;
    padding-bottom: 50px;
  }
}

.noselect {
  user-select: none;
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE/Edge */
}

/* 源码编辑markdown编辑模式样式 */
.toast-markdown-editor.markdown-mode {
  .toastui-editor {
    /* 确保垂直分割布局正确显示 */
    .toastui-editor-main {
      display: flex;
      height: 100%;
    }
    
    /* 左侧编辑区域 */
    .toastui-editor-md-container {
      flex: 1;
      border-right: 1px solid #dadde6;
    }
    
    /* 右侧预览区域 */
    .toastui-editor-md-preview {
      flex: 1;
      padding: 16px 24px;
      font-size: 17px;
      line-height: 1.5;
      font-family:
        -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
        sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    }
    
    /* 预览区域内容样式 */
    .toastui-editor-md-preview {
      /* 大标题居中 */
      h1 {
        text-align: center;
      }
      
      /* 标题重叠处理 */
      h1, h2, h3, h4, h5, h6 {
        line-height: 1.5;
      }
      
      /* 取消斜体 */
      em {
        font-style: initial;
      }
      
      /* 有序、无序列表增加缩进 */
      ol, ul {
        margin-left: 2em;
      }
      
      /* 段落缩进 */
      p {
        text-indent: 2em;
        text-align: justify;
      }
    }
    
    /* 滚动同步按钮样式 */
    .toastui-editor-toolbar-icons .scroll-sync {
      background-color: #f8f9fa;
    }
    
    .toastui-editor-toolbar-icons .scroll-sync.active {
      background-color: #e9ecef;
    }
  }
}

/* LaTeX公式 */
.math-inline {
  display: inline;
  margin: 0 2px;
  line-height: inherit !important;
  vertical-align: baseline;
}

.math-block {
  display: block;
  text-align: center;
  line-height: inherit !important;
  /* padding: 12px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #e8e8e8; */
}

.math-error {
  color: #ff4d4f;
  background-color: #fff2f0;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: monospace;
  border: 1px solid #ffccc7;
}

/* KaTeX 样式优化 */
.katex {
  font-size: 1.1em;
  line-height: 1.2;
}

.katex-display {
  margin: 0;
  text-align: center;
}

/* LaTeX公式工具栏按钮 */
.math-inline-button,
.math-block-button {
  font-weight: bold;
  font-family: 'Times New Roman', serif;
}

.math-inline-button:hover,
.math-block-button:hover {
  background-color: #f0f0f0;
}

/* LaTeX公式在markdown模式下的显示 */
.toast-markdown-editor.markdown-mode .toastui-editor-md-preview .math-block {
  background-color: transparent;
  border: none;
  padding: 8px 0;
}

.toast-markdown-editor.markdown-mode .toastui-editor-md-preview .math-inline {
  margin: 0 1px;
}

/* 非 参考文献样式 */
.titleClass {
  /* color: red !important; */
}

.contentClass {
  text-indent: 0em !important;
  text-align: justify;
  /* background-color: red !important;
  border-left: 3px solid #1890ff !important; */
}

.listItemClass {
  text-indent: 0;
  text-align: justify;
}
