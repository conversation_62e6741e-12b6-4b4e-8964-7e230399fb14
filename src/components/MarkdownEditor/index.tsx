/**
 * Toast UI Editor的封装组件 - 支持渲染模式和编辑模式
 * 该组件用于在项目中统一处理markdown内容的展示和编辑
 *
 */
import { useAuth } from "@/contexts/AuthContext";
import { DownOutlined } from "@ant-design/icons";
import "@toast-ui/editor/dist/i18n/zh-cn";
import "@toast-ui/editor/dist/toastui-editor.css";
import { Editor, Viewer } from "@toast-ui/react-editor";
import { Button } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import subsupPlugin from "tui.editor.supsup_plugin";
import showLimitedModal from "../LimitedModal";
// import { SelectionEditor, SelectionPoper, TextAction } from "../TextSelectionTool";
import "./index.css";
import { splitBrIntoP } from "./utils/splitBrIntoP";
// LaTeX公式公式插件和katex
import mathPlugin from "./plugins/mathPlugin";
import katex from 'katex';

const CONTEXT_EXTEND_LENGTH = 100; // 上下文前后扩展范围
/** viewer自动滚动 */
const VIEWER_CONTAINER_ID = "viewer-container-id";
const AUTO_SCROLL_HEIGHT = 1000;
/** 拷贝限制 */
const MARKDOWN_CONTAINER_ID = "markdown-container-id";

/** 默认排除的标题 */
const DEFAULT_EXCLUDE_PATTERNS = ["参考文献"];

/** 默认样式类名 */
const DEFAULT_STYLE_CLASSES = {
  titleClass: "titleClass",
  contentClass: "contentClass",
  listItemClass: "listItemClass",
};

export interface MarkdownEditorProps {
  projectId?: string;
  content: string;
  height?: string;
  isEditMode?: boolean;
  trialMask?: Boolean;
  onChange?: (content: string) => void;
  /** 排除的标题模式，如['参考文献'] */
  excludeTitlePatterns?: string[];
  /** 添加到非排除标题及其内容的样式类 */
  titleContentClasses?: {
    titleClass?: string; // 标题样式类
    contentClass?: string; // 内容样式类
    listItemClass?: string; // 列表项样式类
  };
  /** 是否应用样式 */
  disableAutoStyle?: boolean;
  /** 是否流式获取中 */
  isStreaming?: boolean;
  /** 编辑器类型，wysiwyg(所见即所得) 、 markdown(源码编辑) */
  editorType?: 'wysiwyg' | 'markdown';
}

export interface MarkdownEditorRef {
  // getInstance: () => any;
  getMarkdown: () => string;
  getHTML: () => string;
}

/**
 * MarkdownEditor组件 - 使用Toast UI Editor实现markdown内容的编辑和渲染
 * @param props - 组件属性，包含标题、内容、高度、是否可编辑、文本动作回调等
 * @param ref - 父组件传入的ref，用于调用编辑器实例的方法
 */
const ToastMarkdownEditor = forwardRef<MarkdownEditorRef, MarkdownEditorProps>((props, ref) => {
  const {
    projectId,
    content = "",
    height = "100%",
    isEditMode = false,
    trialMask = false,
    excludeTitlePatterns = DEFAULT_EXCLUDE_PATTERNS,
    titleContentClasses = DEFAULT_STYLE_CLASSES,
    disableAutoStyle = false,
    isStreaming,
    onChange,
    // 默认所见即所得
    editorType = 'wysiwyg',
  } = props;

  const { isTrial } = useAuth();

  const editorRef = useRef<any>(null);
  const viewerRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  // 跟踪是否已经应用了样式
  const stylesAppliedRef = useRef<boolean>(false);

  const [selectionEditorOpen, setSelectionEditorOpen] = useState(false);
  // const [selectionEditorAction, setSelectionEditorAction] = useState<TextAction | null>(null);
  const [selectionEditorContent, setSelectionEditorContent] = useState<string>("");
  const [selectionEditorContext, setSelectionEditorContext] = useState<string>("");

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    getMarkdown: () => {
      return editorRef.current?.getInstance().getMarkdown();
    },
    getHTML: () => {
      const instance = isEditMode
        ? editorRef.current?.getInstance()
        : viewerRef.current?.getInstance();
      // console.log("ref===", instance);
      // 获取innerHTML，等效document
      const htmlContent = instance.preview.previewContent.innerHTML;
      // console.log("ref111===", htmlContent);
      // const doc = document.getElementsByClassName("toastui-editor-contents");
      // console.log("ref222===", doc[0].innerHTML);
      // // getHTML能实时获取到html内容，但只适用于编辑模式，且标签没有id，
      // const hm = editorRef.current?.getInstance().getHTML();
      // console.log("ref333===", hm);

      return htmlContent;
    },
  }));

  useEffect(() => {
    const container = document.getElementById(MARKDOWN_CONTAINER_ID);
    container?.addEventListener("contextmenu", e => onContextMenu(e));
    container?.addEventListener("copy", e => onCopyOrCut(e));
    container?.addEventListener("cut", e => onCopyOrCut(e));

    return () => {
      container?.removeEventListener("contextmenu", e => onContextMenu(e));
      container?.removeEventListener("copy", e => onCopyOrCut(e));
      container?.removeEventListener("cut", e => onCopyOrCut(e));
    };
  }, []);

  useEffect(() => {
    console.log("splitBrIntoP wait");
    // 流式结束后执行，减少dom操作
    if (content && isStreaming === false) {
      if (!isEditMode) {
        // 预览模式
        const ins = viewerRef.current?.getInstance();
        if (ins) {
          const htmlContent = ins.preview.previewContent;
          console.log("splitBrIntoP ready ================= ", htmlContent);
          requestAnimationFrame(() => {
            splitBrIntoP(htmlContent);
            // LaTeX公式渲染
            renderMathFormulas(htmlContent);
          });
        }
      } else if (editorType === 'markdown') {
        // 源码编辑的右侧预览LaTeX公式渲染
        setTimeout(() => {
          renderMathFormulas();
        }, 300); // 延迟确保渲染完成
      }
    }
  }, [isEditMode, content, isStreaming, editorType]);

  // LaTeX公式渲染
  const renderMathFormulas = (container?: Element) => {
    let targetContainer = container;
    
    if (!targetContainer) {
      if (isEditMode && editorType === 'markdown') {
        // 源码编辑右侧预览
        const editorElement = editorRef.current?.getRootElement();
        if (editorElement) {
          // Toast UI Editor在markdown模式下，预览区域的选择器
          targetContainer = editorElement.querySelector('.toastui-editor-md-preview .toastui-editor-contents');
        }
      } else if (isEditMode) {
        // 可视模式
        targetContainer = editorRef.current?.getRootElement();
      } else {
        // 预览模式
        targetContainer = viewerRef.current?.getInstance()?.preview?.previewContent;
      }
    }
      
    if (!targetContainer) {
      console.warn('LaTeX公式：未找到目标容器');
      return;
    }
    console.log('LaTeX公式渲染:', targetContainer);
    
    try {
      // 处理未被插件正确渲染的数学公式
      const textNodes: Text[] = [];
      const walker = document.createTreeWalker(
        targetContainer,
        NodeFilter.SHOW_TEXT,
        null
      );
      
      let node;
      while (node = walker.nextNode()) {
        if (node.nodeType === Node.TEXT_NODE) {
          textNodes.push(node as Text);
        }
      }
      
      textNodes.forEach((textNode: Text) => {
        if (textNode.nodeValue) {
          const content = textNode.nodeValue;
          
          // 处理行内公式 $...$
          if (content.includes('$') && !content.includes('$$')) {
            const mathRegex = /\$([^$\n]+?)\$/g;
            if (mathRegex.test(content)) {
              const span = document.createElement('span');
              span.innerHTML = content.replace(mathRegex, (match, formula) => {
                try {
                  return `<span class="math-inline">${katex.renderToString(formula, {
                    displayMode: false,
                    throwOnError: false
                  })}</span>`;
                } catch (error) {
                  console.warn('LaTeX公式：行内公式渲染失败', error);
                  return match;
                }
              });
              textNode.parentNode?.replaceChild(span, textNode);
            }
          }
          
          // 处理块级公式 $$...$$
          if (content.includes('$$')) {
            const blockMathRegex = /\$\$([\s\S]*?)\$\$/g;
            if (blockMathRegex.test(content)) {
              const div = document.createElement('div');
              div.innerHTML = content.replace(blockMathRegex, (match, formula) => {
                try {
                  return `<div class="math-block">${katex.renderToString(formula.trim(), {
                    displayMode: true,
                    throwOnError: false
                  })}</div>`;
                } catch (error) {
                  console.warn('LaTeX公式：块级公式渲染失败', error);
                  return match;
                }
              });
              textNode.parentNode?.replaceChild(div, textNode);
            }
          }
        }
      });
      
      console.log('LaTeX公式渲染完成');
    } catch (error) {
      console.error('LaTeX公式渲染出错', error);
    }
  };

  /** 禁用右键菜单 */
  const onContextMenu = (e: any) => {
    if (isTrial) {
      e.preventDefault();
      window.getSelection()?.removeAllRanges();
      showLimitedModal("copy");
      return;
    }
  };
  /** 禁用复制行为 */
  const onCopyOrCut = (e: any) => {
    if (isTrial) {
      e.preventDefault();
      window.getSelection()?.removeAllRanges();
      showLimitedModal("copy");
      return;
    }
  };

  // 应用样式的函数
  const applyStyles = () => {
    if (disableAutoStyle) return;

    // 重置标记，表示需要重新应用样式
    stylesAppliedRef.current = false;

    // 使用requestAnimationFrame确保DOM更新后再应用样式
    requestAnimationFrame(() => {
      // 获取正确的实例
      const instance = isEditMode
        ? editorRef.current?.getInstance()
        : viewerRef.current?.getInstance();
      if (!instance) return;

      // 标记已应用样式
      stylesAppliedRef.current = true;

      // 获取预览内容元素 - 编辑模式和预览模式下的DOM结构不同
      let previewElement;

      if (isEditMode) {
        // 编辑模式下，需要获取编辑器内容区域
        // Toast UI Editor在wysiwyg模式下使用iframe
        const editorEl = editorRef.current?.getRootElement();
        if (!editorEl) return;

        // 查找wysiwyg编辑器内容区域
        // const editorIframe = editorEl.querySelector('.toastui-editor-contents');
        const editorIframe = editorEl.querySelector(".toastui-editor .toastui-editor-contents");
        // console.log("编辑模式editorIframe === ", editorIframe);

        // if (!editorIframe) {
        //   // 尝试获取iframe内部文档
        //   const iframe = editorEl.querySelector('iframe');
        //   if (iframe && iframe.contentDocument) {
        //     previewElement = iframe.contentDocument.body;
        //     console.log("编辑模式previewElement === ", previewElement);
        //   } else {
        //     console.warn('无法获取编辑器内容区域');
        //     return;
        //   }
        // } else {
        previewElement = editorIframe;
        // console.log("编辑模式previewElement === ", previewElement);
        // }
      } else {
        // 预览模式
        if (!instance.preview || !instance.preview.previewContent) return;
        previewElement = instance.preview.previewContent;

        // console.log("预览模式previewElement === ", previewElement);
      }

      if (!previewElement) {
        console.warn("无法获取内容预览元素");
        return;
      }

      // 查找所有标题
      const headings = previewElement.querySelectorAll("h1, h2, h3, h4, h5, h6");

      if (!headings || headings.length === 0) return;

      // 处理每个标题
      headings.forEach((heading: Element) => {
        const headingText = heading.textContent?.toLowerCase() || "";

        // 检查是否应该排除（"参考文献"）
        const shouldExclude = excludeTitlePatterns.some(pattern =>
          headingText.includes(pattern.toLowerCase()),
        );

        // 不匹配排除模式，应用样式
        // if (!shouldExclude) {
        // 转换思路，给编辑器中所有内容增加缩进样式，找到参考文献的再加取消缩进
        if (shouldExclude) {
          // 添加标题样式
          if (titleContentClasses.titleClass) {
            heading.classList.add(titleContentClasses.titleClass);
          }

          // 处理该标题后的所有内容，直到下一个标题
          let currentElement = heading.nextElementSibling;
          while (currentElement && !currentElement.matches("h1, h2, h3, h4, h5, h6")) {
            // 添加内容样式
            if (titleContentClasses.contentClass) {
              // currentElement.classList.add(titleContentClasses.contentClass);
            }

            // 处理列表项
            if (
              (currentElement.tagName === "UL" || currentElement.tagName === "OL") &&
              titleContentClasses.listItemClass
            ) {
              const listItems = currentElement.querySelectorAll("li");
              listItems.forEach(item => {
                // item.classList.add(titleContentClasses.listItemClass || '');
              });
            }

            currentElement = currentElement.nextElementSibling as Element;
          }
        }
      });
    });
  };

  // 监听内容变化
  useEffect(() => {
    const instance = isEditMode
      ? editorRef.current?.getInstance()
      : viewerRef.current?.getInstance();
    if (instance) {
      instance.setMarkdown(content, false);
      // 设置内容后延迟应用样式
      setTimeout(applyStyles, 100);
    }
    if (content && !isEditMode) {
      const scrollContainer = document.getElementById(VIEWER_CONTAINER_ID);
      if (scrollContainer) {
        const scrollOffset =
          scrollContainer.scrollHeight - scrollContainer.scrollTop - scrollContainer.clientHeight;
        // console.log("scrollOffset======", scrollOffset);
        const needAutoScroll = scrollOffset < AUTO_SCROLL_HEIGHT;
        if (needAutoScroll) {
          // console.log(
          //   "need scroll======",
          //   scrollContainer.scrollTop,
          //   scrollContainer.scrollHeight,
          // );
          scrollContainer.scrollTo({
            top: scrollContainer.scrollHeight,
            behavior: "smooth",
          });
        }
      }
    }
  }, [content, isEditMode, disableAutoStyle]);

  // 专门处理编辑模式下的样式应用
  useEffect(() => {
    if (!isEditMode || disableAutoStyle) return;

    const editorInstance = editorRef.current?.getInstance();
    if (!editorInstance) return;

    // 只保留初始应用样式
    setTimeout(applyStyles, 200);
  }, [isEditMode, disableAutoStyle]);

  // 监听编辑模式变化时重新应用样式
  useEffect(() => {
    // 当编辑模式变化时，需要重新应用样式
    setTimeout(applyStyles, 200);
  }, [isEditMode]);

  // 监听编辑器类型，重新应用样式
  useEffect(() => {
    if (isEditMode) {
      // 编辑器重新创建后，延迟应用样式
      setTimeout(applyStyles, 300);
    }
  }, [editorType, isEditMode]);

  // const onPoperAction = (action: TextAction) => {
  //   const ins = editorRef?.current?.getInstance();
  //   if (!ins) {
  //     console.warn("onPoperAction, 未获取到editor实例");
  //     return;
  //   }
  //   console.log("onPoperAction ins === ", ins);
  //   /** 获取目标内容 */
  //   const selectedText: string = ins.getSelectedText() || "";
  //   if (!selectedText.trim()) return;
  //   console.log("onPoperAction selectedText === ", selectedText);
  //   console.log("onPoperAction selectedText length === ", selectedText.length);
  //   // 动态截取上下文
  //   const dynamicLength = Math.floor(selectedText.length / 5);
  //   const contextLength = Math.max(dynamicLength, CONTEXT_EXTEND_LENGTH);
  //   /** 获取上下文内容 */
  //   const md = ins.getMarkdown();
  //   console.log("onPoperAction md length === ", md.length);
  //   const editorSelection = ins.getSelection();
  //   console.log("onPoperAction editorSelection === ", editorSelection);
  //   const [from, to] = editorSelection;
  //   const fixFrom = from - contextLength < 0 ? 0 : from - contextLength;
  //   const prefix = ins.getSelectedText(fixFrom, from);
  //   console.log("onPoperAction prefix === ", prefix);
  //   let subffix = "";
  //   const fixTo = to + contextLength >= md.length ? md.length - 1 : to + contextLength;
  //   try {
  //     subffix = ins.getSelectedText(to, fixTo);
  //   } catch (error) {
  //     console.warn("getSelectedText subffix error === ", error);
  //   }
  //   console.log("onPoperAction subffix === ", subffix);
  //   const selectedContext = prefix + selectedText + subffix;
  //   console.log("onPoperAction selectedContext === ", selectedContext);
  //   /** 更新状态 */
  //   setSelectionEditorOpen(prev => {
  //     setSelectionEditorAction(action);
  //     setSelectionEditorContent(selectedText);
  //     setSelectionEditorContext(selectedContext);
  //     return true;
  //   });
  // };

  const onEditorOk = (content: string) => {
    console.log("onEditorOk action === ", content);
    setSelectionEditorOpen(false);
    // 替换选中的内容
    editorRef.current?.getInstance().replaceSelection(content);
  };

  const onEditorCancel = () => {
    setSelectionEditorOpen(false);
  };

  const renderEditor = () => {
    // 源码编辑：根据编辑类型动态配置编辑器参数
    const editorConfig = {
      // 基础配置保持不变
      ref: editorRef,
      initialValue: content,
      height: height,
      autofocus: false,
      // LaTeX公式插件和上标下标插件
      plugins: [subsupPlugin, mathPlugin],
      usageStatistics: false, // 禁用统计使用情况
      language: "zh-CN",
      hideModeSwitch: true, // 隐藏模式切换按钮
      onChange: () => {
        if (onChange && editorRef.current) {
          const newContent = editorRef.current.getInstance().getMarkdown();
          if (newContent !== content) {
            onChange(newContent);
            // 在内容变化后应用样式，与useEffect中的handleChange保持一致
            setTimeout(applyStyles, 100);
            
            // 源码编辑模式下，内容变化时重新渲染公式
            if (editorType === 'markdown') {
              setTimeout(() => {
                renderMathFormulas();
              }, 300); //  延迟确保更新完成
            }
          }
        }
      },
      ...(editorType === 'markdown' ? {
        // Markdown 源码编辑模式配置
        initialEditType: "markdown" as const,
        previewStyle: "vertical" as const,     // 垂直分割布局
        toolbarItems: [
          ["heading", "bold", "italic", "strike"],
          ["hr", "quote"],
          ["ul", "ol", "task", "indent", "outdent"],
          ["link"], // 'table', 'image',
          ["code", "codeblock"],
          // LaTeX公式：暂时移除自定义工具栏按钮，有冲突
          // ["mathInline", "mathBlock"],
          ["scrollSync"],             // 启用滚动同步
        ],
      } : {
        // WYSIWYG 模式配置（保持现有）
        initialEditType: "wysiwyg" as const,
        previewStyle: "tab" as const,
        toolbarItems: [
          ["heading", "bold", "italic", "strike"],
          ["hr", "quote"],
          ["ul", "ol", "task", "indent", "outdent"],
          ["link"], // 'table', 'image',
          ["code", "codeblock"],
          // LaTeX公式：暂时移除自定义工具栏按钮，有冲突
          // ["mathInline", "mathBlock"],
          // ['scrollSync'],
        ],
      })
    };

    // 重新创建实例，源码编辑切换
    return <Editor key={`editor-${editorType}`} {...editorConfig} />;
  };
  const renderViewer = () => {
    return (
      <div
        className="viewer-container"
        id={VIEWER_CONTAINER_ID}
        style={{
          height: height,
        }}
      >
        <Viewer
          ref={viewerRef}
          initialValue={content}
          usageStatistics={false} // 禁用统计使用情况
          // LaTeX公式插件
          plugins={[mathPlugin]}
        />
      </div>
    );
  };

  return (
    <div
      className={`toast-markdown-editor ${isTrial ? "noselect" : ""} ${
        isEditMode && editorType === 'markdown' ? 'markdown-mode' : ''
      }`}
      id={MARKDOWN_CONTAINER_ID}
    >
      <div className="editor-ref-container" ref={containerRef}>
        {isEditMode ? renderEditor() : renderViewer()}
        {/* {isEditMode && <SelectionPoper targetRef={containerRef} onAction={onPoperAction} />} */}
      </div>
      {trialMask && (
        <div className="trial-mask">
          <Button
            variant="filled"
            style={{ border: "none" }}
            icon={<DownOutlined />}
            iconPosition="end"
            onClick={() => {
              showLimitedModal("words");
            }}
          >
            字数已达字上限，升级账户即可获得完整报告
          </Button>
        </div>
      )}
      {/* <SelectionEditor
        projectId={projectId || ""}
        open={selectionEditorOpen}
        action={selectionEditorAction}
        content={selectionEditorContent}
        context={selectionEditorContext}
        onCancel={onEditorCancel}
        onOk={onEditorOk}
      /> */}
    </div>
  );
});

export default ToastMarkdownEditor;
