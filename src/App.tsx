import { antTheme } from '@/config/theme';
import { App as AntApp, ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import React from 'react';
import Navigator from './components/Navigator';
import { AuthProvider } from './contexts/AuthContext';
import { PermissionProvider } from './contexts/PermissionContext';
import './styles/global.css';

const App: React.FC = () => {
  return (
    <AntApp>
      <AuthProvider>
        <PermissionProvider>
          <ConfigProvider theme={antTheme} locale={zhCN}>
            <Navigator />
          </ConfigProvider>
        </PermissionProvider>
      </AuthProvider>
    </AntApp>
  );
};

export default App; 