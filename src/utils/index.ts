/* 传入{label: string; value: string}[]和value值返回label */
export function getLabel(
  value: string,
  list: {
    value: string;
    label: string;
  }[],
) {
  return list.find(item => item.value === value)?.label || "";
}

/**
 * 自定义事件系统，组件间通信
 */
export const eventBus = {
  listeners: {} as Record<string, Function[]>,
  on(event: string, callback: Function) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
    return () => this.off(event, callback); // 返回取消订阅函数
  },
  off(event: string, callback: Function) {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
  },
  emit(event: string, data?: any) {
    if (!this.listeners[event]) return;
    this.listeners[event].forEach(callback => callback(data));
    console.log(`事件 ${event} 已触发，数据:`, data);
  },
};

export const EVENTS = {
  MODEL_CONFIGS_UPDATED: "MODEL_CONFIGS_UPDATED",
};

/** 生成随机 HSL 颜色，降低饱和度 */
export const getRandomLowSaturationColor = () => {
  const hue = Math.floor(Math.random() * 360);
  const saturation = Math.floor(Math.random() * 30) + 40; // 饱和度在 40% 到 70% 之间
  const lightness = Math.floor(Math.random() * 20) + 60; // 亮度在 60% 到 80% 之间
  return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
};
/**
 * 生成随机舒适色系 HEX 颜色，适合作为背景或标签使用
 * @returns {string} 16进制颜色值，例如 #d8eaff
 */
export const getRandomComfortableHexColor = (): string => {
  // 在 HSL 空间中生成颜色，以便于控制饱和度和亮度
  const hue = Math.floor(Math.random() * 360); // 随机色相 0-359
  const saturation = Math.floor(Math.random() * 25) + 40;
  const lightness = Math.floor(Math.random() * 10) + 65;

  // 将 HSL 转换为 RGB
  const c = ((1 - Math.abs((2 * lightness) / 100 - 1)) * saturation) / 100;
  const x = c * (1 - Math.abs(((hue / 60) % 2) - 1));
  const m = lightness / 100 - c / 2;

  let r, g, b;
  if (hue < 60) {
    [r, g, b] = [c, x, 0];
  } else if (hue < 120) {
    [r, g, b] = [x, c, 0];
  } else if (hue < 180) {
    [r, g, b] = [0, c, x];
  } else if (hue < 240) {
    [r, g, b] = [0, x, c];
  } else if (hue < 300) {
    [r, g, b] = [x, 0, c];
  } else {
    [r, g, b] = [c, 0, x];
  }

  // 调整并转换为 0-255 范围
  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  // 转换为十六进制并确保两位数
  const toHex = (num: number): string => {
    const hex = num.toString(16);
    return hex.length === 1 ? "0" + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
};

// 移除markdown的一级标题和文章的题目
export function removeMarkdownH1AndText(text: string, title: string) {
  if (!title) return text;

  // 截取前面部分文本
  const splitIndex = title.length * 2 + 20;
  const frontText = text.slice(0, splitIndex);
  const remainingText = text.slice(splitIndex);

  // 分段处理（按空行分段）
  const paragraphs = frontText.split(/\n+/);

  // 移除包含 title 的段落
  const filteredParagraphs = paragraphs.filter(paragraph => paragraph.indexOf(title) === -1);
  // 合并过滤后的前部分和剩余文本
  const filteredFrontText = filteredParagraphs.join('\n\n');
  const withoutTitle = filteredFrontText + remainingText;

  return withoutTitle
}

/**
 * 获取文件类型对应的dify分类
 * @param fileType
 * @returns
 */
export const getFileTypeForDify = (fileType: string): "image" | "document" | "custom" => {
  const imageTypes = ["jpg", "jpeg", "png", "bmp", "tiff", "webp"];
  const docTypes = ["doc", "docx", "pdf", "txt", "md", "markdown"];

  const type = fileType.toLowerCase();

  const imageType = imageTypes.find(f => type.includes(f));
  if (imageType) {
    return "image";
  }

  const docType = docTypes.find(f => type.includes(f));
  if (docType) {
    return "document";
  }

  return "custom";
};

/**
 * 处理LaTeX代码块，转换为可渲染的markdown数学公式
 * @param content 包含latex代码块的内容
 * @returns 转换后的markdown格式内容
 */
export const processLatexContent = (content: string): string => {
  let processedContent = content;

  // 处理latex代码块 ```latex ... ```
  processedContent = processedContent.replace(
    /```latex\s*\n([\s\S]*?)\n```/g,
    (match, latexContent) => {
      let latex = latexContent.trim();

      // 1. 移除完整LaTeX文档结构
      latex = latex.replace(/\\documentclass\{[^}]*\}/g, "");
      latex = latex.replace(/\\usepackage(?:\[[^\]]*\])?\{[^}]*\}/g, "");
      latex = latex.replace(/\\begin\{document\}/g, "");
      latex = latex.replace(/\\end\{document\}/g, "");

      // 2. 移除LaTeX注释（以%开头的行）
      latex = latex.replace(/^\s*%.*$/gm, "");

      // 3. 处理行间公式 \[ ... \] → $$ ... $$ (修复替换语法)
      latex = latex.replace(/\\\[([\s\S]*?)\\\]/g, (match: string, formula: string) => {
        return `$$${formula}$$`;
      });

      // 4. 处理行内公式 \( ... \) → $ ... $
      latex = latex.replace(/\\\(([\s\S]*?)\\\)/g, (match: string, formula: string) => {
        return `$${formula}$`;
      });

      // 5. 处理各种数学环境 → $$ ... $$
      const mathEnvironments = [
        "align",
        "align\\*",
        "equation",
        "equation\\*",
        "gather",
        "gather\\*",
        "multline",
        "multline\\*",
        "split",
        "split\\*",
      ];

      mathEnvironments.forEach(env => {
        const envPattern = env.replace("\\*", "\\*");
        latex = latex.replace(new RegExp(`\\\\begin\\{${envPattern}\\}`, "g"), "$$");
        latex = latex.replace(new RegExp(`\\\\end\\{${envPattern}\\}`, "g"), "$$");
      });

      // 6. 移除对齐符号 &（但保护矩阵环境）
      const matrixPattern = /\\begin\{[bpvBVM]?matrix\}[\s\S]*?\\end\{[bpvBVM]?matrix\}/g;
      const matrices: string[] = [];
      let placeholder = "___MATRIX_PLACEHOLDER___";

      // 临时移除矩阵内容
      latex = latex.replace(matrixPattern, (match: string, index: number) => {
        matrices.push(match);
        return `${placeholder}${matrices.length - 1}`;
      });

      // 在非矩阵区域移除&
      latex = latex.replace(/&/g, "");

      // 恢复矩阵内容
      matrices.forEach((matrix, index) => {
        latex = latex.replace(`${placeholder}${index}`, matrix);
      });

      // 7. 清理多余空行
      latex = latex.replace(/\n\s*\n\s*\n/g, "\n\n");
      latex = latex.trim();

      // 8. 🔥 兼容性修复：只对纯数学表达式添加$$包装
      if (latex && latex.length > 0) {
        // 检查是否已经包含数学公式标记
        const hasFormulaMarkers =
          latex.includes("$$") ||
          latex.includes("$") ||
          latex.includes("\\[") ||
          latex.includes("\\(");

        // 检查是否是纯数学表达式（没有文本描述）
        const isPureMath =
          !latex.includes(":") &&
          !latex.includes("。") &&
          !latex.includes(".") &&
          !latex.includes("时") &&
          !latex.includes("当") &&
          !/[a-zA-Z]{3,}/.test(latex.replace(/\\[a-zA-Z]+/g, "")); // 排除LaTeX命令

        // 只对没有标记且是纯数学的内容添加$$
        if (!hasFormulaMarkers && isPureMath) {
          latex = `$$${latex}$$`;
        }
      }

      return latex;
    },
  );

  return processedContent.trim();
};
