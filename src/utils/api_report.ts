import api from './api';

export const path = {
  project_report: '/project-reports',
};

// 项目报告相关API
export const projectReportApi = {
  /**
   * 生成项目大纲
   * @param projectId 项目ID
   */
  generateOutline: (projectId: string): Promise<any> =>
    api.post(`${path.project_report}/${projectId}/generate-outline`),

  /**
   * 生成完整项目报告
   * @param projectId 项目ID
   */
  generateReport: (projectId: string): Promise<any> =>
    api.post(`${path.project_report}/${projectId}/generate-report-step`),

  /**
   * 获取项目大纲流式内容的URL
   * @param projectId 项目ID
   */
  getOutlineStreamUrl: (projectId: string): string =>
    `${window.AppConfig.baseURL}${path.project_report}/${projectId}/stream-outline`,

  /**
   * 获取项目报告流式内容的URL
   * @param projectId 项目ID
   */
  getReportStreamUrl: (projectId: string): string =>
    `${window.AppConfig.baseURL}${path.project_report}/${projectId}/stream-report`,

  /**
   * 终止项目大纲生成
   * @param projectId 项目ID
   */
  stopOutlineGeneration: (projectId: string): Promise<any> =>
    api.post(`${path.project_report}/${projectId}/stop-outline`),

  /**
   * 终止项目报告生成
   * @param projectId 项目ID
   */
  stopReportGeneration: (projectId: string): Promise<any> =>
    api.post(`${path.project_report}/${projectId}/stop-report`),
};



export default { projectReportApi};
