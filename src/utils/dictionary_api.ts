import api from './api';
import { Dictionary, AreaItem } from "@/types/Dictionary"

export const path = {
    dictionary: '/dictionary',
    area: "/area/children"
}

export const dictionaryApi = {
    /** 获取字典 */
    getDictionary: (params: any): Promise<Dictionary[]> =>
        api.get(`${path.dictionary}`, { params }),
    /* 获取省市区 */
    getArea: (params?: {
        area_id: string
    }): Promise<AreaItem[]> => api.get(path.area, {
        params
    })
}


export default { dictionaryApi }