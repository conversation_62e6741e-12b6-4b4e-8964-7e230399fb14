import api from "./api";

export const path = {
  ai_chat: "/ai-chat",
};

export const aiChatApi = {
  // 文件上传
  uploadMultipart: (file: any): Promise<any> =>
    api.post(`${path.ai_chat}/upload-multipart`, file, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),

  /** 对话流式内容URL */
  getChatUrl: (): string => `${window.AppConfig.baseURL}${path.ai_chat}/chat`,
};

export default { aiChatApi };
