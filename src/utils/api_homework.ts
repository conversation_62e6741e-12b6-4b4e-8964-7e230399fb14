import api from "./api";

export const path = {
  ai_homework: "/ai-homework",
};

export const aiHomeworkApi = {
  /** 作业解答流式内容URL */
  getHomeworkUrl: (): string => `${window.AppConfig.baseURL}${path.ai_homework}/ai_homework`,

  /**
   * 导出作业
   * @param params
   * @returns
   */
  exportHomeworkInWORD: (data: { content: string; filename?: string }): Promise<any> =>
    api.post(`${path.ai_homework}/export-homework`, data, { responseType: "blob" }),
  exportHomeworkInPDF: (data: { content: string; filename?: string }): Promise<any> =>
    api.post(`${path.ai_homework}/export-homework-pdf`, data, { responseType: "blob" }),
};

export default { aiHomeworkApi };
