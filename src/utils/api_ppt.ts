import api from './api';

// PPT相关API路径配置
export const path = {
  ppt: '/ppt'
};

// PPT生成请求参数接口
export interface PptGenerateParams {
  file: File;
  ppt_type: string;
}

// PPT生成响应接口
export interface PptGenerateResponse {
  success: boolean;
  code: number;
  data: {
    success: boolean;        // 忽略，以外层为准
    message: string;         // 忽略，以外层为准
    timestamp: string;       // 忽略，以外层为准
    id: string;             // 任务唯一标识符
    file_path: string;      // 服务器文件路径
    filename: string;       // 文件名
    size: number;           // 文件大小（字节）
    download_url: string;   // 下载链接
    preview_url: string;    // 预览链接
    document_content: string; // 文档内容预览（前500字符）
    generation_time: number; // 生成耗时（秒）
  };
  error?: string;
}

// PPT模板接口
export interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: string;
}

// PPT历史记录项接口
export interface PptHistoryItem {
  id: string;                      // PPT生成记录的唯一标识符
  original_filename: string;       // 用户上传的原始文档文件名
  original_file_path: string;      // 原始文档在服务器上的存储路径
  ppt_filename: string;            // 系统生成的PPT文件名
  ppt_file_path: string;           // 生成的PPT文件在服务器上的存储路径
  file_type: string;               // 原始文件类型（如.docx、.pdf、.txt）
  ppt_type: string;                // PPT模板类型（simple、creative、graduation）
  status: string;                  // 生成状态（pending、processing、completed、failed）
  file_size: number;               // PPT文件大小（单位：字节）
  error_message?: string | null;   // 错误信息（失败时显示，成功时为null）
  created_at: string;              // 创建时间（ISO格式）
  updated_at: string;              // 更新时间（ISO格式）
}

// PPT历史记录分页响应数据接口
export interface PptHistoryResponseData {
  total: number;                   // 总记录数
  page: number;                    // 当前页码
  page_size: number;               // 每页大小
  total_pages: number;             // 总页数
  items: PptHistoryItem[];         // 历史记录列表
}

// PPT历史记录分页响应接口
export interface PptHistoryResponse {
  success: boolean;                // 请求是否成功
  code: number;                    // 响应状态码（0表示成功）
  data: PptHistoryResponseData;    // 响应数据
  error: string;                   // 错误信息（成功时为空字符串）
}

// PPT相关API
export const pptApi = {
  /**
   * 生成PPT
   * @param formData 包含文件和模板类型的FormData
   * @returns Promise<PPT生成结果>
   */
  generatePpt: (formData: FormData): Promise<PptGenerateResponse['data']> =>
    api.post(`${path.ppt}/generate`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  /**
   * 模拟下载PPT文件（仅UI展示，不实际下载）
   * @param pptId PPT任务ID
   * @returns Promise<文件信息>
   */
  simulateDownloadPpt: async (pptId: string): Promise<{ filename: string; size: number }> => {
    // 模拟下载延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 根据ID获取PPT详情来获取文件信息
    const pptDetail = await pptApi.getPptHistoryById(pptId);

    return {
      filename: pptDetail.ppt_filename,
      size: pptDetail.file_size
    };
  },

  /**
   * 获取PPT模板列表
   * TODO: 后端接口开发完成后启用
   * @returns Promise<PPT模板列表>
   */
  getPptTemplates: (): Promise<PptTemplate[]> =>
    api.get(`${path.ppt}/templates`),

  /**
   * 根据ID获取PPT模板详情
   * TODO: 后端接口开发完成后启用
   * @param templateId 模板ID
   * @returns Promise<PPT模板详情>
   */
  getPptTemplateById: (templateId: string): Promise<PptTemplate> =>
    api.get(`${path.ppt}/templates/${templateId}`),

  /**
   * 下载生成的PPT文件
   * TODO: 后续实现下载功能
   * @param filePath PPT文件路径
   * @param filename 下载文件名
   * @returns Promise<void>
   */
  downloadPpt: (filePath: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}${path.ppt}/download?file_path=${encodeURIComponent(filePath)}`;
    return new Promise((resolve, reject) => {
      try {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取PPT生成状态
   * TODO: 如果需要支持异步生成，可以添加此接口
   * @param taskId 任务ID
   * @returns Promise<生成状态>
   */
  getPptGenerateStatus: (taskId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    message: string;
    result?: PptGenerateResponse['data'];
  }> =>
    api.get(`${path.ppt}/status/${taskId}`),

  /**
   * 获取PPT生成历史记录（分页）
   * @param page 页码，从1开始
   * @param pageSize 每页数量，默认10，最大100
   * @param keyword 搜索关键词（可选）
   * @returns Promise<PPT历史记录分页数据>
   */
  getPptHistory: (page: number = 1, pageSize: number = 10, keyword?: string): Promise<PptHistoryResponseData> => {
    const params: any = {
      page,
      page_size: pageSize
    };
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim();
    }
    return api.get(`${path.ppt}/history`, { params });
  },

  /**
   * 根据ID获取PPT历史记录详情
   * @param pptId PPT记录ID
   * @returns Promise<PPT历史记录详情>
   */
  getPptHistoryById: (pptId: string): Promise<PptHistoryItem> =>
    api.get(`${path.ppt}/history/${pptId}`),

  /**
   * 删除PPT历史记录
   * @param pptId PPT记录ID
   * @returns Promise<void>
   */
  deletePptHistory: (pptId: string): Promise<void> =>
    api.delete(`${path.ppt}/history/${pptId}`),
};

export default { pptApi };
