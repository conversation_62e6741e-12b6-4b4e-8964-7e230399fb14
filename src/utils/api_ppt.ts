import axios from 'axios';
import api from './api';
import { AuthManager } from './auth';

// PPT相关API路径配置
export const path = {
  ppt: '/ppt'
};

// PPT生成请求参数接口
export interface PptGenerateParams {
  file: File;
  ppt_type: string;
}

// PPT生成响应接口
export interface PptGenerateResponse {
  success: boolean;
  code: number;
  data: {
    success: boolean;        // 忽略，以外层为准
    message: string;         // 忽略，以外层为准
    timestamp: string;       // 忽略，以外层为准
    id: string;             // 任务唯一标识符
    file_path: string;      // 服务器文件路径
    filename: string;       // 文件名
    size: number;           // 文件大小（字节）
    download_url: string;   // 下载链接
    preview_url: string;    // 预览链接
    document_content: string; // 文档内容预览（前500字符）
    generation_time: number; // 生成耗时（秒）
  };
  error?: string;
}

// PPT模板接口
export interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: string;
}

// PPT历史记录项接口
export interface PptHistoryItem {
  id: string;
  original_filename: string;
  ppt_filename: string;
  file_type: string;
  model_type: string;
  status: string;
  file_size: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
  download_count: number;
}

// PPT历史记录分页响应接口
export interface PptHistoryResponse {
  items: PptHistoryItem[];
  total: number;
  page: number;
  page_size: number;
  has_more: boolean;
}

// PPT相关API
export const pptApi = {
  /**
   * 生成PPT
   * @param formData 包含文件和模板类型的FormData
   * @returns Promise<PPT生成结果>
   */
  generatePpt: (formData: FormData): Promise<PptGenerateResponse['data']> =>
    api.post(`${path.ppt}/generate`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  /**
   * 根据ID下载PPT文件
   * @param pptId PPT任务ID
   * @returns Promise<Blob> PPT文件的二进制流
   */
  downloadPptById: async (pptId: string): Promise<{ blob: Blob; filename: string }> => {
    try {
      // 创建独立的axios实例用于下载，避免响应拦截器影响
      const downloadApi = axios.create({
        baseURL: window.AppConfig.baseURL,
        timeout: 300000, // 5分钟超时，适合大文件下载
      });

      // 添加请求拦截器，设置认证头
      const token = AuthManager.getToken();
      const headers: any = {
        'Accept': 'application/octet-stream'
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await downloadApi.get(`${path.ppt}/download/by-id/${pptId}`, {
        responseType: 'blob',
        headers: headers
      });

      // 从响应头中提取文件名
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'presentation.pptx'; // 默认文件名

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      return {
        blob: response.data,
        filename: filename
      };
    } catch (error: any) {
      console.error('PPT下载API错误:', error);

      // 处理不同类型的错误
      if (error.response) {
        // 服务器响应了错误状态码
        const status = error.response.status;
        if (status === 404) {
          throw new Error('PPT文件不存在');
        } else if (status === 403) {
          throw new Error('没有权限下载此文件');
        } else if (status >= 500) {
          throw new Error('服务器内部错误');
        } else {
          throw new Error(`下载失败: ${status}`);
        }
      } else if (error.request) {
        // 网络错误
        throw new Error('网络连接失败');
      } else {
        // 其他错误
        throw new Error(error.message || '下载失败');
      }
    }
  },

  /**
   * 获取PPT模板列表
   * TODO: 后端接口开发完成后启用
   * @returns Promise<PPT模板列表>
   */
  getPptTemplates: (): Promise<PptTemplate[]> =>
    api.get(`${path.ppt}/templates`),

  /**
   * 根据ID获取PPT模板详情
   * TODO: 后端接口开发完成后启用
   * @param templateId 模板ID
   * @returns Promise<PPT模板详情>
   */
  getPptTemplateById: (templateId: string): Promise<PptTemplate> =>
    api.get(`${path.ppt}/templates/${templateId}`),

  /**
   * 下载生成的PPT文件
   * TODO: 后续实现下载功能
   * @param filePath PPT文件路径
   * @param filename 下载文件名
   * @returns Promise<void>
   */
  downloadPpt: (filePath: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}${path.ppt}/download?file_path=${encodeURIComponent(filePath)}`;
    return new Promise((resolve, reject) => {
      try {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取PPT生成状态
   * TODO: 如果需要支持异步生成，可以添加此接口
   * @param taskId 任务ID
   * @returns Promise<生成状态>
   */
  getPptGenerateStatus: (taskId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    message: string;
    result?: PptGenerateResponse['data'];
  }> =>
    api.get(`${path.ppt}/status/${taskId}`),

  /**
   * 获取PPT生成历史记录（分页）
   * @param page 页码，从1开始
   * @param pageSize 每页数量，默认10，最大100
   * @param keyword 搜索关键词（可选）
   * @returns Promise<PPT历史记录分页数据>
   */
  getPptHistory: (page: number = 1, pageSize: number = 10, keyword?: string): Promise<PptHistoryResponse> => {
    const params: any = {
      page,
      page_size: pageSize
    };
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim();
    }
    return api.get(`${path.ppt}/history`, { params });
  },

  /**
   * 根据ID获取PPT历史记录详情
   * @param pptId PPT记录ID
   * @returns Promise<PPT历史记录详情>
   */
  getPptHistoryById: (pptId: string): Promise<PptHistoryItem> =>
    api.get(`${path.ppt}/history/${pptId}`),

  /**
   * 删除PPT历史记录
   * @param pptId PPT记录ID
   * @returns Promise<void>
   */
  deletePptHistory: (pptId: string): Promise<void> =>
    api.delete(`${path.ppt}/history/${pptId}`),
};

export default { pptApi };
