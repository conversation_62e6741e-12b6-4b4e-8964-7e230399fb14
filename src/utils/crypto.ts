import { Base64 } from "js-base64";
import pkg from "sm-crypto";
const { sm3, sm4 } = pkg;

// 基础密钥 - 实际项目中应该从环境变量或配置中获取
const baseKey = "5UYxQwbK3tJnMrVfL8zSgHe5R2NdAa7Z";
const sm3Hash = sm3(baseKey);
const key2 = sm3Hash.substring(0, sm3Hash.length / 2);
const iv2 = sm3Hash.substring(sm3Hash.length / 2, sm3Hash.length);

/** 加密数据 */
export const encrypt = (data: string): string => {
  if (!data) {
    return "";
  }
  
  const encryptedData = sm4.encrypt(data, key2, {
    mode: "cbc",
    iv: iv2,
    output: "array",
  });
  
  // 将普通数组转换为Uint8Array
  const uint8Array = new Uint8Array(encryptedData);
  
  // 然后使用Base64编码
  return Base64.fromUint8Array(uint8Array);
};

/** 解密数据 */
export const decrypt = (encryptedStr: string): string => {
  if (!encryptedStr) {
    return "";
  }
  return sm4.decrypt(Base64.toUint8Array(encryptedStr), key2, {
    mode: "cbc",
    iv: iv2,
  });
}; 