import { CreateReportParams, Report } from '@/types';
import { ApiResponse } from '@/types/ApiRes';
import { CreateMenuForm, Menu, MenuTreeNode } from '@/types/Menu';
import { ModelConfig } from '@/types/ModelConfig';
import { CreateOrganizationForm, Organization } from '@/types/Organization';
import { BasePageModel, PaginationParams } from '@/types/Page';
import { CreateRoleForm, Role } from '@/types/Role';
import { LoginInfo, UserInfo } from '@/types/UserInfo';
import { message } from 'antd';
import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { AuthManager } from './auth';

// 公共基础路径
export const getBasePath = () => {
  return import.meta.env.BASE_URL
};

// 创建axios实例
const api = axios.create({
  baseURL: window.AppConfig.baseURL,
  timeout: 6000000,
  // 100分钟
});

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = AuthManager.getToken();
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error: any) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一错误处理
api.interceptors.response.use(
  // (response: AxiosResponse) => response.data,

  (response: AxiosResponse) => {
    // 统一处理响应格式
    const res = response.data as ApiResponse;
    
    // 标准返回401（无效的认证凭据）
    if (res.code === 401) {
      message.open({
        content: res.error || '登录无效或已过期，请重新登录',
        type: 'error'
      });
      // 触发退出
      window.dispatchEvent(new CustomEvent('auth-expired'));
      // 清除登录信息
      AuthManager.clear();
      // 跳转到登录页面，公共基础URL
      window.location.href = `${getBasePath()}/login`;
      return Promise.reject(new Error(res.error || '登录无效或已过期'));
    }
    
    // 如果后端返回的不是标准格式，转换为标准格式
    if (res.code === undefined) {
      return {
        code: response.status,
        data: response.data,
        success: response.status >= 200 && response.status < 300,
      };
    }

    // 处理业务错误码
    // if (!res.success) {
    if (!res.success || res.code !== 0) {
      console.log('res.error', res.error); 
      message.open({
        content: res.error || '操作失败',
        type: 'error'
      });
      return Promise.reject(new Error(res.error || '操作失败'));
    }

    // 返回成功数据
    return res.data;
  },
  (error: any) => {
    // 获取错误状态码
    const status = error.response?.status;
    let errorMsg = '未知错误，请稍后重试';
    
    // 根据HTTP状态码提示错误信息
    if (status === 401) {
      errorMsg = '登录无效或已过期，请重新登录';
      // 触发退出
      window.dispatchEvent(new CustomEvent('auth-expired'));
      // 清除登录信息
      AuthManager.clear();
      // 跳转到登录页面，公共基础URL
      window.location.href = `${getBasePath()}/login`;
      return Promise.reject(new Error(errorMsg));
    } else if (status === 400) {
      errorMsg = '请求参数错误，请检查输入';
    } else if (status === 403) {
      errorMsg = '没有权限执行此操作';
    } else if (status === 404) {
      errorMsg = '请求的资源不存在';
    } else if (status === 500) {
      errorMsg = '服务器内部错误，请联系管理员';
    } else if (status === 502) {
      errorMsg = '网关错误，请稍后重试';
    } else if (status === 503) {
      errorMsg = '服务暂时不可用，请稍后重试';
    } else if (status === 504) {
      errorMsg = '网关超时，请稍后重试';
    }
    
    // 尝试从响应中获取更详细的错误信息
    const serverErrorMsg = error.response?.data?.error;
    if (serverErrorMsg) {
      errorMsg = serverErrorMsg;
    }
    
    // 构造标准错误响应格式
    const errorResponse: ApiResponse = {
      code: status || 500,
      data: null,
      error: errorMsg,
      success: false
    };
    
    // 记录详细错误日志
    console.error('API请求错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status,
      errorMsg,
      error
    });
    
    // 显示错误消息
    message.open({
      content: errorMsg,
      type: 'error'
    });
    
    // 将原始错误包装成有意义的Error对象
    return Promise.reject(new Error(errorMsg));
  }
);

/**
 * 通用文件下载辅助函数
 * @param url 下载请求的URL
 * @param defaultFilename 默认文件名
 * @param acceptHeader 请求头中的Accept类型
 * @param blobType Blob对象的类型
 */
export const downloadFile = async (url: string, defaultFilename: string, acceptHeader: string, blobType: string) => {
  try {
      const response = await axios.get(url, {
          responseType: 'blob',
          headers: {
              Authorization: `Bearer ${AuthManager.getToken()}`,
              'Accept': acceptHeader,
          }
      });

      if (response.status !== 200) {
          throw new Error(`下载失败: ${response.status}`);
      }

      const contentDisposition = response.headers['content-disposition'];
      let filename = defaultFilename;
      // if (contentDisposition && contentDisposition.includes('filename=')) {
      //     const filenameMatch = contentDisposition.match(/filename="?([^"]*)"?/);
      //     if (filenameMatch && filenameMatch[1]) {
      //         // 解码文件名，以防含有中文等特殊字符
      //         filename = decodeURIComponent(filenameMatch[1]);
      //     }
      // }
      // console.log("filename=================", filename);

      const blob = new Blob([response.data], {
          type: response.headers['content-type'] || blobType
      });

      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
      console.error('下载失败:', error);
      throw error;
  }
};


// 登录认证相关API
export const authApi = {
  adminLogin: (username: string, encryptedPassword: string): Promise<LoginInfo> =>
    api.post('/auth/login',
      {
        username: username,
        password: encryptedPassword,
      },
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    ),

  // saas登录
  saasLogin: (params: { code: string; platform: string }) =>
    api.post('/auth/saas/login', params),

  // 发送短信验证码
  sendSmsCode: (mobile: string): Promise<any> =>
    api.post('/sms/send-code', { mobile }),

  // 手机号验证码登录/注册
  mobileLogin: (mobile: string, code: string): Promise<LoginInfo> =>
    api.post('/sms/mobile-login', { mobile, code }),

  // 获取当前用户信息
  getCurrentUser: (): Promise<UserInfo> =>
    api.get('/users/me'),
  
  // 获取所有用户列表-分页
  getAllUsers: (params: PaginationParams): Promise<BasePageModel<UserInfo>> =>
    api.get('/users/', {
      params: {
        page: params.page,
        size: params.size,
        keyword: params.keyword || ''
      }
    }),
  
  // 创建新用户
  createUser: (userData: Partial<UserInfo> & { password: string }): Promise<UserInfo> =>
    api.post('/users/', userData),
  
  // 更新用户信息
  updateUser: (userId: string, userData: Partial<UserInfo>): Promise<UserInfo> =>
    api.put(`/users/${userId}`, userData),

  // 创建修改用户使用次数
  createUserMaxCount: (data: {max_allowed_count: number, user_id: string}): Promise<any> =>
    api.post('/user-report-usages/', {
      // used_count: 0,
      max_allowed_count: data.max_allowed_count,
      user_id: data.user_id
    }),

  // 获取用户使用次数
  getUserMaxCount: (userId: string): Promise<any> =>
    api.get(`/user-report-usages/by-user/${userId}`),
  
  // 删除用户
  deleteUser: (userId: string): Promise<any> =>
    api.delete(`/users/${userId}`),

};

// 角色管理
export const roleApi = {
  // 获取所有角色列表 
  // organization_id机构ID，超级管理员必填，机构管理员忽略此参数
  getAllRoles: (organization_id: string | null): Promise<Role[]> =>
    api.get(`/role?organization_id=${organization_id}`),
  // 获取当前机构所有角色列表
  getOrgAllRoles: (): Promise<Role[]> =>
    api.get(`/role`),

  // 获取所有角色列表-分页
  getAllRolesPaginated: (params: PaginationParams): Promise<BasePageModel<Role>> =>
    api.get('/role/paginated', {
      params: {
        page: params.page,
        size: params.size,
        keyword: params.keyword || ''
      }
    }),
  
  // 获取角色详情
  getRole: (role_id: string): Promise<Role> =>
    api.get(`/role/${role_id}`),
  
  // 创建新角色
  createRole: (roleData: CreateRoleForm): Promise<{success: boolean, data: Role}> =>
    api.post('/role', roleData),
  
  // 更新角色信息
  updateRole: (role_id: string, roleData: Partial<CreateRoleForm>): Promise<{success: boolean, data: Role}> =>
    api.put(`/role/${role_id}`, roleData),
  
  // 删除角色
  deleteRole: (role_id: string): Promise<{success: boolean, data: any}> =>
    api.delete(`/role/${role_id}`),

};

// 分配菜单权限-机构管理员-超级管理员
export const orgAdminApi = {
  // 获取当前机构所有菜单树数据结构-机构管理员
  getOrgMenuTree: (organization_id: string): Promise<MenuTreeNode[]> =>
    api.get(`/organization-menu/tree?organization_id=${organization_id}`),

  // 获取当前机构所有菜单平铺数据结构-机构管理员
  getOrgMenu: (organization_id: string): Promise<Menu[]> =>
    api.get(`/organization-menu?organization_id=${organization_id}`),
  
  // 获取当前角色的菜单平铺数据-产品导航菜单
  getRoleMenu: (role_id: string): Promise<Menu[]> =>
    api.get(`/role-menu?role_id=${role_id}`),

  // 机构管理员给机构用户分配菜单权限-提交接口
  OrgRoleMenus: (data: {organizationId: string, roleId: string, menu_ids: string[]}): Promise<{success: boolean, data: any}> =>
    api.post(`/role-menu`, {
      organization_id: data.organizationId,
      role_id: data.roleId,
      menu_ids: data.menu_ids
    }),

  // 超级管理员给机构分配菜单权限-提交接口
  SuperRoleMenus: (data: {organizationId: string, menu_ids: string[]}): Promise<{success: boolean, data: any}> =>
    api.post(`/organization-menu/batch`, {
      organization_id: data.organizationId,
      menu_ids: data.menu_ids
    }),
};

// 菜单管理
export const menuApi = {
  // 获取所有菜单树结构-超级管理员
  getMenuTree: (): Promise<MenuTreeNode[]> =>
    api.get('/menu/tree'),

  // 获取当前用户角色的菜单树结构 - 产品导航菜单
  // 是否平铺数据结构：is_flat: true-平铺，false-树结构
  getRoleMenuTree: (is_flat: boolean): Promise<MenuTreeNode[]> =>
    api.get(`/role-menu/tree?is_flat=${is_flat}`),

  // 获取菜单详情
  getMenu: (menu_id: string): Promise<Menu> =>
    api.get(`/menu/${menu_id}`),
  
  // 创建新菜单
  createMenu: (menuData: CreateMenuForm): Promise<{success: boolean, data: Menu}> =>
    api.post('/menu', menuData),
  
  // 更新菜单信息
  updateMenu: (menu_id: string, menuData: Partial<CreateMenuForm>): Promise<{success: boolean, data: Menu}> =>
    api.put(`/menu/${menu_id}`, menuData),
  
  // 删除菜单
  deleteMenu: (menu_id: string): Promise<{success: boolean, data: any}> =>
    api.delete(`/menu/${menu_id}`),
  
  // 缓存的角色菜单数据，用于快速检查权限
  _cachedRoleMenus: null as Menu[] | null,
  
  // 获取缓存的角色菜单数据
  getCachedRoleMenus: function(): Menu[] | null {
    return this._cachedRoleMenus;
  },
  
  // 设置缓存的角色菜单数据
  setCachedRoleMenus: function(menus: Menu[] | null): void {
    this._cachedRoleMenus = menus;
  }
};

// 历史报告相关API
export const historyReportApi = {
  // 下载论文大纲
  downloadOutline: (project_id: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}/project-reports/${project_id}/download-outline`;
    return downloadFile(
      url,
      filename,
      'application/octet-stream',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
  },

  // 下载论文
  downloadReport: (project_id: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}/project-reports/${project_id}/download-report`;
    return downloadFile(
      url,
      filename,
      'application/octet-stream',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
  },

};

// 模型 - 用户
export const modelUserApi = {
  // 获取用户的所有可用模型列表
  getUserAllModels: (): Promise<any> =>
    api.get('/users/model'),

  // 获取用户的默认模型
  getUserDefaultModel: (): Promise<any> =>
    api.get('/user-model'),

  // 修改用户的默认模型
  updateUserDefaultModel: (data: { list_model: {model_id: string; default_way: string}[] }): Promise<any> =>
    api.post(`/user-model`, data),

};

// 模型分配 - 机构
export const modelOrgApi = {
  // 获取机构的可用模型
  getOrgModels: (organization_id: string): Promise<any> =>
    api.get(`/organization-model/detail?organization_id=${organization_id}`),

  // 为机构批量添加模型
  arrangeOrgModels: (data: { organization_id: string; list_model: string[]; }): Promise<any> =>
    api.post(`/organization-model/arrange-model`, data),
};

// 模型管理相关API - 超级管理员
export const modelApi = {
  // 获取所有模型-分页
  getAllModelsPaginated: (params: PaginationParams): Promise<BasePageModel<ModelConfig>> =>
    api.get('/model-configs/list/paginated', {
      params: {
        page: params.page,
        size: params.size,
        keyword: params.keyword || ''
      }
    }),

  // 获取所有模型
  getAllModels: (): Promise<any> =>
    api.get('/model-configs/list'),

  // 获取单个模型
  getModel: (model_config_id: string): Promise<ModelConfig> =>
    api.get(`/model-configs/${model_config_id}`),
  
  // 添加新模型
  addModel: (modelData: any): Promise<any> =>
    api.post('/model-configs', modelData),
  
  // 更新模型
  updateModel: (model_config_id: string, modelData: any): Promise<any> =>
    api.put(`/model-configs/${model_config_id}`, modelData),
  
  // 删除模型
  deleteModel: (model_config_id: string): Promise<any> =>
    api.delete(`/model-configs/${model_config_id}`)
};

// 报告相关API
export const reportApi = {
  // 创建报告
  createReport: (data: CreateReportParams): Promise<Report> =>
    api.post('/reports/', data),

  // 获取报告详情
  getReportById: (id: string): Promise<Report> =>
    api.get(`/reports/${id}`),

  // 获取报告列表
  getReports: (): Promise<{ reports: Report[] }> =>
    api.get('/reports/'),

  // 删除报告
  deleteReport: (id: string): Promise<any> =>
    api.delete(`/reports/${id}`),

  // 流式获取报告内容
  streamReport: (id: string): string =>
    `/reports/${id}/stream`
};

export const adminReportApi = {
  // 获取报告列表
  getReports: (): Promise<{ reports: Report[] }> =>
    api.get('/reports/admin/'),

  // 删除报告
  deleteReport: (id: string): Promise<any> =>
    api.delete(`/reports/admin/${id}`),
};

// 研究相关API
export const researchApi = {
  // 获取研究列表
  getResearches: (): Promise<{ researches: any[] }> =>
    api.get('/research'),

  // 创建研究
  createResearch: (query: string): Promise<any> =>
    api.post('/research', { query: query.trim() }),

  // 删除研究
  deleteResearch: (id: string): Promise<any> =>
    api.delete(`/research/${id}`),

  // 获取研究详情
  getResearchById: (id: string): Promise<any> =>
    api.get(`/research/${id}`),

  // 启动研究流程
  startResearch: (id: string): Promise<any> =>
    api.post(`/research/${id}/start`),

};

// 机构管理相关API
export const organizationApi = {
  // 获取所有机构列表
  getAllOrganizations: (): Promise<Organization[]> =>
    api.get('/organizations'),

  // 获取所有机构列表-分页
  getAllOrganizationsPaginated: (params: PaginationParams): Promise<BasePageModel<Organization>> =>
    api.get('/organizations/paginated', {
      params: {
        page: params.page,
        size: params.size,
        keyword: params.keyword || ''
      }
    }),
  
  // 获取机构详情
  getOrganization: (orgId: string): Promise<Organization> =>
    api.get(`/organizations/${orgId}`),
  
  // 创建新机构
  createOrganization: (orgData: CreateOrganizationForm): Promise<any> =>
    api.post('/organizations', orgData),
  
  // 更新机构信息
  updateOrganization: (orgId: string, orgData: Partial<CreateOrganizationForm>): Promise<any> =>
    api.put(`/organizations/${orgId}`, orgData),
  
  // 删除机构
  deleteOrganization: (orgId: string): Promise<any> =>
    api.delete(`/organizations/${orgId}`),
};

export default api; 