import { AuthManager } from "./auth";

/** Dify流式连接状态枚举 */
export enum DifySSEStatus {
  CONNECTING = "connecting",
  OPEN = "open",
  CLOSED = "closed",
  ERROR = "error",
}

/** Dify消息发送请求参数 */
export interface DifyMessageRequest {
  /** 用户消息内容 */
  query: string;
  /** 用户标识符 */
  //   user: string;
  /** 会话ID，为空时自动创建新会话 */
  conversation_id?: string;
  /** 应用定义的变量输入 */
  inputs?: Record<string, any>;
  /** 响应模式，默认为streaming */
  response_mode?: "blocking" | "streaming";
  auto_generate_name?: boolean;
  /** 文件列表 */
  files?: Array<{
    type: string;
    transfer_method: string;
    url?: string;
    upload_file_id?: string;
  }>;
}

/** Dify流式事件数据 */
export interface DifyStreamEvent {
  /** 事件类型 */
  event: "message" | "message_end" | "message_replace" | "error" | "ping";
  /** 任务ID */
  task_id?: string;
  /** 消息ID */
  message_id?: string;
  /** 会话ID */
  conversation_id?: string;
  /** 回答内容 */
  answer?: string;
  /** 错误信息 */
  error?: string;
  /** 元数据 */
  metadata?: {
    usage?: {
      prompt_tokens?: number;
      completion_tokens?: number;
      total_tokens?: number;
      total_price?: string;
      currency?: string;
      latency?: number;
    };
    retriever_resources?: Array<{
      position: number;
      dataset_id: string;
      dataset_name: string;
      document_id: string;
      document_name: string;
      segment_id: string;
      score: number;
      content: string;
    }>;
  };
  /** 创建时间 */
  created_at?: number;
}

/** Dify流式数据接收回调选项 */
export interface DifySSECallbacks {
  /** 当收到消息片段时触发 */
  onMessage?: (chunk: string, fullContent: string, event: any) => void;
  /** 当流式传输完成时触发 */
  onComplete?: (finalContent: string, conversationId?: string, messageId?: string) => void;
  /** 当发生错误时触发 */
  onError?: (error: Error, errorEvent?: any) => void;
  /** 当流式传输开始时触发 */
  onStart?: () => void;
}

/** Dify流式连接控制器 */
export interface DifySSEController {
  /** 中止流式连接 */
  abort: () => void;
  /** 获取流式连接状态 */
  status: () => DifySSEStatus;
}

/**
 * 发送Dify流式消息
 *
 * 向Dify应用发送消息并接收流式响应数据
 *
 * @param url Dify API的完整URL (例如: https://api.dify.ai/v1/chat-messages)
 * @param messageData 消息请求参数
 * @param callbacks 回调函数集合
 * @returns 流控制器，用于中止连接和获取状态
 */
export function sendDifyMessage(
  url: string,
  messageData: DifyMessageRequest,
  callbacks: DifySSECallbacks = {},
): DifySSEController {
  // 状态变量
  let connectionStatus: DifySSEStatus = DifySSEStatus.CONNECTING;
  let fullContent = "";
  let conversationId: string | undefined;
  let messageId: string | undefined;

  // 创建AbortController用于取消请求
  const abortController = new AbortController();

  // 准备请求头
  const headers: Record<string, string> = {
    Authorization: `Bearer ${AuthManager.getToken()}`,
    "Content-Type": "application/json",
    Accept: "text/event-stream",
    "Cache-Control": "no-cache",
  };

  // 准备请求体
  const requestBody: DifyMessageRequest = {
    response_mode: "streaming",
    auto_generate_name: true,
    inputs: {
      additionalProp1: {},
    },
    ...messageData,
  };

  // 触发开始回调
  if (callbacks.onStart) {
    callbacks.onStart();
  }

  // 启动POST请求
  fetch(url, {
    method: "POST",
    headers,
    body: JSON.stringify(requestBody),
    signal: abortController.signal,
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }
      if (!response.body) {
        throw new Error("当前浏览器不支持 ReadableStream");
      }

      connectionStatus = DifySSEStatus.OPEN;

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";
      let bufferObj: Record<string, any>;

      // 递归读取流数据
      function readStream() {
        let hasError = false;
        reader
          .read()
          .then((result: any) => {
            if (result.done) {
              connectionStatus = DifySSEStatus.CLOSED;
              handleComplete();
              return;
            }
            // 解码数据
            buffer += decoder.decode(result.value, { stream: true });
            // 处理数据块 - 按行分割
            const lines = buffer.split("\n");
            buffer = lines.pop() || "";

            for (const line of lines) {
              const trimmedLine = line.trim();

              // 解析SSE数据格式
              if (trimmedLine.startsWith("data: ")) {
                // const eventDataStr = trimmedLine.substring(6);

                try {
                  bufferObj = JSON.parse(trimmedLine.substring(6)) as Record<string, any>; // remove data: and parse as json
                } catch {
                  console.warn("bufferObj parse error:", bufferObj);
                  return;
                }

                try {
                  // 处理不同事件类型
                  switch (bufferObj.event) {
                    case "message":
                      // 处理消息内容
                      if (bufferObj.answer) {
                        fullContent += bufferObj.answer;
                        if (callbacks.onMessage) {
                          callbacks.onMessage(bufferObj.answer, fullContent, bufferObj);
                        }
                      }
                      // 保存会话和消息ID
                      if (bufferObj.conversation_id) {
                        conversationId = bufferObj.conversation_id;
                      }
                      if (bufferObj.message_id) {
                        messageId = bufferObj.message_id;
                      }
                      break;

                    case "message_end":
                      // 消息结束
                      connectionStatus = DifySSEStatus.CLOSED;
                      handleComplete();
                      return;

                    case "message_replace":
                      // 消息替换（某些情况下dify会替换之前的内容）
                      if (bufferObj.answer !== undefined) {
                        fullContent = bufferObj.answer;
                        if (callbacks.onMessage) {
                          callbacks.onMessage(bufferObj.answer, fullContent, bufferObj);
                        }
                      }
                      break;

                    case "error":
                      // 错误处理
                      const errorMessage = bufferObj.error || "服务器返回错误";
                      handleError(new Error(errorMessage), bufferObj);
                      return;

                    default:
                      console.warn("未处理的事件类型:", bufferObj.event);
                  }
                } catch (error) {
                  console.error("解析Dify事件数据错误:", error, "原始数据:", bufferObj);
                }
              }
            }

            // 继续读取下一块数据
            if (!hasError) {
              readStream();
            }
          })
          .catch(error => {
            if (error.name !== "AbortError") {
              handleError(error);
            }
          });
      }

      // 开始读取
      readStream();
    })
    .catch(error => {
      // 只处理非中止请求导致的错误
      if (error.name !== "AbortError") {
        handleError(error);
      }
    });

  /**
   * 处理错误回调
   */
  function handleError(error: Error, errorEvent?: any) {
    connectionStatus = DifySSEStatus.ERROR;
    if (callbacks.onError) {
      callbacks.onError(error, errorEvent);
    }
    console.error("Dify流式数据获取错误:", error);
  }

  /**
   * 处理完成回调
   */
  function handleComplete() {
    if (callbacks.onComplete) {
      callbacks.onComplete(fullContent, conversationId, messageId);
    }
  }

  // 返回控制器对象
  return {
    abort: () => {
      abortController.abort();
      connectionStatus = DifySSEStatus.CLOSED;
    },
    status: () => connectionStatus,
  };
}
