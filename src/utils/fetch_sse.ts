import { AuthManager } from "./auth";
import { removeMarkdownH1AndText } from "../utils/index"

/** 流式连接状态枚举 */
export enum SSEStatus {
  CONNECTING = 'connecting',
  OPEN = 'open',
  CLOSED = 'closed',
  ERROR = 'error'
}

export enum StreamStatus {
  ERROR = 'error',
  NORMAL = 'normal',
  COMPLETED = 'completed',
  HEART_BEAT = 'heart_beat'
}

/** 流式事件类型接口 */
export interface StreamEvent {
  /** 内容片段 */
  content?: string
  /** 完成状态 */
  status?: StreamStatus;
  /** 总块数 */
  total_chunk?: number;
}

/**
 * 流式数据接收回调选项
 */
export interface SSECallbacks {
  /** 当收到消息片段时触发 */
  onMessage?: (chunk: string, fullContent: string) => void;
  /** 当流式传输完成时触发 */
  onComplete?: (finalContent: string) => void;
  /** 当发生错误时触发 */
  onError?: (error: Error) => void;
  /** 当流式传输开始时触发 */
  onStart?: () => void;
}

/**
 * 流式连接控制器
 */

export interface SSEController {
  /** 中止流式连接 */
  abort: () => void;
  /** 流式连接状态 */
  status: () => SSEStatus;
}

/**
 * 通用SSE流式数据获取函数
 * 
 * 用于从服务器获取流式响应数据，支持事件解析和回调处理
 * 
 * @param url 服务器URL
 * @param callbacks 回调函数集合
 * @param options 配置选项
 * @returns 流控制器，用于中止连接
 */
export function fetchSSE(
  url: string,
  callbacks: SSECallbacks = {},
  title?: string
): SSEController {
  // 状态变量
  let connectionStatus: SSEStatus = SSEStatus.CONNECTING;
  let fullContent = '';
  // 是否添加过头部了
  let is_add_title = false

  // 创建AbortController用于取消请求
  const abortController = new AbortController();

  // 准备请求头
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${AuthManager.getToken()}`,
    'Accept': 'text/event-stream',
    'Cache-Control': 'no-cache'
  };

  // 触发开始回调
  if (callbacks.onStart) {
    callbacks.onStart();
  }

  // 启动fetch请求
  fetch(url, {
    method: 'GET',
    headers,
    signal: abortController.signal,
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }
      if (!response.body) {
        throw new Error('当前浏览器不支持 ReadableStream。');
      }

      connectionStatus = SSEStatus.OPEN;

      const reader = response.body.getReader();
      if (!reader) {
        throw new Error('无法创建流式读取器');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      // 递归读取流数据
      function readStream() {
        // 额外安全检查
        if (!reader) {
          throw new Error('读取器不存在');
        }

        reader.read().then(({ done, value }) => {
          if (done) {
            connectionStatus = SSEStatus.CLOSED;
            handleComplete();
            return;
          }

          // 解码数据
          buffer += decoder.decode(value, { stream: true });

          // 处理数据块
          const chunks = buffer.split('\n\n');
          buffer = chunks.pop() || '';

          for (const chunk of chunks) {
            if (chunk.startsWith('data: ')) {
              try {
                const eventData = JSON.parse(chunk.substring(6)) as StreamEvent;
                // 处理状态
                if (eventData.status === StreamStatus.COMPLETED) {
                  connectionStatus = SSEStatus.CLOSED;
                  handleComplete();
                  abortController.abort();
                  return;
                } else if (eventData.status === StreamStatus.ERROR) {
                  handleError(new Error(eventData.content || '服务器返回错误状态'));
                  abortController.abort();
                  return;
                } else if (eventData.status === StreamStatus.NORMAL) {
                  // 处理内容
                  if (eventData.content) {
                    fullContent += eventData.content;
                    if (!is_add_title && title && fullContent.length > title?.length * 2 + 20) {
                      is_add_title = true
                      // alert("wuhu")
                      fullContent = `# ${title}\n\n${removeMarkdownH1AndText(fullContent, title)}`
                    }
                    if (callbacks.onMessage) {
                      callbacks.onMessage(eventData.content, fullContent);
                    }
                  }
                } else {
                  // heart_beat here
                }

              } catch (error) {
                console.error('解析事件数据错误:', error);
              }
            }
          }

          // 继续读取
          readStream();
        }).catch(error => {
          if (error.name !== 'AbortError') {
            handleError(error);
          }
        });
      }

      // 开始读取
      readStream();
    })
    .catch(error => {
      // 只处理非中止请求导致的错误
      if (error.name !== 'AbortError') {
        handleError(error);
      }
    });

  /**
   * 处理错误回调
   */
  function handleError(error: Error) {
    connectionStatus = SSEStatus.ERROR;
    if (callbacks.onError) {
      callbacks.onError(error);
    }

    console.error('流式数据获取错误:', error);
  }

  /**
   * 处理完成回调
   */
  function handleComplete() {
    if (callbacks.onComplete) {
      callbacks.onComplete(fullContent);
    }
  }


  // 返回控制器对象
  return {
    abort: () => {
      abortController.abort();
      connectionStatus = SSEStatus.CLOSED;
    },
    status: () => connectionStatus
  };
}
