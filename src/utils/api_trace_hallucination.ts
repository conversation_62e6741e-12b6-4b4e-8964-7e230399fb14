import { ProjectConfigCreate, ProjectLeader, ProjectMember } from '@/types/ReportConfig';
import api, { downloadFile } from './api';


// AI去痕
export const aiTraceApi = {
  // AI去痕上传
  uploadAiTraces: (file: any): Promise<any> =>
    api.post(`/ai-traces/traces`, file, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // AI去痕优化内容下载
  downloadAiTraces: (trace_id: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}/ai-traces/export-traces?trace_id=${trace_id}`;
    return downloadFile(
      url,
      filename,
      'application/octet-stream',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
  },

  // AI去痕优化内容下载PDF
  downloadAiTracesPdf: (trace_id: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}/ai-traces/export-traces-pdf?trace_id=${trace_id}`;
    return downloadFile(
      url,
      filename,
      'application/pdf',
      'application/pdf'
    );
  }

};

// 幻觉审查
export const hallucinationApi = {
  // 幻觉审查上传
  uploadPaper: (file: any): Promise<any> =>
    api.post(`/verify/upload-paper`, file, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),
  
  // 创建幻觉审查记录
  createVerify: (data: any): Promise<any> =>
    api.post(`/verify`, data),

  // 获取幻觉审查结果
  getVerify: (verify_id: string): Promise<any> =>
    api.get(`/verify/${verify_id}`),

  // 幻觉审查内容下载
  downloadVerify: (id: string, flag : string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}/verify/${id}/download?flag=${flag}`; // pdf/doc
    return downloadFile(
      url,
      filename,
      flag === 'pdf' ? 'application/pdf' : 'application/octet-stream',
      flag === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
  },

  // 获取幻觉审查记录列表
  getVerifyListPaginated: (page: number = 1, size: number = 10, keyword: string = ''): Promise<any> =>
    api.get(`/verify/list/paginated`, {
      params: { page, size, keyword }
    }),

  // 删除幻觉审查记录
  deleteVerify: (verify_id: string): Promise<any> =>
    api.delete(`/verify/${verify_id}`),

}

export default {
  aiTraceApi,
  hallucinationApi,
};

