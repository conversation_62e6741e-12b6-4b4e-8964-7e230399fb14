// 主题配置，可以通过环境变量自定义
export const theme = {
  primaryColor: window.AppConfig.primaryColor || '#2A5CAA',
  secondaryColor: '#9f8bff',
  textColor: '#333',
  textSecondary: '#666',
  textLight: '#999',
  avatarBackground: '#E6EEFF',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
  buttonShadow: '0 4px 12px rgba(123, 97, 255, 0.2)',
  gradient: 'linear-gradient(145deg, #f5f7ff 0%, #ffffff 100%)',
  primaryGradient: 'linear-gradient(135deg, #7B61FF 0%, #9f8bff 100%)',
  fontTitle: "'PingFang SC', '微软雅黑', 'Microsoft YaHei', '苹方', '黑体', -apple-system, BlinkMacSystemFont, sans-serif",
  fontBody: "'PingFang SC', '微软雅黑', 'Microsoft YaHei', '宋体', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
}

// Ant Design主题变量覆盖
export const antTheme = {
  token: {
    colorPrimary: theme.primaryColor,
    borderRadius: 8,
    fontSize: 14,
    boxShadow: theme.boxShadow,
  },
  components: {
    Button: {
      primaryShadow: '0 6px 16px -3px rgba(123, 97, 255, 0.35)'
    },
    Card: {
      boxShadow: theme.boxShadow
    },
    Input: {
      activeBorderColor: theme.primaryColor
    }
  }
} 