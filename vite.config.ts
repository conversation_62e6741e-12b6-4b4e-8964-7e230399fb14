import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'url'
import { defineConfig, loadEnv } from 'vite'
import { createHtmlPlugin } from 'vite-plugin-html';

const __dirname = fileURLToPath(new URL('.', import.meta.url))

// https://vitejs.dev/config/
export default defineConfig(({mode}) => {
  const env = loadEnv(mode, process.cwd(), '')
  console.log(env.VITE_API_BASE_URL)
  return {
    base: '/college',
    plugins: [react(),
      {
        name: 'markdown-loader',
        transform(code, id) {
          if (id.endsWith('.md?raw')) {
            return `export default ${JSON.stringify(code)};`;
          }
        }
      },
      createHtmlPlugin({
        inject: {
          data: {
            VITE_APP_VERSION: process.env.VITE_APP_VERSION || 'dev'
          }
        }
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
      },
    },
    build: {
      minify: false,
      sourcemap: true,
      rollupOptions: {
        treeshake: false
      }
    },
    server: {
      port: 3000,
      // 配置代理，解决开发环境跨域问题
      proxy: {
        '/api': {
          target: env.VITE_APP_PROXY_API_URL || 'https://dev-companion.taihealth.cn/',
          // target: 'https://test-hi-researcher.taihealth.cn/',
          // target: 'http://192.168.0.120:8000/',
          changeOrigin: true,
          // 不重写路径
          rewrite: (path) => path,
          /** @ts-ignore */
          logLevel: 'debug'
        }
      }
    },
    assetsInclude: ['**/*.md'], // 将 .md 文件视为资源文件
  }}
)