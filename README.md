# College Agent 前端

高校AI助手系统的前端部分，基于React和Vite开发。

## 技术栈

- React 18
- TypeScript
- Vite
- Ant Design
- React Router
- Axios
- React Markdown

## 特性

- 现代化UI设计，柔和色彩搭配
- 高校AI助手系统
- 智能学习伙伴
- 响应式布局，适配不同设备
- 统一封装的API请求模块

## 项目结构

```
src/
├── components/      # 公共组件
├── pages/           # 页面组件
├── contexts/        # 上下文管理
├── hooks/           # 自定义钩子
├── styles/          # 样式文件
├── config/          # 配置文件
├── types/           # 类型定义
├── utils/           # 工具函数
│   └── api.ts       # API请求封装
├── App.tsx          # 主应用组件
├── main.tsx         # 入口文件
└── routes.tsx       # 路由配置
```

## 快速开始

### 安装依赖

```bash
# 使用npm
npm install
```

### 开发模式

```bash
# 启动开发服务器
npm run dev
```

访问 <http://localhost:3000> 查看应用。

### 构建生产版本

```bash
# 构建生产版本
./build.sh 1.0.0
```

## 主要页面

- **首页 (Home)**：系统欢迎页面
- **作业助手 (Homework)**：理科作业智能解答系统，支持高数、物理、化学等科目
- **智能聊天 (Chat)**：智能对话助手
- **论文生成 (Paper)**：论文写作与配置系统
- **AI去痕工具 (AiTraces)**：文档AI痕迹优化工具

## API封装

项目使用统一的API请求模块，位于`src/utils/api.ts`。所有API请求都通过此模块进行封装，具有以下特点：

- 集中管理所有接口请求
- 统一的错误处理和请求拦截
- 自动附加API Key到请求头
- API Key过期时自动提示用户
- 按业务模块分组API方法
  - `reportApi`: 报告相关接口
  - `researchApi`: 研究相关接口

## 脚本工具

### OpenAPI文档转换工具

项目提供了自动化工具，用于将OpenAPI规范转换为更友好的Markdown文档：

- **脚本路径**：`scripts/parse_api_to_llm_friendly.py`
- **功能**：将OpenAPI规范（JSON格式）转换为结构化Markdown文档，便于开发者和大语言模型（LLM）理解API
- **使用方法**：

  ```bash
  python scripts/parse_api_to_llm_friendly.py
  ```

- **说明**：
  - 默认从`http://**********:7999/openapi.json`获取API规范
  - 生成的文档保存为`接口文档汇总.md`
  - 包含API路径、方法、参数、响应格式等详细信息
  - 所有接口文档带有中文注释

## 自定义配置

项目支持通过环境变量进行配置：

- `primaryColor` - 主题色，默认为`#2A5CAA`

## 配置说明

### 环境变量

```bash
# 应用版本号
VITE_APP_VERSION=1.0.0
```

### 编译

```bash
./build.sh 1.0.0
```

### docker 构建

```bash
docker build --platform linux/amd64 -f Dockerfile_${configuration.getName()} -t registry.cn-shanghai.aliyuncs.com/chos/college-agent-frontend:${build.version} .
```

### docker push

```bash
docker push registry.cn-shanghai.aliyuncs.com/chos/college-agent-frontend:${build.version}
```

### 运行

```bash
docker run --restart always -d -p 7051:7051 -e TZ=Asia/Shanghai --name college-agent-frontend registry.cn-shanghai.aliyuncs.com/chos/college-agent-frontend:${build.version}
```

### 注意

如果npm install报错：
npm error code ERESOLVE
npm error ERESOLVE could not resolve
npm error
npm error While resolving: @toast-ui/react-editor@3.2.3
npm error Found: react@18.3.1
需要使用兼容性参数安装，执行：

```bash
   npm i --legacy-peer-deps
```
