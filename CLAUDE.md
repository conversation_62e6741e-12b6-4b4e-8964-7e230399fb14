# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Start Development Server
```bash
npm run dev
```
Starts the Vite development server. Access at http://localhost:3000.

### Build Project
```bash
npm run build
```
Runs TypeScript compilation (`tsc`) followed by Vite build.

### Production Build with Version
```bash
./build.sh 1.0.0
```
Custom build script that sets version and creates production build.

### Preview Build
```bash
npm run preview
```
Preview the production build locally.

### Package Installation
If encountering npm dependency resolution errors with `@toast-ui/react-editor`, use:
```bash
npm i --legacy-peer-deps
```

## Architecture Overview

### Technology Stack
- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite 4
- **UI Framework**: Ant Design 5
- **Routing**: React Router 6
- **HTTP Client**: Axios with custom interceptors
- **Editor**: Toast UI Editor with Markdown support
- **Charts**: ECharts integration

### Core Application Structure

The application follows a context-driven architecture with:
- **AuthProvider**: Handles authentication state and token management
- **PermissionProvider**: Manages user permissions and role-based access
- **Navigator**: Main routing component with protection mechanisms

### API Architecture

All API requests flow through a unified system in `src/utils/api.ts`:

#### Request Flow
1. **Request Interceptor**: Automatically injects Bearer tokens
2. **Response Interceptor**: Standardizes response format and handles errors
3. **Error Handling**: 401 responses trigger automatic logout and redirect

#### API Response Format
```typescript
interface ApiResponse<T = any> {
    code: number;       // 0 = success, others = error
    data: T;            // Business data
    error: string;      // Error message
    success: boolean;   // Success flag
}
```

#### API Organization
APIs are grouped by business domain:
- `authApi`: Authentication and user management
- `reportApi`: Report creation and management
- `organizationApi`: Organization/institution management
- `roleApi`: Role and permission management
- `menuApi`: Menu and navigation management
- `modelApi`: AI model configuration

### Route Structure

Routes are organized in `src/routes.tsx` with protection:
- **Main Routes**: `/college/*` (protected by permissions)
- **Admin Routes**: `/background/*` (admin interface)
- **Public Routes**: `/login`, `/auth/redirect`

All business routes use `withPermission()` wrapper for role-based access control.

### Component Architecture

#### Page Components (`src/pages/`)
- **Home**: Welcome page and dashboard
- **Homework**: Math/science homework assistant with subject support
- **Chat**: AI conversation interface
- **Paper**: Academic paper generation with workflow
- **AiTraces**: AI content optimization tools
- **Background**: Admin management interface

#### Shared Components (`src/components/`)
- **MarkdownEditor**: Rich text editing with math support
- **MarkdownViewer**: Markdown rendering with syntax highlighting
- **FileUploadDragger**: File upload interface
- **ProtectedRoute**: Route protection wrapper
- **Navigator**: Main navigation and layout

### State Management

#### Context Providers
- **AuthContext**: User authentication state, token management
- **PermissionContext**: Role-based permissions and menu access

#### Authentication Flow
1. Login generates JWT token
2. Token stored via `AuthManager`
3. Automatic injection in API requests
4. 401 responses trigger logout and redirect

### Configuration System

#### Environment Variables
- `VITE_APP_VERSION`: Application version
- Configuration loaded from `public/config.js`

#### Theme Configuration
- Primary color: `#2A5CAA`
- Ant Design theme customization in `src/config/theme.ts`

### Development Guidelines

#### API Development
- All API calls must use the centralized `api.ts` module
- Never use axios directly in components
- API methods should be grouped by business domain
- Include TypeScript types for all request/response data

#### Component Development
- Use functional components with TypeScript props
- Business pages go in `src/pages/`
- Reusable components go in `src/components/`
- Include Chinese comments for complex business logic
- Follow existing naming and structure patterns

#### Error Handling
- API errors are automatically displayed via Ant Design messages
- 401 errors trigger automatic logout
- Network errors include user-friendly messages
- Detailed error logging for debugging

### Deployment

#### Docker Build
```bash
docker build --platform linux/amd64 -f Dockerfile -t registry.cn-shanghai.aliyuncs.com/chos/college-agent-frontend:1.0.0 .
```

#### Docker Run
```bash
docker run --restart always -d -p 7051:7051 -e TZ=Asia/Shanghai --name college-agent-frontend registry.cn-shanghai.aliyuncs.com/chos/college-agent-frontend:1.0.0
```

### File Upload Integration

The application includes `FileUploadDragger` component for handling file uploads with:
- Drag and drop support
- File type validation
- Upload progress tracking
- Integration with backend APIs

### Markdown and Math Support

- **MarkdownEditor**: Toast UI Editor with math plugin
- **MarkdownViewer**: React Markdown with KaTeX support
- **Math Rendering**: KaTeX integration for mathematical expressions
- **Syntax Highlighting**: Code block highlighting via rehype-highlight