{"name": "college-agent-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "cross-env VITE_APP_VERSION=1.0-local && vite", "start": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@toast-ui/editor": "^3.2.2", "@toast-ui/react-editor": "^3.2.3", "@xyflow/react": "^12.6.4", "antd": "^5.9.0", "axios": "^1.5.0", "dayjs": "^1.11.9", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "github-markdown-css": "^5.8.1", "js-base64": "^3.7.7", "katex": "^0.16.22", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-masonry-css": "^1.0.16", "react-router-dom": "^6.15.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sm-crypto": "^0.3.13", "tui.editor.supsup_plugin": "github:ahamelers/tui.editor.subsup_plugin", "vite-plugin-html": "^3.2.2"}, "overrides": {"@toast-ui/react-editor": {"react": "^18.2.0"}}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/node": "^22.13.14", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.0.3", "cross-env": "^7.0.3", "prettier": "^3.5.3", "typescript": "^5.8.2", "vite": "^4.5.10"}}