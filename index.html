<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>College-Agent - 大学生智能学习助手</title>
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <meta name="description" content="基于人工智能的大学生智能学习助手">
    <!-- 引入配置文件，通过版本号参数避免缓存 -->
    <script type="text/javascript" src="/config.js?v=<%= VITE_APP_VERSION %>"></script>
    <!-- 设置页面标题 -->
    <script type="text/javascript">
      document.addEventListener('DOMContentLoaded', function() {
        if (window.AppConfig && window.AppConfig.appTitle) {
          document.title = window.AppConfig.appTitle;
          console.log('页面标题已设置为：', window.AppConfig.appTitle);
        } else {
          console.warn('未找到appTitle配置，页面标题未设置');
        }
      });
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      console.log('VITE_APP_VERSION:', '<%= VITE_APP_VERSION %>');
    </script>
  </body>
</html> 